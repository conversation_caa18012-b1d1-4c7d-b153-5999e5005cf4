# SharePoint Links

This folder contains Windows shortcuts to files synced from SharePoint via OneDrive.

## How It Works

**Portable Design:**
- `index.json` - Shared catalog of files (committed to Git)
- `config.json` - Your local OneDrive path (gitignored)
- `*.lnk` files - Shortcuts generated from index + config

**Works across different machines!** Each user has their own OneDrive path in config.json.

## Usage

**Click any .lnk file** to open the file from OneDrive.

## Updating

```powershell
cd System\scripts

# Update index from OneDrive + create shortcuts
.\Sync-SharePointIndex.ps1

# Only update index
.\Sync-SharePointIndex.ps1 -UpdateIndex

# Only create shortcuts
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

## Last Updated

Index generated: 2025-10-20 11:20:42
By: jrazza
Total files: 4564
Shortcuts created: 4471

# Scripts and Tools Registry - ReidCEO System

**Last Updated**: 2025-10-28
**Purpose**: Comprehensive inventory of all helper scripts, automation tools, and utilities
**Status**: Active - Single source of truth for all scripts

---

## Overview

This registry documents **all helper scripts and automation tools** in the ReidCEO management system.

**Total Scripts**: 36+ files (21 PowerShell + 15 Python/Support)

**Script Categories**:
1. **Email System** (4 scripts) - Send emails, templates, automation
2. **SharePoint Integration** (9 scripts) - Sync, index, and search SharePoint content
3. **Search Systems** (5 scripts) - Keyword, semantic, and image search
4. **Setup & Configuration** (4 scripts) - System setup wizards
5. **Image Processing** (3 scripts) - AI vision analysis and content extraction
6. **Development Tools** (3 scripts) - Cygwin setup, testing utilities
7. **Support Files** (8 files) - Python modules, logs, configs

---

## Quick Reference by Task

| What You Want to Do | Script to Use | Location |
|---------------------|---------------|----------|
| **Send email** | `New-Email.ps1` | `System/scripts/` |
| **Send templated email** | `Send-TemplatedEmail.ps1` | `System/scripts/` |
| **Email daily briefing** | `Email-DailyBriefing.ps1` | `System/scripts/` |
| **Email action items** | `Email-ActionItems.ps1` | `System/scripts/` |
| **Sync SharePoint content** | `Sync-All.ps1` | `System/scripts/` |
| **Update SharePoint index** | `Sync-SharePointIndex.ps1` | `System/scripts/` |
| **Search SharePoint (keywords)** | `Search-SharePointContent.ps1` | `System/scripts/` |
| **Search SharePoint (semantic)** | `Search-SemanticContent.ps1` | `System/scripts/` |
| **Search images** | `Search-SharePointContent.ps1 -FileType image` | `System/scripts/` |
| **Set up Confluence** | `Setup-ConfluenceMCP.ps1` | `System/scripts/` |
| **Set up Serena AI** | `Setup-Serena-QuickStart.ps1` | `System/scripts/` |
| **Extract SharePoint files** | `Extract-SharePointContent.ps1` | `System/scripts/` |
| **Analyze images with AI** | `Analyze-Images-AI.ps1` | `System/scripts/` |
| **Create SharePoint shortcuts** | `Create-SharePointShortcuts.ps1` | `System/scripts/` |

---

## Category 1: Email System

### 1. `New-Email.ps1`
**Type**: PowerShell Email Sender (HTML & Plain Text)
**Purpose**: Send emails using Outlook COM automation
**Status**: ✅ Production - NEW

**What It Does**:
- Send emails from your Outlook account
- Draft mode (review before sending) - Default and safe
- Immediate send mode (automated emails)
- Support for attachments, CC/BCC, HTML, priorities

**Usage**:
```powershell
# Open draft for review (SAFE)
.\New-Email.ps1 -To "<EMAIL>" -Subject "Question" -Body "Message"

# Send immediately (automated)
.\New-Email.ps1 -To "<EMAIL>" -Subject "Alert" -Body "Message" -Send

# With attachment
.\New-Email.ps1 -To "<EMAIL>" -Subject "Report" -Body "See attached" -Attachments "report.pdf"

# HTML email (professional formatting)
$htmlBody = Get-Content "email.html" -Raw
.\New-Email.ps1 -To "<EMAIL>" -Subject "Update" -Body $htmlBody -BodyFormat HTML
```

**Parameters**:
- `-To` (required): Recipient email(s)
- `-Subject` (required): Email subject
- `-Body` (required): Email body
- `-Cc`, `-Bcc`: Additional recipients
- `-Attachments`: File(s) to attach
- `-Importance`: Normal, High, Low
- `-BodyFormat`: Text or HTML
- `-Send`: Send immediately (default: draft mode)
- `-SaveToDrafts`: Save to Drafts folder
- `-Silent`: Suppress output messages

**Dependencies**:
- Outlook desktop application installed and configured

**Process Docs**: EMAIL-SYSTEM-GUIDE.md

---

### 2. `Send-TemplatedEmail.ps1`
**Type**: PowerShell Template Processor
**Purpose**: Process email templates with variable substitution
**Status**: ✅ Production - NEW

**What It Does**:
- Loads email template from `System/EmailTemplates/`
- Replaces `{{variables}}` with actual values
- Calls New-Email.ps1 to send

**Usage**:
```powershell
.\Send-TemplatedEmail.ps1 -Template "action-item-assigned" -Variables @{
    assignee_name = "John Smith"
    assignee_email = "<EMAIL>"
    task_title = "Review Budget"
    priority = "High"
    deadline = "Friday"
}
```

**Dependencies**:
- New-Email.ps1
- Email templates in `System/EmailTemplates/` (supports FORMAT: HTML header)

**Process Docs**: EMAIL-SYSTEM-GUIDE.md

---

### 3. `Email-DailyBriefing.ps1`
**Type**: PowerShell Automation Script
**Purpose**: Automated daily briefing email
**Status**: ✅ Production - NEW

**What It Does**:
- Reads `CEO-Dashboard/daily-briefing.md`
- Extracts sections (KPIs, urgent items, priorities, alerts)
- Generates formatted email using template
- Opens draft for review (or sends if `-Send` flag used)

**Usage**:
```powershell
# Open draft
.\Email-DailyBriefing.ps1

# Send to specific address
.\Email-DailyBriefing.ps1 -To "<EMAIL>" -Send
```

**Dependencies**:
- CEO-Dashboard/daily-briefing.md must exist
- Send-TemplatedEmail.ps1
- EmailTemplates/daily-briefing-html.txt (HTML format with table conversion)

**Automation**: Can be scheduled via Windows Task Scheduler for daily 7am email

**Process Docs**: EMAIL-SYSTEM-GUIDE.md

---

### 4. `Email-ActionItems.ps1`
**Type**: PowerShell Automation Script
**Purpose**: Send action item assignment notifications
**Status**: ✅ Production - NEW

**What It Does**:
- Reads action item markdown file
- Extracts task details (title, description, context)
- Generates formatted email to assignee
- Opens draft for review

**Usage**:
```powershell
.\Email-ActionItems.ps1 `
    -ActionItemFile "CEO-Dashboard/action-items/task.md" `
    -AssigneeName "John Smith" `
    -AssigneeEmail "<EMAIL>" `
    -Priority "High" `
    -Deadline "Friday"
```

**Dependencies**:
- Send-TemplatedEmail.ps1
- EmailTemplates/action-item-assigned-html.txt (HTML format with professional styling)

**Process Docs**: EMAIL-SYSTEM-GUIDE.md

---

## Category 2: SharePoint Integration Scripts

### 1. `Sync-All.ps1`
**Type**: PowerShell Master Orchestrator
**Purpose**: Complete SharePoint sync workflow (master script that calls others)
**Status**: ✅ Production - Daily use

**What It Does**:
- Updates SharePoint index
- Extracts content from new files
- Rebuilds keyword index
- Rebuilds semantic embeddings
- Orchestrates entire sync process

**Usage**:
```powershell
.\Sync-All.ps1                    # Full sync
.\Sync-All.ps1 -IncrementalOnly   # Only new/changed files
.\Sync-All.ps1 -Department Marketing  # Single department
```

**Dependencies**:
- Sync-SharePointIndex.ps1
- Extract-SharePointContent.ps1
- Index-SharePointContent.ps1
- Generate-SemanticIndex.ps1
- Python (for embeddings)

**Calls**: Multiple other scripts in sequence

**Output**: Logs to `sync-log.txt` (gitignored)

**Process Docs**:
- SEARCH-SYSTEMS-GUIDE.md
- System/scripts/README.md

---

### 2. `Sync-SharePointIndex.ps1`
**Type**: PowerShell Indexer
**Purpose**: Scans OneDrive and creates portable SharePoint file index
**Status**: ✅ Production - Daily use

**What It Does**:
- Scans OneDrive SharePoint folder
- Creates portable index.json (committed to Git)
- Generates Windows shortcuts (.lnk files)
- Works across different machines

**Usage**:
```powershell
.\Sync-SharePointIndex.ps1              # Update index + create shortcuts
.\Sync-SharePointIndex.ps1 -UpdateIndex # Only update index
```

**Output Files**:
- `SharePointLinks/index.json` (committed - portable)
- `SharePointLinks/config.json` (gitignored - machine-specific)
- `SharePointLinks/**/*.lnk` (gitignored - shortcuts)

**Dependencies**:
- OneDrive sync must be running
- Access to `C:\Users\<USER>\OneDrive - Gain Servicing\`

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 3. `Extract-SharePointContent.ps1`
**Type**: PowerShell Content Extractor
**Purpose**: Extract PDF/DOCX/XLSX files to markdown format
**Status**: ✅ Production

**What It Does**:
- Extracts text from PDF files
- Converts DOCX to markdown
- Extracts XLSX to structured markdown tables
- Saves to `docs/sharepoint-content/`

**Usage**:
```powershell
.\Extract-SharePointContent.ps1              # Extract all
.\Extract-SharePointContent.ps1 -Department Marketing  # Single department
```

**Output**: Markdown files in `docs/sharepoint-content/` (gitignored)

**Dependencies**:
- `extract_content.py` (Python module)
- Python packages: pypdf, python-docx, openpyxl

**Log File**: `extract-log.txt` (gitignored)

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 4. `Create-SharePointShortcuts.ps1`
**Type**: PowerShell Utility
**Purpose**: Generate Windows shortcuts to SharePoint files
**Status**: ✅ Production

**What It Does**:
- Creates .lnk shortcut files
- Points to OneDrive-synced SharePoint files
- Organized by department in `SharePointLinks/`

**Usage**:
```powershell
.\Create-SharePointShortcuts.ps1
```

**Output**: `SharePointLinks/**/*.lnk` (gitignored)

**Dependencies**:
- SharePoint index.json
- WScript.Shell COM object (Windows only)

**Log File**: `shortcuts-log.txt` (gitignored)

**Process Docs**: System/scripts/README.md

---

### 5. `Setup-SharePointLinks.ps1`
**Type**: PowerShell Setup Script
**Purpose**: Initial setup for SharePoint shortcuts system
**Status**: ✅ Production

**What It Does**:
- Creates SharePointLinks folder structure
- Generates initial config.json
- Sets up first-time shortcuts

**Usage**:
```powershell
.\Setup-SharePointLinks.ps1
```

**Run**: One-time setup, or when resetting shortcuts

**Dependencies**: OneDrive sync completed

**Process Docs**: System/scripts/README.md

---

### 6. `Index-SharePointContent.ps1`
**Type**: PowerShell Indexer
**Purpose**: Build keyword search index from extracted content
**Status**: ✅ Production

**What It Does**:
- Scans extracted markdown files
- Creates inverted index for fast keyword search
- Saves to `docs/sharepoint-content/search-index.json`

**Usage**:
```powershell
.\Index-SharePointContent.ps1           # Incremental update
.\Index-SharePointContent.ps1 -Rebuild  # Full rebuild
```

**Output**: `docs/sharepoint-content/search-index.json` (gitignored)

**Dependencies**:
- Extracted content in `docs/sharepoint-content/`

**Log File**: `index-log.txt` (gitignored)

**Used By**: `Search-SharePointContent.ps1`

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 7. `Generate-SemanticIndex.ps1`
**Type**: PowerShell + Python Orchestrator
**Purpose**: Create AI embeddings for semantic search
**Status**: ✅ Production - Optional (AI-powered)

**What It Does**:
- Calls Python script to generate sentence embeddings
- Creates vector index for similarity search
- Enables "find similar concepts" capability

**Usage**:
```powershell
.\Generate-SemanticIndex.ps1           # Incremental
.\Generate-SemanticIndex.ps1 -Rebuild  # Full rebuild
```

**Output**: `docs/sharepoint-content/semantic-embeddings.json` (gitignored)

**Dependencies**:
- `generate_embeddings.py`
- Python packages: sentence-transformers, torch, numpy

**Log File**: `semantic-log.txt` (gitignored)

**Used By**: `Search-SemanticContent.ps1`

**Cost**: Free (local model), ~500MB model download first time

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 8. `Extract-ImageContent.ps1`
**Type**: PowerShell Image Processor
**Purpose**: Extract metadata and preview info from images
**Status**: ✅ Production

**What It Does**:
- Extracts EXIF metadata from images
- Identifies image type (chart, diagram, photo)
- Prepares images for AI analysis

**Usage**:
```powershell
.\Extract-ImageContent.ps1
```

**Output**: Image metadata in search index

**Dependencies**:
- `extract_image_metadata.py`
- Python packages: Pillow

**Log File**: `extract-image-log.txt` (gitignored)

**Process Docs**: IMAGE-SEARCH-GUIDE.md

---

### 9. `Analyze-Images-AI.ps1`
**Type**: PowerShell + AI Vision
**Purpose**: Use Claude AI to analyze image content
**Status**: ✅ Production - Optional (requires API key)

**What It Does**:
- Sends images to Claude Vision API
- Extracts text, charts, diagrams descriptions
- Makes images searchable by content

**Usage**:
```powershell
# Set API key first
$env:ANTHROPIC_API_KEY = "sk-ant-..."
.\Analyze-Images-AI.ps1
```

**Output**: Image descriptions in search index

**Dependencies**:
- `analyze_image.py`
- Python packages: anthropic, Pillow
- **Anthropic API key** (costs ~$0.05-0.10 per 100 images)

**Log File**: `extract-image-log.txt` (gitignored)

**Used By**: `Search-SharePointContent.ps1 -FileType image`

**Cost**: ~$0.001 per image (Claude 3.5 Sonnet vision)

**Process Docs**: IMAGE-SEARCH-GUIDE.md

---

## Category 2: Search Systems

### 10. `Search-SharePointContent.ps1`
**Type**: PowerShell Search Tool
**Purpose**: Keyword search across extracted SharePoint content
**Status**: ✅ Production - Primary search tool

**What It Does**:
- Full-text keyword search
- File type filtering
- Department filtering
- Image search (if images analyzed)
- Shows context snippets

**Usage**:
```powershell
.\Search-SharePointContent.ps1 -Query "customer acquisition"
.\Search-SharePointContent.ps1 -Query "budget" -Department Finance
.\Search-SharePointContent.ps1 -Query "chart" -FileType image
```

**Parameters**:
- `-Query` (required): Search keywords
- `-Department`: Filter by department (Marketing, Finance, etc.)
- `-FileType`: Filter by type (pdf, docx, xlsx, image)
- `-ShowContext`: Show surrounding text
- `-Top`: Limit results (default 10)

**Dependencies**:
- `Index-SharePointContent.ps1` (must run first)
- search-index.json

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 11. `Search-SemanticContent.ps1`
**Type**: PowerShell + AI Semantic Search
**Purpose**: Conceptual similarity search (finds related ideas)
**Status**: ✅ Production - Optional (AI-powered)

**What It Does**:
- Converts query to vector embedding
- Finds semantically similar content
- "Customer retention" finds "loyalty programs", "satisfaction metrics"

**Usage**:
```powershell
.\Search-SemanticContent.ps1 -Query "lead generation tactics"
.\Search-SemanticContent.ps1 -Query "client retention" -Department Marketing -Top 5
```

**Parameters**:
- `-Query` (required): Conceptual query
- `-Department`: Filter by department
- `-Top`: Number of results (default 10)
- `-Threshold`: Similarity threshold (0-1, default 0.5)

**Dependencies**:
- `Generate-SemanticIndex.ps1` (must run first)
- `semantic_search.py`
- Python packages: sentence-transformers

**Process Docs**: SEARCH-SYSTEMS-GUIDE.md

---

### 12. `Search-SharePointFiles.ps1`
**Type**: PowerShell File Finder
**Purpose**: Search SharePoint file index by filename
**Status**: ✅ Production

**What It Does**:
- Searches index.json for file names
- Pattern matching on file paths
- Quick file location lookup

**Usage**:
```powershell
.\Search-SharePointFiles.ps1 -Pattern "budget"
.\Search-SharePointFiles.ps1 -Pattern "*.pptx" -Department Marketing
```

**Parameters**:
- `-Pattern`: Filename pattern (supports wildcards)
- `-Department`: Filter by department

**Dependencies**: SharePointLinks/index.json

**Process Docs**: System/scripts/README.md

---

## Category 3: Setup & Configuration Scripts

### 13. `Setup-ConfluenceMCP.ps1`
**Type**: PowerShell Setup Wizard
**Purpose**: Interactive Confluence integration setup
**Status**: ✅ Production

**What It Does**:
- Collects Confluence credentials (URL, email, API token)
- Creates `confluence-credentials.json` (gitignored)
- Updates Claude Code MCP server configuration
- Tests connection to Confluence

**Usage**:
```powershell
.\Setup-ConfluenceMCP.ps1
```

**Output Files**:
- `confluence-credentials.json` (gitignored, shareable within team)
- Updates Claude Code config: `claude_desktop_config.json`

**Dependencies**:
- Python 3.8+
- pip packages: mcp-atlassian, pydantic==2.11.0

**Team Sharing**: Credentials file can be shared within Gain Servicing team

**Process Docs**:
- CONFLUENCE-MCP-SETUP-GUIDE.md
- CONFLUENCE-QUICK-SETUP.md

---

### 14. `Setup-Serena-QuickStart.ps1`
**Type**: PowerShell Setup Wizard
**Purpose**: Install and configure Serena AI assistant
**Status**: ✅ Production

**What It Does**:
- Installs Serena MCP server
- Configures Claude Code integration
- Sets up code intelligence tools
- Creates initial project index

**Usage**:
```powershell
.\Setup-Serena-QuickStart.ps1
```

**Output**:
- Updates Claude Code MCP configuration
- Creates `.serena/` folder with cache

**Dependencies**:
- Node.js 18+
- npm or npx

**Process Docs**: SERENA-AI-ASSISTANT-GUIDE.md

---

### 15. `setup-serena.ps1`
**Type**: PowerShell Setup Script (Alternative)
**Purpose**: Manual Serena installation (advanced users)
**Status**: ✅ Production - Alternative to QuickStart

**What It Does**:
- Direct Serena MCP installation
- More control over configuration
- For advanced users or troubleshooting

**Usage**:
```powershell
.\setup-serena.ps1
```

**When to Use**: When QuickStart doesn't work or need custom config

**Process Docs**: SERENA-AI-ASSISTANT-GUIDE.md

---

### 16. `Install-Cygwin.ps1`
**Type**: PowerShell System Setup
**Purpose**: Install Cygwin for Unix-like environment on Windows
**Status**: ⚠️ Development/Optional

**What It Does**:
- Downloads Cygwin installer
- Installs common Unix utilities
- Sets up PATH

**Usage**:
```powershell
# Run as Administrator
.\Install-Cygwin.ps1
```

**When to Use**: When needing Unix commands on Windows

**Note**: Optional for most users - only needed for advanced scripting

---

### 17. `Install-CygpathShim.ps1`
**Type**: PowerShell Utility
**Purpose**: Install cygpath command for path translation
**Status**: ⚠️ Development/Optional

**What It Does**:
- Installs cygpath utility
- Enables Windows ↔ Cygwin path translation

**Usage**:
```powershell
.\Install-CygpathShim.ps1
```

**Related**: Works with `cygpath` binary in same folder

**Note**: Optional - only for advanced cross-platform scripting

---

## Category 4: Python Support Modules

### 18. `extract_content.py`
**Type**: Python Module
**Purpose**: Extract text from PDF/DOCX/XLSX files
**Status**: ✅ Production

**Called By**: `Extract-SharePointContent.ps1`

**Capabilities**:
- PDF text extraction (pypdf)
- DOCX to markdown (python-docx)
- XLSX to markdown tables (openpyxl)

**Usage** (called by PowerShell):
```python
python extract_content.py --file "path/to/file.pdf"
```

**Dependencies**:
- pypdf
- python-docx
- openpyxl
- markdown

---

### 19. `generate_embeddings.py`
**Type**: Python Module
**Purpose**: Generate sentence embeddings for semantic search
**Status**: ✅ Production

**Called By**: `Generate-SemanticIndex.ps1`

**What It Does**:
- Loads pre-trained sentence transformer model
- Converts text to 384-dimensional vectors
- Saves embeddings to JSON

**Model**: `all-MiniLM-L6-v2` (22MB, fast, good quality)

**Usage** (called by PowerShell):
```python
python generate_embeddings.py --input content.json --output embeddings.json
```

**Dependencies**:
- sentence-transformers
- torch
- numpy

---

### 20. `semantic_search.py`
**Type**: Python Module
**Purpose**: Perform vector similarity search
**Status**: ✅ Production

**Called By**: `Search-SemanticContent.ps1`

**What It Does**:
- Converts query to embedding
- Computes cosine similarity
- Returns top N most similar results

**Usage** (called by PowerShell):
```python
python semantic_search.py --query "customer retention" --top 10
```

**Dependencies**:
- sentence-transformers
- numpy
- scipy

---

### 21. `analyze_image.py`
**Type**: Python Module
**Purpose**: Send images to Claude Vision API for analysis
**Status**: ✅ Production - Optional (requires API key)

**Called By**: `Analyze-Images-AI.ps1`

**What It Does**:
- Encodes images to base64
- Calls Claude Vision API
- Extracts descriptions, charts, text

**Usage** (called by PowerShell):
```python
python analyze_image.py --image "path/to/image.png" --api-key "sk-ant-..."
```

**Dependencies**:
- anthropic
- Pillow (PIL)
- base64

**Cost**: ~$0.001 per image

---

### 22. `extract_image_metadata.py`
**Type**: Python Module
**Purpose**: Extract EXIF and metadata from images
**Status**: ✅ Production

**Called By**: `Extract-ImageContent.ps1`

**What It Does**:
- Reads EXIF data
- Identifies image dimensions
- Extracts camera/software info

**Usage** (called by PowerShell):
```python
python extract_image_metadata.py --image "path/to/image.jpg"
```

**Dependencies**:
- Pillow (PIL)

---

### 23. `test_embeddings.py`
**Type**: Python Test Utility
**Purpose**: Test semantic embeddings generation
**Status**: ⚠️ Development/Testing

**What It Does**:
- Validates embedding model
- Tests vector generation
- Debug utility

**Usage**:
```bash
python test_embeddings.py
```

**When to Use**: Troubleshooting semantic search issues

---

## Category 5: Configuration & Support Files

### 24. `confluence-credentials.json`
**Type**: Configuration File (gitignored)
**Purpose**: Store Confluence API credentials
**Status**: ✅ Production - Gitignored, shareable within team

**Contents**:
```json
{
  "url": "https://gainservicing.atlassian.net",
  "email": "<EMAIL>",
  "api_token": "[REDACTED]"
}
```

**Created By**: `Setup-ConfluenceMCP.ps1`

**Security**: Gitignored, but safe to share within Gain Servicing team

---

### 25. `confluence-spaces-config.json`
**Type**: Configuration File
**Purpose**: Configure which Confluence spaces to sync
**Status**: ✅ Production

**Contents**: List of Confluence space keys to access

**Created By**: `Setup-ConfluenceMCP.ps1`

---

### 26-32. Log Files (All Gitignored)
**Type**: Runtime Logs
**Status**: ✅ Production - Auto-generated

**Files**:
- `sync-log.txt` - Master sync operations
- `extract-log.txt` - Content extraction logs
- `index-log.txt` - Indexing operations
- `semantic-log.txt` - Semantic index generation
- `extract-image-log.txt` - Image extraction/analysis
- `sharepoint-index-log.txt` - SharePoint indexing
- `shortcuts-log.txt` - Shortcut creation

**Purpose**: Debugging and monitoring

**Rotation**: Manual - delete when too large

---

## Cross-Reference: Scripts by Process Guide

### SEARCH-SYSTEMS-GUIDE.md References:
- `Sync-All.ps1` - Complete sync workflow
- `Sync-SharePointIndex.ps1` - Update file index
- `Extract-SharePointContent.ps1` - Extract documents
- `Index-SharePointContent.ps1` - Build keyword index
- `Generate-SemanticIndex.ps1` - Create embeddings
- `Search-SharePointContent.ps1` - Keyword search
- `Search-SemanticContent.ps1` - Semantic search

### IMAGE-SEARCH-GUIDE.md References:
- `Analyze-Images-AI.ps1` - AI vision analysis
- `Extract-ImageContent.ps1` - Image metadata
- `Search-SharePointContent.ps1 -FileType image` - Image search

### SERENA-AI-ASSISTANT-GUIDE.md References:
- `Setup-Serena-QuickStart.ps1` - Main setup
- `setup-serena.ps1` - Alternative setup

### CONFLUENCE-MCP-SETUP-GUIDE.md References:
- `Setup-ConfluenceMCP.ps1` - Interactive setup

### System/scripts/README.md References:
- `Sync-All.ps1`
- `Sync-SharePointIndex.ps1`
- `Setup-ConfluenceMCP.ps1`
- `Extract-SharePointContent.ps1`
- Related scripts overview

---

## Dependencies Matrix

### PowerShell Scripts
**Requires**: PowerShell 5.1+ (Windows) or PowerShell Core 7+ (cross-platform)

**All scripts work on**: Windows 10/11

### Python Scripts
**Requires**: Python 3.8+

**Required packages** (install with pip):
```bash
pip install pypdf python-docx openpyxl markdown
pip install sentence-transformers torch numpy scipy
pip install anthropic Pillow
pip install mcp-atlassian pydantic==2.11.0
```

### Node.js Tools
**Requires**: Node.js 18+

**Used by**: Serena MCP server

---

## Installation & First-Time Setup

### Quick Start (Recommended Order)

**Step 1: Set up Serena AI** (5 minutes)
```powershell
cd System/scripts
.\Setup-Serena-QuickStart.ps1
```

**Step 2: Set up Confluence** (5 minutes)
```powershell
.\Setup-ConfluenceMCP.ps1
```

**Step 3: Sync SharePoint** (10-30 minutes first time)
```powershell
.\Sync-All.ps1
```

**Step 4: Optional - Set up semantic search** (10 minutes + 500MB download)
```powershell
# Already included in Sync-All.ps1
# Or run manually:
.\Generate-SemanticIndex.ps1
```

**Step 5: Optional - Analyze images** (5-10 minutes + API cost)
```powershell
$env:ANTHROPIC_API_KEY = "sk-ant-your-key-here"
.\Analyze-Images-AI.ps1
```

---

## Maintenance Schedule

### Daily (Automated)
- None currently automated (task scheduler can be set up)

### Weekly (Manual)
```powershell
# Update SharePoint content
.\Sync-All.ps1 -IncrementalOnly
```

### Monthly (Manual)
```powershell
# Full rebuild
.\Sync-All.ps1

# Re-analyze images if needed
.\Analyze-Images-AI.ps1
```

### As Needed
- Run specific scripts when content changes
- Rebuild indexes after bulk file operations
- Update Confluence credentials if changed

---

## Troubleshooting

### Common Issues

**"Python not found"**:
- Install Python 3.8+: https://www.python.org/downloads/
- Ensure added to PATH

**"Module not found"**:
```powershell
pip install [module-name]
```

**"No SharePoint files found"**:
- Check OneDrive sync status
- Verify path in SharePointLinks/config.json

**"Semantic search not working"**:
- Run `.\Generate-SemanticIndex.ps1` first
- Check Python dependencies installed

**"Image analysis failing"**:
- Verify `ANTHROPIC_API_KEY` environment variable
- Check API key is valid
- Ensure `anthropic` package installed

**"Confluence not connecting"**:
- Verify credentials in `confluence-credentials.json`
- Test API token in browser
- Re-run `.\Setup-ConfluenceMCP.ps1`

---

## Script Development Guidelines

### For Adding New Scripts

**Location**: `System/scripts/`

**Naming Convention**:
- PowerShell: `Verb-Noun.ps1` (e.g., `Sync-SharePoint.ps1`)
- Python: `verb_noun.py` (e.g., `extract_content.py`)

**Documentation Requirements**:
1. Add to this registry (SCRIPTS-AND-TOOLS-REGISTRY.md)
2. Add usage to System/scripts/README.md
3. Reference in relevant process guides
4. Include inline comments in script

**Testing**:
- Test on clean environment
- Document all dependencies
- Handle errors gracefully
- Log to appropriate `*-log.txt` file

---

## Related Documentation

- **System/scripts/README.md** - Quick reference for common scripts
- **SEARCH-SYSTEMS-GUIDE.md** - Search capabilities overview
- **IMAGE-SEARCH-GUIDE.md** - Image search details
- **SERENA-AI-ASSISTANT-GUIDE.md** - Serena setup and usage
- **CONFLUENCE-MCP-SETUP-GUIDE.md** - Confluence integration
- **INSTALLED-TOOLS-INVENTORY.md** - External tools and dependencies

---

## Version History

**Version 1.0** (2025-10-28)
- Initial comprehensive inventory
- 32 scripts documented
- Cross-references to process guides
- Dependencies and usage documented

---

*This registry is the single source of truth for all helper scripts in the ReidCEO system. Keep it updated when adding/modifying scripts.*

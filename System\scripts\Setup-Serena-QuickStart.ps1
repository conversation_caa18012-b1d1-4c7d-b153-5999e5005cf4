# SERENA QUICK START FOR REID
# Double-click this file to set up Serena AI assistant
# Or right-click and choose "Run with PowerShell"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  SERENA AI ASSISTANT - Quick Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This will set up Serena for intelligent code navigation" -ForegroundColor White
Write-Host "and semantic search in the ReidCEO system." -ForegroundColor White
Write-Host ""

# Determine project path dynamically
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectPath = Split-Path -Parent (Split-Path -Parent $scriptPath)

Write-Host "Project: $projectPath" -ForegroundColor Gray
Write-Host ""

# Ask user which method to use
Write-Host "Choose installation method:" -ForegroundColor Yellow
Write-Host ""
Write-Host "  [1] <PERSON> (Recommended - fastest, simplest)" -ForegroundColor White
Write-Host "  [2] Full Setup Script (comprehensive, tests everything)" -ForegroundColor White
Write-Host ""
$choice = Read-Host "Enter choice (1 or 2)"

Write-Host ""

if ($choice -eq "1") {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Option 1: Claude CLI Installation" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""

    # Step 1: Check if git-bash path is set
    Write-Host "[1/3] Checking git-bash configuration..." -ForegroundColor Yellow

    $gitBashPath = [Environment]::GetEnvironmentVariable('CLAUDE_CODE_GIT_BASH_PATH', 'User')

    if (-not $gitBashPath -or !(Test-Path $gitBashPath)) {
        Write-Host "  ⚠ Git-bash path not configured" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Please enter the path to bash.exe (typically in Git installation):" -ForegroundColor White
        Write-Host "Common locations:" -ForegroundColor Gray
        Write-Host "  - C:\Dev\Git\usr\bin\bash.exe" -ForegroundColor Gray
        Write-Host "  - C:\Program Files\Git\usr\bin\bash.exe" -ForegroundColor Gray
        Write-Host "  - C:\Git\usr\bin\bash.exe" -ForegroundColor Gray
        Write-Host ""

        $bashPath = Read-Host "Enter bash.exe path"

        if (Test-Path $bashPath) {
            [Environment]::SetEnvironmentVariable('CLAUDE_CODE_GIT_BASH_PATH', $bashPath, 'User')
            $env:CLAUDE_CODE_GIT_BASH_PATH = $bashPath
            Write-Host "  ✓ Git-bash configured at: $bashPath" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Path not found: $bashPath" -ForegroundColor Red
            Write-Host ""
            Write-Host "Please verify Git is installed and try again." -ForegroundColor Yellow
            pause
            exit 1
        }
    } else {
        Write-Host "  ✓ Git-bash already configured: $gitBashPath" -ForegroundColor Green
    }

    Write-Host ""

    # Step 2: Check if Claude CLI is available
    Write-Host "[2/3] Checking Claude CLI..." -ForegroundColor Yellow

    try {
        $claudeVersion = claude --version 2>&1
        Write-Host "  ✓ Claude CLI available" -ForegroundColor Green
    } catch {
        Write-Host "  ✗ Claude CLI not found" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please install Claude Code first:" -ForegroundColor Yellow
        Write-Host "  https://claude.com/claude-code" -ForegroundColor Gray
        Write-Host ""
        pause
        exit 1
    }

    Write-Host ""

    # Step 3: Add Serena to Claude Code
    Write-Host "[3/3] Adding Serena to Claude Code..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Running command:" -ForegroundColor Gray
    Write-Host "  claude mcp add serena-ReidCEO -- uvx --from git+https://github.com/oraios/serena \" -ForegroundColor DarkGray
    Write-Host "    serena start-mcp-server --context ide-assistant --project `"$projectPath`"" -ForegroundColor DarkGray
    Write-Host ""

    try {
        # Change to project directory for relative path resolution
        Push-Location $projectPath

        $output = claude mcp add serena-ReidCEO -- uvx --from git+https://github.com/oraios/serena serena start-mcp-server --context ide-assistant --project $projectPath 2>&1

        Pop-Location

        if ($output -like "*Added*" -or $output -like "*Success*") {
            Write-Host "  ✓ Serena added successfully!" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Output: $output" -ForegroundColor Yellow
        }
    } catch {
        Pop-Location
        Write-Host "  ✗ Failed to add Serena" -ForegroundColor Red
        Write-Host "  Error: $_" -ForegroundColor Red
        Write-Host ""
        Write-Host "Try Option 2 (Full Setup Script) instead." -ForegroundColor Yellow
        pause
        exit 1
    }

    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  ✓ SETUP COMPLETE!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "NEXT STEP: Restart Claude Code" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "After restart, Serena will provide:" -ForegroundColor White
    Write-Host "  - Semantic code/doc search" -ForegroundColor Gray
    Write-Host "  - Symbol navigation and references" -ForegroundColor Gray
    Write-Host "  - Intelligent code understanding" -ForegroundColor Gray
    Write-Host "  - Smart editing tools" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Dashboard (when active): http://localhost:24282/dashboard/index.html" -ForegroundColor Cyan
    Write-Host ""

} elseif ($choice -eq "2") {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Option 2: Full Setup Script" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Running comprehensive setup..." -ForegroundColor White
    Write-Host ""

    # Run the full setup script
    $fullSetupScript = Join-Path $scriptPath "setup-serena.ps1"

    if (Test-Path $fullSetupScript) {
        & $fullSetupScript -ProjectPath $projectPath -Language "python"
    } else {
        Write-Host "  ✗ Setup script not found: $fullSetupScript" -ForegroundColor Red
        pause
        exit 1
    }

} else {
    Write-Host "Invalid choice. Please run again and choose 1 or 2." -ForegroundColor Red
    pause
    exit 1
}

Write-Host ""
Write-Host "For detailed documentation, see:" -ForegroundColor Gray
Write-Host "  System/SystemGuides/SERENA-AI-ASSISTANT-GUIDE.md" -ForegroundColor DarkGray
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
pause

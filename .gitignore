# ReidCEO - CEO Management System
# Git Ignore File

# ============================================
# TempDocs - Working Documents (NOT committed)
# ============================================
# This is your scratch pad for:
# - Working drafts and analysis
# - Meeting notes and experiments
# - AI-generated reports before review
# - Personal notes and brainstorming
TempDocs/

# ============================================
# Operating System Files
# ============================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ============================================
# Editor and IDE Files
# ============================================
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================
# Temporary Files
# ============================================
*.tmp
*.bak
*.log
~$*.xlsx
~$*.docx
~$*.pptx

# ============================================
# SharePoint - Links and Config (NOT committed)
# ============================================
# Local OneDrive configuration (machine-specific)
SharePointLinks/config.json

# Generated shortcuts (recreated from index.json)
SharePointLinks/**/*.lnk

# Sync automation logs
System/scripts/*-log.txt
System/scripts/sync-config.json

# Old approach (deprecated)
SharePointDocs/

# ============================================
# SharePoint Extracted Content (NOT committed)
# ============================================
# Extracted markdown from SharePoint (user-specific, changes frequently)
# Each user pulls their own SharePoint content locally
# SharePoint links are in KEY-DOCUMENTS-REGISTRY.md (committed)
docs/sharepoint-content/

# ============================================
# Personal/Private Notes
# ============================================
*PRIVATE*.md
*PERSONAL*.md
*SCRATCH*.md

# ============================================
# Serena MCP - AI Coding Assistant (NOT committed)
# ============================================
# Local Serena cache and indices (machine-specific)
.serena/cache/
.serena/logs/
.serena/memories/

# Temporary MCP config reference files
serena-mcp-config.json

# ============================================
# Confluence Credentials (COMMITTED for team sharing)
# ============================================
# Note: Confluence credentials are committed to allow team access
# Token is shared among trusted team members
# System/scripts/confluence-credentials.json - NOW COMMITTED
System/scripts/.env

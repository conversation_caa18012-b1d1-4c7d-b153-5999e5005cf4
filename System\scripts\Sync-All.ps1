<#
.SYNOPSIS
    Master sync script - updates everything when new SharePoint data is available

.DESCRIPTION
    Complete workflow for SharePoint content updates:
    1. Sync SharePoint index (finds new files)
    2. Extract content from new files
    3. Rebuild keyword index
    4. Rebuild semantic embeddings
    5. Ready for searches!

.PARAMETER Department
    Only sync specific department (Marketing, Finance, Legal, etc.)

.PARAMETER SkipExtraction
    Skip content extraction (only update indexes)

.PARAMETER SkipSemanticIndex
    Skip semantic embeddings (faster, but no semantic search)

.PARAMETER IncrementalOnly
    Incremental mode: Only process new/changed files (much faster)
    - Extraction: Skips already-extracted files (always incremental)
    - Keyword index: Always rebuilds (fast - 2 seconds)
    - Semantic embeddings: Only new files (vs full rebuild)

.EXAMPLE
    .\Sync-All.ps1
    Full sync of everything

.EXAMPLE
    .\Sync-All.ps1 -IncrementalOnly
    Fast incremental sync (only new/changed files)

.EXAMPLE
    .\Sync-All.ps1 -Department Marketing
    Only sync Marketing department

.EXAMPLE
    .\Sync-All.ps1 -SkipSemanticIndex
    Sync but skip embeddings (faster)

.NOTES
    Run this whenever:
    - New files added to SharePoint
    - SharePoint files are updated
    - After syncing new departments

    Time estimate:
    - Small update (few files): ~30 seconds
    - Full sync (284 files): ~5 minutes
#>

[CmdletBinding()]
param(
    [string]$Department = "",
    [switch]$SkipExtraction,
    [switch]$SkipSemanticIndex,
    [switch]$IncrementalOnly
)

$ErrorActionPreference = "Stop"
$StartTime = Get-Date

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "  SHAREPOINT SYNC - MASTER UPDATE SCRIPT" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

$stats = @{
    sharepointFiles = 0
    extractedFiles = 0
    keywordIndexed = 0
    semanticIndexed = 0
    errors = 0
}

# Step 1: Update SharePoint Index (creates/updates shortcuts)
Write-Host "[1/5] Updating SharePoint Index..." -ForegroundColor Yellow
Write-Host "      (Scans OneDrive, creates shortcuts)" -ForegroundColor Gray
Write-Host ""

try {
    if ($Department) {
        # Note: SharePoint index doesn't have per-department filter
        # It indexes all files, but we'll filter in extraction
        & "$PSScriptRoot\Sync-SharePointIndex.ps1" -UpdateIndex -NoAutoSync
    } else {
        & "$PSScriptRoot\Sync-SharePointIndex.ps1" -UpdateIndex -NoAutoSync
    }

    # Get count from index
    $indexFile = Join-Path (Split-Path -Parent (Split-Path -Parent $PSScriptRoot)) "SharePointLinks\index.json"
    if (Test-Path $indexFile) {
        $index = Get-Content $indexFile -Raw -Encoding UTF8 | ConvertFrom-Json
        $stats.sharepointFiles = $index.files.Count
        Write-Host "   Found: $($stats.sharepointFiles) files in SharePoint" -ForegroundColor Green
    }

    Write-Host ""
} catch {
    Write-Host "   ERROR: SharePoint index update failed" -ForegroundColor Red
    Write-Host "   $_" -ForegroundColor Red
    $stats.errors++
}

# Step 2: Extract Content
if (-not $SkipExtraction) {
    Write-Host "[2/5] Extracting Content..." -ForegroundColor Yellow
    Write-Host "      (PDF/DOCX/XLSX/PPTX -> Markdown)" -ForegroundColor Gray
    Write-Host ""

    try {
        $extractArgs = @()
        if ($Department) {
            $extractArgs += "-Department"
            $extractArgs += $Department
        }

        $extractOutput = & "$PSScriptRoot\Extract-SharePointContent.ps1" @extractArgs 2>&1

        # Check if extraction failed critically (not just "already extracted")
        if ($LASTEXITCODE -ne 0 -and $extractOutput -notmatch "already extracted") {
            throw "Extraction failed: $extractOutput"
        }

        # Count extracted files
        $contentDir = Join-Path (Split-Path -Parent (Split-Path -Parent $PSScriptRoot)) "docs\sharepoint-content"
        if (Test-Path $contentDir) {
            $mdFiles = Get-ChildItem -Path $contentDir -Filter "*.md" | Where-Object { $_.Name -ne "README.md" -and $_.Name -notlike "*readme*" }
            $stats.extractedFiles = $mdFiles.Count
            Write-Host "   Extracted: $($stats.extractedFiles) files (including previously extracted)" -ForegroundColor Green
        }

        Write-Host ""
    } catch {
        Write-Host "   ERROR: Content extraction failed" -ForegroundColor Red
        Write-Host "   $_" -ForegroundColor Red
        $stats.errors++
    }
} else {
    Write-Host "[2/5] Skipping Content Extraction (as requested)" -ForegroundColor DarkGray
    Write-Host ""
}

# Step 3: Create Shortcuts (if not done)
Write-Host "[3/5] Creating Shortcuts..." -ForegroundColor Yellow
Write-Host "      (Windows .lnk files)" -ForegroundColor Gray
Write-Host ""

try {
    & "$PSScriptRoot\Sync-SharePointIndex.ps1" -CreateShortcuts
    Write-Host "   Shortcuts updated" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "   ERROR: Shortcut creation failed" -ForegroundColor Red
    Write-Host "   $_" -ForegroundColor Red
    $stats.errors++
}

# Step 4: Rebuild Keyword Index
Write-Host "[4/5] Rebuilding Keyword Index..." -ForegroundColor Yellow
Write-Host "      (Inverted word index for fast keyword search)" -ForegroundColor Gray
Write-Host ""

try {
    # Always rebuild keyword index (it's fast - only 2 seconds)
    & "$PSScriptRoot\Index-SharePointContent.ps1" -Rebuild

    # Get stats
    $keywordIndexFile = Join-Path (Split-Path -Parent (Split-Path -Parent $PSScriptRoot)) "docs\sharepoint-content\search-index.json"
    if (Test-Path $keywordIndexFile) {
        $kwIndex = Get-Content $keywordIndexFile -Raw -Encoding UTF8 | ConvertFrom-Json
        $stats.keywordIndexed = $kwIndex.totalFiles
        Write-Host "   Indexed: $($stats.keywordIndexed) files for keyword search" -ForegroundColor Green
    }

    Write-Host ""
} catch {
    Write-Host "   ERROR: Keyword indexing failed" -ForegroundColor Red
    Write-Host "   $_" -ForegroundColor Red
    $stats.errors++
}

# Step 5: Rebuild Semantic Embeddings
if (-not $SkipSemanticIndex) {
    Write-Host "[5/5] Rebuilding Semantic Embeddings..." -ForegroundColor Yellow
    Write-Host "      (AI embeddings for semantic search)" -ForegroundColor Gray
    Write-Host ""

    try {
        if ($IncrementalOnly) {
            # Incremental mode: only generate embeddings for new files
            Write-Host "   Running in incremental mode (new files only)..." -ForegroundColor Cyan
            & "$PSScriptRoot\Generate-SemanticIndex.ps1"
        } else {
            # Full rebuild
            & "$PSScriptRoot\Generate-SemanticIndex.ps1" -Rebuild
        }

        # Get stats
        $semanticFile = Join-Path (Split-Path -Parent (Split-Path -Parent $PSScriptRoot)) "docs\sharepoint-content\semantic-embeddings.json"
        if (Test-Path $semanticFile) {
            $semIndex = Get-Content $semanticFile -Raw -Encoding UTF8 | ConvertFrom-Json
            $stats.semanticIndexed = $semIndex.total_files
            Write-Host "   Embedded: $($stats.semanticIndexed) files for semantic search" -ForegroundColor Green
        }

        Write-Host ""
    } catch {
        Write-Host "   ERROR: Semantic indexing failed" -ForegroundColor Red
        Write-Host "   $_" -ForegroundColor Red
        $stats.errors++
    }
} else {
    Write-Host "[5/5] Skipping Semantic Embeddings (as requested)" -ForegroundColor DarkGray
    Write-Host ""
}

# Summary
$EndTime = Get-Date
$Duration = ($EndTime - $StartTime).TotalSeconds

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "  SYNC COMPLETE" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "  SharePoint files found:  $($stats.sharepointFiles)" -ForegroundColor White
Write-Host "  Content extracted:       $($stats.extractedFiles)" -ForegroundColor White
Write-Host "  Keyword indexed:         $($stats.keywordIndexed)" -ForegroundColor White
Write-Host "  Semantic embedded:       $($stats.semanticIndexed)" -ForegroundColor White
Write-Host "  Errors:                  $($stats.errors)" -ForegroundColor $(if ($stats.errors -eq 0) { "Green" } else { "Red" })
Write-Host "  Duration:                $([math]::Round($Duration, 1))s" -ForegroundColor White
Write-Host ""

if ($stats.errors -eq 0) {
    Write-Host "All indexes updated successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now search:" -ForegroundColor Cyan
    Write-Host "  - Keyword:  .\Search-SharePointContent.ps1 -Query 'budget'" -ForegroundColor Gray
    Write-Host "  - Semantic: .\Search-SemanticContent.ps1 -Query 'customer satisfaction'" -ForegroundColor Gray
} else {
    Write-Host "Completed with $($stats.errors) error(s) - check logs above" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

@echo off
cd "C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts"

echo Loading email body...
set "bodyFile=..\..\TempDocs\test-email-body.txt"

echo Running Send-Email.ps1...
powershell.exe -ExecutionPolicy Bypass -Command "$body = Get-Content '%bodyFile%' -Raw; .\Send-Email.ps1 -To '<EMAIL>' -Subject 'ReidCEO System - Tools & Capabilities Overview' -Body $body"

echo.
echo Done! Check Outlook for the draft email.
pause

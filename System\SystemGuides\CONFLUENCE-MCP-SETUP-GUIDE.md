# Confluence MCP Setup Guide

**Purpose**: Connect Claude Code to Gain Servicing's Confluence workspace for AI-powered document search and retrieval.

**Status**: ✅ Ready for team deployment

---

## Overview

This guide shows you how to set up the Confluence MCP (Model Context Protocol) server, which allows Claude Code to:
- Search Confluence spaces and pages
- Retrieve page content and metadata
- Access ops information, runbooks, technology docs, training materials, and HR info
- Use AI-powered semantic search across all Confluence content

**Important**: The Confluence API token can be shared within the Gain Servicing team. This allows everyone to use the same credentials for Claude Code access.

---

## Prerequisites

- Python 3.11+ installed
- Access to https://gainservicing.atlassian.net
- Claude Code installed and configured
- Administrator or sufficient permissions to generate API tokens

---

## Step 1: Install Required Packages

Open PowerShell as Administrator and run:

```powershell
cd C:\Projects\ReidCEO

# Install mcp-atlassian package
pip install mcp-atlassian

# Fix pydantic compatibility (important!)
pip install pydantic==2.11.0

# Verify installation
python -c "import mcp_atlassian.confluence; print('SUCCESS: MCP module installed!')"
```

**Expected output**: `SUCCESS: MCP module installed!`

---

## Step 2: Get Confluence API Token

**Option A: Use Existing Team Token (Recommended)**
- Ask Jonathan or IT for the shared Confluence API token
- This token is already configured and tested
- Skip to Step 3

**Option B: Generate Your Own Token**

1. Go to https://id.atlassian.com/manage-profile/security/api-tokens
2. Click **"Create API token"**
3. Give it a name: `Claude Code - [Your Name]`
4. Copy the generated token immediately (you won't see it again!)
5. Store it securely

---

## Step 3: Run the Setup Script

**Easy Setup (Recommended):**

```powershell
cd C:\Projects\ReidCEO\System\scripts
.\Setup-ConfluenceMCP.ps1
```

The script will:
- Prompt for your Confluence API token (or use the team token)
- Create the credentials file (gitignored)
- Update your MCP server configuration
- Test the connection
- Verify everything works

**Manual Setup (Advanced):**

If you prefer manual configuration, see the "Manual Configuration" section at the end.

---

## Step 4: Verify Setup

The setup script will automatically test the connection. You should see:

```
✓ Connected to Confluence!
✓ Found X spaces:
  - Accounting
  - Technology
  - Operations
  [etc...]
```

---

## Step 5: Use Confluence in Claude Code

Once set up, Claude Code can access Confluence through MCP tools. You can ask:

**Search examples:**
- "Search Confluence for employee onboarding procedures"
- "Find all pages about database backup procedures"
- "What's in our technology roadmap?"
- "Show me the latest HR policies"

**Space navigation:**
- "List all Confluence spaces"
- "What pages are in the Operations space?"
- "Show me recent updates in Technology"

**Content retrieval:**
- "Get the content of the 'Emergency Response Runbook' page"
- "Show me the troubleshooting guide for the CRM"

---

## Configuration Details

### Files Created

**`System/scripts/confluence-credentials.json`** (gitignored):
```json
{
  "confluence_url": "https://gainservicing.atlassian.net",
  "confluence_email": "<EMAIL>",
  "confluence_api_token": "YOUR_API_TOKEN_HERE"
}
```

**`serena-mcp-config.json`** (gitignored):
```json
{
  "mcpServers": {
    "serena-ReidCEO": { ... },
    "confluence": {
      "command": "python",
      "args": [
        "-m",
        "mcp_atlassian.confluence",
        "--url", "https://gainservicing.atlassian.net",
        "--email", "<EMAIL>",
        "--token", "YOUR_API_TOKEN_HERE"
      ]
    }
  }
}
```

### Security Notes

1. **Credentials are gitignored**: Your API token never goes to Git
2. **Team sharing**: The API token can be safely shared within Gain Servicing
3. **Token rotation**: If the token needs to be changed, just re-run the setup script
4. **Access level**: The token has the same permissions as the user who created it

---

## Troubleshooting

### "Module not found" error

```powershell
pip install mcp-atlassian
pip install pydantic==2.11.0
```

### "Cannot import" or pydantic errors

```powershell
# Fix pydantic compatibility
pip install pydantic==2.11.0
```

### "Authentication failed"

- Verify your API token is correct (check for typos)
- Ensure you have access to https://gainservicing.atlassian.net
- Try regenerating the API token

### "Connection timeout"

- Check your internet connection
- Verify Confluence is not experiencing an outage: https://status.atlassian.com/
- Try again in a few minutes

---

## Manual Configuration

If you prefer to configure manually instead of using the setup script:

### 1. Create Credentials File

Create `System/scripts/confluence-credentials.json`:
```json
{
  "confluence_url": "https://gainservicing.atlassian.net",
  "confluence_email": "<EMAIL>",
  "confluence_api_token": "YOUR_API_TOKEN_HERE"
}
```

### 2. Update MCP Config

Edit `serena-mcp-config.json` and add the confluence server:
```json
{
  "mcpServers": {
    "serena-ReidCEO": {
      "command": "uvx",
      "args": [
        "--from",
        "git+https://github.com/oraios/serena",
        "serena",
        "start-mcp-server",
        "--context",
        "ide-assistant",
        "--project",
        "C:\\Projects\\ReidCEO"
      ]
    },
    "confluence": {
      "command": "python",
      "args": [
        "-m",
        "mcp_atlassian.confluence",
        "--url",
        "https://gainservicing.atlassian.net",
        "--email",
        "<EMAIL>",
        "--token",
        "YOUR_API_TOKEN_HERE"
      ]
    }
  }
}
```

### 3. Test Connection

```powershell
python -c "from atlassian import Confluence; import json; creds = json.load(open('C:/Projects/ReidCEO/System/scripts/confluence-credentials.json')); c = Confluence(url=creds['confluence_url'], username=creds['confluence_email'], password=creds['confluence_api_token']); spaces = c.get_all_spaces(limit=5); print(f'Connected! Found {len(spaces[\"results\"])} spaces:'); [print(f\"  - {s['name']}\") for s in spaces['results']]"
```

---

## Team Token (Shared Credentials)

**Current Team Token** (as of 2025-10-20):
- **URL**: https://gainservicing.atlassian.net
- **Email**: <EMAIL>
- **Token**: Contact IT or check System/scripts/confluence-credentials.json

**Using the team token**:
- Copy the values from the existing `confluence-credentials.json` file
- Or ask Jonathan/IT for the current token
- All team members can use the same credentials

---

## Need Help?

- **Documentation Issues**: Update this guide at `System/SystemGuides/CONFLUENCE-MCP-SETUP-GUIDE.md`
- **Technical Support**: Contact IT or Jonathan
- **Confluence Issues**: Check https://status.atlassian.com/

---

**Version**: 1.0
**Last Updated**: 2025-10-20
**Maintainer**: ReidCEO Project Team

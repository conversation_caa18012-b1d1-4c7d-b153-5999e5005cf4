# Search Systems Guide - ReidCEO

**Last Updated**: 2025-10-18
**Status**: Active - Two complementary search systems

---

## Overview

ReidCEO has **TWO independent search systems** that work together to provide complete coverage:

1. **📂 SharePoint Search** (Custom PowerShell Scripts)
2. **🤖 Repository Search** (Serena AI Assistant)

**Key Point**: These systems are **complementary, not redundant**. Each searches different content with different capabilities.

---

## Quick Decision Guide

### Use SharePoint Search When...

✅ Searching for **SharePoint documents** (Marketing, Finance, Legal, etc.)
✅ Finding **images, charts, or presentations**
✅ Need **semantic/conceptual search** ("similar ideas")
✅ Want **direct PowerShell access** (without AI assistant)
✅ Searching across **284 extracted SharePoint files**

**Location**: `System/scripts/Search-*.ps1`

### Use Serena (Repository Search) When...

✅ Navigating **ReidCEO code and documentation**
✅ Finding **PowerShell functions or markdown sections**
✅ Need to **edit or modify files** intelligently
✅ Want **symbol-based navigation** (classes, functions, headers)
✅ Finding **where code is referenced or used**
✅ Working with **Git-tracked files** (CEO-Dashboard, System, templates)

**Access**: Via Claude Code AI assistant

---

## System 1: SharePoint Search (Custom Scripts)

### What It Searches

**Content**: Extracted SharePoint documents stored in `docs/sharepoint-content/`

```
docs/sharepoint-content/
├── Marketing_F_Drive_*.md (presentations, strategies)
├── Finance_*.md (budgets, projections)
├── Legal_*.md (contracts, compliance)
└── 54 markdown files (2.3 MB extracted content)
```

**Important**: This content is **gitignored** (user-specific, changes frequently), but:
- ✅ Serena STILL indexes these files (configured to include gitignored SharePoint content)
- ✅ Custom scripts provide semantic + image search
- ✅ **Both systems work together** on SharePoint content
- ✅ Each user (you, Reid, others) pulls their own SharePoint content locally

**Why gitignored**: SharePoint content changes frequently and is user-specific. SharePoint **links** are in KEY-DOCUMENTS-REGISTRY.md (committed), but the extracted content itself is gitignored.

**Note**: Synced from SharePoint via OneDrive, then extracted to markdown.

### Search Capabilities

#### 1. Full-Text Search
```powershell
cd System/scripts
.\Search-SharePointContent.ps1 -Query "customer acquisition"
```

**Best for**: Exact keyword matching

#### 2. Semantic Search
```powershell
.\Search-SemanticContent.ps1 -Query "client retention strategies"
```

**Best for**: Conceptual similarity (finds "customer satisfaction", "relationship management", etc.)

#### 3. Image Search
```powershell
.\Search-SharePointContent.ps1 -Query "revenue chart" -FileType image
```

**Best for**: Finding images by content (uses AI vision analysis)

### Example Use Cases

**Marketing Materials**:
```powershell
.\Search-SemanticContent.ps1 -Query "lead generation tactics" -Department Marketing -Top 5
```

**Finance Documents**:
```powershell
.\Search-SharePointContent.ps1 -Query "2025 budget projections"
```

**Images with Charts**:
```powershell
.\Search-SharePointContent.ps1 -Query "bar chart" -FileType image -ShowContext
```

### Setup Requirements

✅ SharePoint content extracted: `.\Extract-SharePointContent.ps1`
✅ Search index built: `.\Index-SharePointContent.ps1`
✅ Semantic embeddings generated: `.\Generate-SemanticIndex.ps1` (optional)
✅ Images analyzed: `.\Analyze-Images-AI.ps1` (optional, requires API key)

**See**: `System/SystemGuides/IMAGE-SEARCH-GUIDE.md` for image search details

---

## System 2: Repository Search (Serena)

### What It Searches

**Content**: Git-tracked files in the ReidCEO repository

```
ReidCEO/
├── CLAUDE.md
├── CEO-Dashboard/ (briefings, KPIs, action items)
├── Finance/ (templates, tracking files)
├── Legal/ (tracking, briefings)
├── Sales/ (target lists, proposals)
├── Marketing/ (templates, ROI tools)
├── Technology/ (roadmap, initiatives)
└── System/ (scripts, documentation)
```

**Note**: This is **Git-tracked content** (committed to version control).

### Search Capabilities

#### 1. Symbol Search
Ask Claude Code:
```
"Use Serena to find all PowerShell functions starting with 'Sync'"
"Show me markdown sections about KPI tracking"
```

**Best for**: Finding code symbols, function definitions, markdown headers

#### 2. Reference Finding
Ask Claude Code:
```
"Find all files that reference the daily-briefing template"
"Where is the Sync-SharePoint function called?"
```

**Best for**: Understanding code relationships and dependencies

#### 3. File Overview
Ask Claude Code:
```
"Use Serena to show me the structure of CLAUDE.md"
"Give me an overview of the CEO-Dashboard folder"
```

**Best for**: Understanding file organization and hierarchy

#### 4. Pattern Search
Ask Claude Code:
```
"Search for all markdown files containing 'KPI dashboard'"
"Find all PowerShell scripts with error handling"
```

**Best for**: Regex-based content search

### Example Use Cases

**Finding Templates**:
```
"Use Serena to find the account performance template"
```

**Code Navigation**:
```
"Show me all PowerShell functions in the Sync-SharePoint.ps1 script"
```

**Editing Content**:
```
"Find the KPI alerts section and update it with new thresholds"
```

**Understanding Structure**:
```
"Give me a symbols overview of the Finance folder documentation"
```

### Setup Requirements

✅ Serena installed: Run `System/scripts/Setup-Serena-QuickStart.ps1`
✅ Claude Code running with Serena MCP active
✅ Project indexed (automatic on first use)

**See**: `System/SystemGuides/SERENA-AI-ASSISTANT-GUIDE.md` for Serena setup

---

## SharePoint Content Overlap (IMPORTANT)

### Both Systems Index `docs/sharepoint-content/`

The 54 extracted SharePoint markdown files are indexed by **BOTH** search systems:

**Why this is good**:
- Serena provides: Structure navigation, symbol search, AI integration
- Custom scripts provide: Semantic search, image analysis, direct CLI access
- Together: Maximum capability with different strengths

### When to Use Each on SharePoint Content

#### Use Serena (Claude Code) For:
```
"Show me the structure of the Marketing Strategy document"
"Find all markdown headers mentioning 'Budget' in SharePoint files"
"Get an overview of the Lead Generation Plan sections"
```

**Best for**: Understanding document organization, finding sections/headers

#### Use Custom Scripts For:
```powershell
.\Search-SemanticContent.ps1 -Query "customer retention tactics"
.\Search-SharePointContent.ps1 -Query "revenue chart" -FileType image
.\Search-SharePointContent.ps1 -Query "2025 projections" -Department Finance
```

**Best for**: Conceptual/semantic search, image discovery, batch operations

### Is This Redundant?

**No** - They provide different capabilities:
- **Serena**: Structured navigation (markdown headers, sections)
- **Custom**: Semantic meaning + images (AI embeddings + vision)

**Storage overhead**: ~400 KB total (negligible)

---

## Comparison Matrix

| Feature | SharePoint Search | Serena (Repository) |
|---------|-------------------|---------------------|
| **Content** | 54 SharePoint files | Git-tracked files + SharePoint |
| **Access** | PowerShell CLI (direct) | Claude Code (AI assistant) |
| **Search Type** | Full-text, semantic, image | Symbol, pattern, reference |
| **Best For** | Document discovery | Code navigation + structure |
| **Unique Features** | AI vision, semantic similarity | Code editing, reference finding |
| **Setup** | Index scripts | Serena MCP installation |
| **Cost** | ~$0 (local) + API for images | ~$0 (local) |
| **User** | Reid directly | AI assistant for Reid |

---

## When to Use Both

### Scenario: Comprehensive Content Discovery

**Goal**: Find all information about "customer retention"

**Step 1**: Search SharePoint (custom scripts)
```powershell
.\Search-SemanticContent.ps1 -Query "customer retention strategies"
```
**Finds**: Marketing presentations, finance analysis, sales proposals from SharePoint

**Step 2**: Search Repository (Serena)
```
"Use Serena to find all markdown files mentioning customer retention"
```
**Finds**: Templates, tracking files, briefings in Git repository

**Result**: Complete coverage across both content sources

---

## Common Questions

### Q: Why two search systems?

**A**: They search **different content** in **different locations**:
- SharePoint Search → `docs/sharepoint-content/` (gitignored SharePoint extracts)
- Serena → ReidCEO repository (Git-tracked operational files)

### Q: Is there overlap?

**A**: Minimal (~5%). Department folders have both:
- **SharePoint**: Synced documents from company SharePoint
- **Repository**: Operational templates and tracking files

### Q: Which is better?

**A**: Neither - they're complementary:
- SharePoint Search: Better for **documents and images**
- Serena: Better for **code navigation and editing**

### Q: Can they work together?

**A**: Yes! Search SharePoint for documents, use Serena for repository navigation.

### Q: Do I need both?

**A**: Yes, if you want:
- ✅ Access to SharePoint documents
- ✅ Semantic/image search capabilities
- ✅ Code navigation and intelligent editing
- ✅ Complete coverage of all content

---

## Architecture Diagram

```
┌─────────────────────────────────────────────────────┐
│              ReidCEO Search Architecture             │
└─────────────────────────────────────────────────────┘

  SharePoint (OneDrive)              Git Repository
         │                                  │
         │ Extract                          │ Clone
         ↓                                  ↓
  docs/sharepoint-content/           ReidCEO/ (local)
  (284 files, gitignored)            (~50 files, tracked)
         │                                  │
         │ Index Scripts                    │ Serena Index
         ↓                                  ↓
  search-index.json                  .serena/cache/
  semantic-embeddings.json           (symbol indices)
         │                                  │
         │ PowerShell                       │ MCP Tools
         ↓                                  ↓
  Reid (Direct CLI)                  Claude Code (AI)

  ═══════════════════════════════════════════════════
             Two Systems, One Ecosystem
```

---

## Getting Started

### For SharePoint Search

1. **Extract content** (one-time):
   ```powershell
   cd System/scripts
   .\Extract-SharePointContent.ps1
   ```

2. **Build index**:
   ```powershell
   .\Index-SharePointContent.ps1
   ```

3. **Optional - Semantic search**:
   ```powershell
   .\Generate-SemanticIndex.ps1
   ```

4. **Optional - Image analysis**:
   ```powershell
   # Set API key first
   $env:ANTHROPIC_API_KEY = "sk-ant-..."
   .\Analyze-Images-AI.ps1
   ```

5. **Search**:
   ```powershell
   .\Search-SharePointContent.ps1 -Query "your query"
   .\Search-SemanticContent.ps1 -Query "conceptual query"
   ```

### For Serena (Repository Search)

1. **Install Serena** (one-time):
   ```powershell
   cd System/scripts
   .\Setup-Serena-QuickStart.ps1
   ```

2. **Restart Claude Code**

3. **Use via Claude Code**:
   ```
   "Use Serena to find..."
   "Show me the structure of..."
   "Find references to..."
   ```

---

## Maintenance

### SharePoint Search

**Frequency**: Weekly or when SharePoint content changes

```powershell
# Re-extract new content
.\Extract-SharePointContent.ps1

# Rebuild index
.\Index-SharePointContent.ps1 -Rebuild

# Update semantic embeddings (if using)
.\Generate-SemanticIndex.ps1 -Rebuild

# Analyze new images (if using)
.\Analyze-Images-AI.ps1
```

### Serena

**Frequency**: Automatic (no manual maintenance needed)

- Index updates automatically when files change
- Cache managed by Serena
- No user intervention required

---

## Troubleshooting

### SharePoint Search Issues

**"No results found"**:
- Check index exists: `docs/sharepoint-content/search-index.json`
- Rebuild index: `.\Index-SharePointContent.ps1 -Rebuild`

**"Embeddings not found"**:
- Generate embeddings: `.\Generate-SemanticIndex.ps1`
- Requires Python with sentence-transformers

### Serena Issues

**"Tools not available"**:
- Restart Claude Code
- Verify Serena installed: Check Claude Code settings

**"No results"**:
- Check file is Git-tracked (not gitignored)
- Serena only indexes committed files

---

## Summary

✅ **SharePoint Search**: PowerShell scripts for searching 284 SharePoint documents
✅ **Serena**: AI assistant for navigating Git-tracked ReidCEO files
✅ **Complementary**: Different content, different capabilities, different interfaces
✅ **Keep Both**: Maximum coverage and functionality

**For SharePoint documents**: Use PowerShell scripts
**For repository navigation**: Use Serena via Claude Code
**For complete coverage**: Use both systems together

---

## Related Documentation

- **SharePoint Sync**: `SHAREPOINT-SYNC-GUIDE.md`
- **Image Search**: `IMAGE-SEARCH-GUIDE.md`
- **Serena Setup**: `SERENA-AI-ASSISTANT-GUIDE.md`
- **System Overview**: `README.md`

---

*This guide explains how ReidCEO's two search systems work together to provide complete content coverage.*

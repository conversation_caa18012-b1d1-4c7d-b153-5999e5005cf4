# Email-ActionItems.ps1
# Send action item notifications to team members

param(
    [Parameter(Mandatory=$true)]
    [string]$ActionItemFile,  # Path to action item markdown file

    [string]$AssigneeName = "",
    [string]$AssigneeEmail = "",
    [string]$Priority = "Normal",
    [string]$Deadline = "",
    [string]$Context = "",
    [switch]$Send,
    [switch]$Silent
)

<#
.SYNOPSIS
Send action item assignment notification email

.DESCRIPTION
Reads action item from file and sends formatted assignment email to assignee.

.EXAMPLE
.\Email-ActionItems.ps1 -ActionItemFile "CEO-Dashboard/action-items/review-budget.md" `
    -AssigneeName "<PERSON>" `
    -AssigneeEmail "<EMAIL>" `
    -Priority "High" `
    -Deadline "Friday, Nov 1"

Opens draft action item email for review.

.EXAMPLE
.\Email-ActionItems.ps1 -ActionItemFile "path/to/item.md" `
    -AssigneeName "<PERSON>" `
    -AssigneeEmail "<EMAIL>" `
    -Send

Sends action item email immediately.

.NOTES
Requires:
- Send-TemplatedEmail.ps1
- EmailTemplates/action-item-assigned.txt
#>

function Write-Log {
    param($Message, $Level = "INFO")
    if (-not $Silent) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

function Extract-Metadata {
    param($Content, $Key)

    $pattern = "(?mi)^\*\*$Key\*\*:\s*(.+)$"
    if ($Content -match $pattern) {
        if ($matches[1]) {
            return $matches[1].Trim()
        }
    }
    return ""
}

function Clean-Content {
    param($Content)

    if (-not $Content) {
        return $Content
    }

    # Remove emojis
    $cleaned = $Content -replace '[^\x00-\x7F]+', ''

    # Convert markdown bold to HTML
    $cleaned = $cleaned -replace '\*\*([^*]+)\*\*', '<strong>$1</strong>'

    # Convert markdown links to HTML
    $cleaned = $cleaned -replace '\[([^\]]+)\]\(([^)]+)\)', '<a href="$2">$1</a>'

    # Convert line breaks to <br>
    $cleaned = $cleaned -replace "`r`n", "<br>`r`n"

    return $cleaned
}

try {
    Write-Log "Starting action item notification..."

    # Validate action item file
    if (-not (Test-Path $ActionItemFile)) {
        Write-Log "ERROR: Action item file not found: $ActionItemFile" "ERROR"
        exit 1
    }

    Write-Log "Reading action item from: $ActionItemFile"
    $itemContent = Get-Content $ActionItemFile -Raw

    # Extract title from filename or content
    $fileName = (Get-Item $ActionItemFile).BaseName
    $taskTitle = $fileName -replace '-', ' '

    # Try to extract title from content (first H1 header)
    if ($itemContent -match "(?m)^#\s+(.+)$") {
        $taskTitle = $matches[1].Trim()
    }

    Write-Log "Task: $taskTitle"

    Write-Log "Extracting metadata..."
    # Extract metadata from content if not provided via parameters
    if (-not $AssigneeName) {
        $AssigneeName = Extract-Metadata $itemContent "Assignee|Assigned To"
        Write-Log "Assignee Name: '$AssigneeName'"
    }

    if (-not $AssigneeEmail) {
        $AssigneeEmail = Extract-Metadata $itemContent "Email|Assignee Email"
        Write-Log "Assignee Email: '$AssigneeEmail'"
    }

    if (-not $Deadline) {
        $Deadline = Extract-Metadata $itemContent "Deadline|Due Date"
        Write-Log "Deadline: '$Deadline'"
    }

    if (-not $Priority) {
        $extractedPriority = Extract-Metadata $itemContent "Priority"
        if ($extractedPriority) {
            $Priority = $extractedPriority
        }
        Write-Log "Priority: '$Priority'"
    }

    Write-Log "Validating required fields..."
    # Validate required fields
    if (-not $AssigneeEmail) {
        Write-Log "ERROR: Assignee email not found. Specify -AssigneeEmail or include in file" "ERROR"
        exit 1
    }

    if (-not $AssigneeName) {
        # Extract name from email
        Write-Log "Extracting name from email..."
        $AssigneeName = ($AssigneeEmail -split '@')[0]
        Write-Log "Assignee name not found, using email prefix: $AssigneeName" "WARN"
    }

    Write-Log "Extracting description..."
    # Extract description (first paragraph after metadata)
    $taskDescription = "See attached action item file for full details."
    if ($itemContent -match "(?ms)^##\s*Description\s*`$(.+?)(?=^##|\z)") {
        if ($matches[1]) {
            $taskDescription = $matches[1].Trim()
        }
    }

    Write-Log "Extracting context..."
    # Use provided context or extract from file
    $taskContext = $Context
    if (-not $taskContext -and ($itemContent -match "(?ms)^##\s*Context\s*`$(.+?)(?=^##|\z)")) {
        if ($matches[1]) {
            $taskContext = $matches[1].Trim()
        }
    }
    if (-not $taskContext) {
        $taskContext = "See action item file for full context."
    }

    Write-Log "Extracting next steps..."
    # Extract next steps
    $nextSteps = "1. Review action item details`n2. Confirm receipt`n3. Ask questions if needed"
    if ($itemContent -match "(?ms)^##\s*Next Steps\s*`$(.+?)(?=^##|\z)") {
        if ($matches[1]) {
            $nextSteps = $matches[1].Trim()
        }
    }

    Write-Log "Cleaning content..."
    # Clean content (remove emojis, convert markdown to HTML)
    $taskDescription = Clean-Content $taskDescription
    $taskContext = Clean-Content $taskContext
    $nextSteps = Clean-Content $nextSteps

    Write-Log "Content cleaned successfully"

    # Prepare template variables
    $variables = @{
        assignee_name = $AssigneeName
        assignee_email = $AssigneeEmail
        task_title = $taskTitle
        priority = $Priority
        deadline = if ($Deadline) { $Deadline } else { "TBD" }
        assigned_by = $env:USERNAME
        task_description = $taskDescription
        task_context = $taskContext
        next_steps = $nextSteps
        task_link = $ActionItemFile
        cc = ""
    }

    # Show summary
    if (-not $Silent) {
        Write-Host ""
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host "Action Item Notification Summary" -ForegroundColor Cyan
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host "Task: $taskTitle" -ForegroundColor White
        Write-Host "Assignee: $AssigneeName ($AssigneeEmail)" -ForegroundColor White
        Write-Host "Priority: $Priority" -ForegroundColor White
        Write-Host "Deadline: $(if ($Deadline) { $Deadline } else { 'TBD' })" -ForegroundColor White
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host ""
    }

    # Build parameters for Send-TemplatedEmail
    $templateParams = @{
        Template = "action-item-assigned-html"
        Variables = $variables
        Silent = $Silent
    }

    if ($Send) { $templateParams["Send"] = $true }

    # Generate and send email
    Write-Log "Generating email from template..."
    $templateScript = Join-Path $PSScriptRoot "Send-TemplatedEmail.ps1"

    if (-not (Test-Path $templateScript)) {
        Write-Log "ERROR: Send-TemplatedEmail.ps1 not found" "ERROR"
        exit 1
    }

    & $templateScript @templateParams

    if ($LASTEXITCODE -eq 0) {
        Write-Log "Action item notification generated successfully" "SUCCESS"
        exit 0
    }
    else {
        Write-Log "ERROR: Failed to generate notification" "ERROR"
        exit 1
    }
}
catch {
    Write-Log "ERROR: Failed to generate action item notification" "ERROR"
    Write-Log "Error details: $($_.Exception.Message)" "ERROR"
    exit 1
}

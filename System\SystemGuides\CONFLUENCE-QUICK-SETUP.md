# Confluence MCP - Quick Setup Card

**Team Token**: Share this with Gain Servicing employees setting up Claude Code

---

## One-Time Setup (5 minutes)

**1. Install packages:**
```powershell
cd C:\Projects\ReidCEO
pip install mcp-atlassian
pip install pydantic==2.11.0
```

**2. Run setup wizard:**
```powershell
cd System\scripts
.\Setup-ConfluenceMCP.ps1
```

**3. When prompted, use these credentials:**
- **URL**: `https://gainservicing.atlassian.net`
- **Email**: `<EMAIL>`
- **Token**: Ask IT or check `System/scripts/confluence-credentials.json`

**That's it!** Restart Claude Code and start searching Confluence.

---

## Example Commands

Once configured, ask Claude:

**Search:**
- "Search Confluence for employee onboarding"
- "Find all runbooks in Operations"
- "What's our current tech roadmap?"

**Browse:**
- "List all Confluence spaces"
- "Show me pages in the Technology space"
- "What's been updated recently in HR?"

**Retrieve:**
- "Get the content of the Emergency Response page"
- "Show me the CRM troubleshooting guide"

---

## Troubleshooting

**"Module not found"**
```powershell
pip install mcp-atlassian
pip install pydantic==2.11.0
```

**"Authentication failed"**
- Check your API token (ask IT if unsure)
- Verify you have access to gainservicing.atlassian.net

**Need help?**
- Full guide: `System\SystemGuides\CONFLUENCE-MCP-SETUP-GUIDE.md`
- Contact: IT or Jonathan

---

## Security Note

✅ The API token is gitignored (safe from commits)
✅ Can be shared within Gain Servicing team
✅ Same permissions as the account that created it

---

**Setup Support**: If you encounter issues, run the wizard again or check the full setup guide.

# Serena MCP Setup Script for Any Project
# This script automates Serena setup for a project, making it reusable across all projects

param(
    [string]$ProjectPath = $PWD,
    [string]$Language = "python",
    [switch]$SkipIndex,
    [switch]$GlobalMCPConfig
)

$ErrorActionPreference = "Stop"

Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Serena MCP Setup Script" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Verify prerequisites
Write-Host "[1/5] Checking prerequisites..." -ForegroundColor Yellow

# Check Python version
try {
    $pythonVersion = python --version 2>&1
    if ($pythonVersion -match "Python (\d+)\.(\d+)") {
        $major = [int]$Matches[1]
        $minor = [int]$Matches[2]
        if ($major -ge 3 -and $minor -ge 11) {
            Write-Host "  ✓ Python $pythonVersion" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Python 3.11+ required (found: $pythonVersion)" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "  ✗ Python not found" -ForegroundColor Red
    exit 1
}

# Check uv
try {
    $uvVersion = uv --version 2>&1
    Write-Host "  ✓ uv $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "  ✗ uv not found. Installing..." -ForegroundColor Yellow
    curl.exe -LsSf https://astral.sh/uv/install.sh | bash
}

# Check if Serena is accessible
try {
    $serenaTest = uvx --from git+https://github.com/oraios/serena serena --help 2>&1
    Write-Host "  ✓ Serena accessible via uvx" -ForegroundColor Green
} catch {
    Write-Host "  ✗ Cannot access Serena" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 2: Generate project.yml
Write-Host "[2/5] Generating Serena project configuration..." -ForegroundColor Yellow

$serenaDir = Join-Path $ProjectPath ".serena"
if (!(Test-Path $serenaDir)) {
    New-Item -ItemType Directory -Path $serenaDir | Out-Null
}

$projectYml = Join-Path $serenaDir "project.yml"
if (Test-Path $projectYml) {
    Write-Host "  ⚠ project.yml already exists, skipping generation" -ForegroundColor Yellow
} else {
    uvx --from git+https://github.com/oraios/serena serena project generate-yml --language $Language $ProjectPath | Out-Null
    Write-Host "  ✓ Generated .serena/project.yml" -ForegroundColor Green
}

Write-Host ""

# Step 3: Index the project
if (!$SkipIndex) {
    Write-Host "[3/5] Indexing project for semantic search..." -ForegroundColor Yellow

    try {
        uvx --from git+https://github.com/oraios/serena serena project index $ProjectPath
        Write-Host "  ✓ Project indexed successfully" -ForegroundColor Green
    } catch {
        Write-Host "  ⚠ Indexing failed, but continuing..." -ForegroundColor Yellow
    }
} else {
    Write-Host "[3/5] Skipping indexing (-SkipIndex specified)" -ForegroundColor Yellow
}

Write-Host ""

# Step 4: Test Serena server startup
Write-Host "[4/5] Testing Serena MCP server..." -ForegroundColor Yellow

$testJob = Start-Job -ScriptBlock {
    param($projectPath)
    uvx --from git+https://github.com/oraios/serena serena start-mcp-server --context ide-assistant --project $projectPath
} -ArgumentList $ProjectPath

Start-Sleep -Seconds 5

if ($testJob.State -eq "Running") {
    Write-Host "  ✓ Serena MCP server started successfully" -ForegroundColor Green
    Write-Host "  ℹ Dashboard should be at: http://localhost:24282/dashboard/index.html" -ForegroundColor Cyan
    Stop-Job $testJob
    Remove-Job $testJob
} else {
    Write-Host "  ✗ Serena MCP server failed to start" -ForegroundColor Red
    Receive-Job $testJob
    Remove-Job $testJob
    exit 1
}

Write-Host ""

# Step 5: Generate MCP configuration
Write-Host "[5/5] Generating MCP configuration..." -ForegroundColor Yellow

$projectName = Split-Path $ProjectPath -Leaf
$absolutePath = (Resolve-Path $ProjectPath).Path

$mcpConfig = @{
    "mcpServers" = @{
        "serena-$projectName" = @{
            "command" = "uvx"
            "args" = @(
                "--from", "git+https://github.com/oraios/serena",
                "serena", "start-mcp-server",
                "--context", "ide-assistant",
                "--project", $absolutePath
            )
        }
    }
} | ConvertTo-Json -Depth 10

$mcpConfigPath = Join-Path $ProjectPath "serena-mcp-config.json"
$mcpConfig | Out-File -FilePath $mcpConfigPath -Encoding UTF8

Write-Host "  ✓ MCP configuration saved to: $mcpConfigPath" -ForegroundColor Green
Write-Host ""

# Summary
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Project: $projectName" -ForegroundColor White
Write-Host "Path: $absolutePath" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Add to Claude Code MCP configuration:" -ForegroundColor White
Write-Host "   - Open Claude Code settings" -ForegroundColor Gray
Write-Host "   - Add MCP server using config from: $mcpConfigPath" -ForegroundColor Gray
Write-Host ""
Write-Host "2. OR use Claude CLI (if available):" -ForegroundColor White
Write-Host "   cd `"$absolutePath`"" -ForegroundColor Gray
Write-Host "   claude mcp add serena-$projectName -- uvx --from git+https://github.com/oraios/serena \" -ForegroundColor Gray
Write-Host "     serena start-mcp-server --context ide-assistant --project `"`$(pwd)`"" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Restart Claude Code to activate Serena" -ForegroundColor White
Write-Host ""
Write-Host "Configuration saved at:" -ForegroundColor Yellow
Write-Host "  - Project config: $projectYml" -ForegroundColor Gray
Write-Host "  - MCP config: $mcpConfigPath" -ForegroundColor Gray
Write-Host ""
Write-Host "Dashboard (when MCP server is running):" -ForegroundColor Yellow
Write-Host "  http://localhost:24282/dashboard/index.html" -ForegroundColor Cyan
Write-Host ""

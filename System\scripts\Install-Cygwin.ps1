# Install-Cygwin.ps1
# Installs Cygwin with cygpath utility for Claude Code compatibility

Write-Host "=== Cygwin Installer ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "This will install real Cygwin with cygpath utility."
Write-Host ""

# Check for admin rights
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "⚠️  This script requires Administrator privileges." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Restarting as Administrator..."
    Start-Process powershell.exe -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
    exit
}

Write-Host "✅ Running as Administrator" -ForegroundColor Green
Write-Host ""

# Step 1: Download Cygwin
$setupPath = "$env:TEMP\cygwin-setup.exe"
Write-Host "[1/4] Downloading Cygwin installer..." -ForegroundColor Cyan

try {
    Invoke-WebRequest -Uri "https://www.cygwin.com/setup-x86_64.exe" -OutFile $setupPath -UseBasicParsing
    Write-Host "✅ Downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Download failed: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please download manually from: https://www.cygwin.com/setup-x86_64.exe"
    exit 1
}

Write-Host ""

# Step 2: Install Cygwin
Write-Host "[2/4] Installing Cygwin with coreutils package..." -ForegroundColor Cyan
Write-Host "This may take 2-3 minutes..." -ForegroundColor Yellow
Write-Host ""

try {
    $installArgs = @(
        "--quiet-mode",
        "--no-shortcuts",
        "--no-desktop",
        "--no-startmenu",
        "--root", "C:\cygwin64",
        "--site", "https://mirrors.kernel.org/sourceware/cygwin/",
        "--packages", "coreutils"
    )

    Start-Process -FilePath $setupPath -ArgumentList $installArgs -Wait -NoNewWindow
    Write-Host "✅ Cygwin installed to C:\cygwin64" -ForegroundColor Green
} catch {
    Write-Host "❌ Installation failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 3: Add to PATH
Write-Host "[3/4] Adding Cygwin to system PATH..." -ForegroundColor Cyan

try {
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

    if ($currentPath -notlike "*cygwin64\bin*") {
        $newPath = "$currentPath;C:\cygwin64\bin"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
        $env:PATH = "$env:PATH;C:\cygwin64\bin"
        Write-Host "✅ Added C:\cygwin64\bin to PATH" -ForegroundColor Green
    } else {
        Write-Host "✅ Cygwin already in PATH" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Could not update PATH automatically: $_" -ForegroundColor Yellow
    Write-Host "You may need to add C:\cygwin64\bin to PATH manually"
}

Write-Host ""

# Step 4: Verify installation
Write-Host "[4/4] Verifying cygpath installation..." -ForegroundColor Cyan

try {
    $testPath = "C:\Users\<USER>\file.txt"
    $result = & C:\cygwin64\bin\cygpath.exe -u $testPath 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test passed: $testPath -> $result" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎉 Cygwin installed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. Restart Claude Code"
        Write-Host "2. The cygpath errors should be gone"
    } else {
        Write-Host "⚠️  Cygpath test failed: $result" -ForegroundColor Yellow
        Write-Host "But installation completed - try restarting your terminal"
    }
} catch {
    Write-Host "⚠️  Could not test cygpath: $_" -ForegroundColor Yellow
    Write-Host "Installation may still be successful - restart your terminal and try:"
    Write-Host "  cygpath -u 'C:\test.txt'"
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# System Scripts

Automation scripts for the ReidCEO management system.

**📋 For Complete Inventories**:
- **All Scripts** (32 total): See `../SCRIPTS-AND-TOOLS-REGISTRY.md` - Comprehensive script inventory
- **All Tools** (Python, Node.js, etc.): See `../INSTALLED-TOOLS-INVENTORY.md` - Complete dependencies list

This README provides quick reference for common scripts. For detailed documentation, usage examples, dependencies, and cross-references to process guides, see the registries above.

---

## SharePoint Sync Scripts

### `Sync-All.ps1`
**Master sync script** - Complete SharePoint sync workflow:
- Updates SharePoint index
- Extracts content from new files
- Rebuilds keyword index
- Rebuilds semantic embeddings

**Usage:**
```powershell
.\Sync-All.ps1                    # Full sync
.\Sync-All.ps1 -IncrementalOnly   # Only new/changed files
.\Sync-All.ps1 -Department Marketing  # Single department
```

### `Sync-SharePointIndex.ps1`
**SharePoint indexer** - Scans OneDrive and creates file index:
- Creates portable index.json (committed to Git)
- Generates Windows shortcuts (.lnk files)
- Works across different machines

**Usage:**
```powershell
.\Sync-SharePointIndex.ps1              # Update index + create shortcuts
.\Sync-SharePointIndex.ps1 -UpdateIndex # Only update index
```

### Related Scripts
- `Extract-SharePointContent.ps1` - Extract PDF/DOCX/XLSX to markdown
- `Index-SharePointContent.ps1` - Build keyword search index
- `Generate-SemanticIndex.ps1` - Create AI embeddings for semantic search
- `Search-SharePointContent.ps1` - Keyword search
- `Search-SemanticContent.ps1` - AI-powered semantic search

---

## Confluence MCP Setup

### `Setup-ConfluenceMCP.ps1`
**Interactive setup wizard** for Confluence integration with Claude Code:
- Collects credentials (or uses team token)
- Creates credentials file (gitignored)
- Updates MCP server configuration
- Tests connection

**Quick Setup:**
```powershell
.\Setup-ConfluenceMCP.ps1
```

**Team Token Sharing:**
The Confluence API token can be shared within the Gain Servicing team. Team members can:
1. Copy the `confluence-credentials.json` file (gitignored)
2. Or run the setup script with the same credentials

**Configuration Files:**
- `confluence-credentials.json` - **Gitignored**, safe to share within team
  - URL: https://gainservicing.atlassian.net
  - Email: <EMAIL>
  - Token: [API token - contact IT or Jonathan]

**Documentation:**
- Full guide: `../SystemGuides/CONFLUENCE-MCP-SETUP-GUIDE.md`
- Quick ref: `../SystemGuides/CONFLUENCE-QUICK-SETUP.md`

---

## File Organization

**Committed to Git:**
- All `.ps1` scripts
- SharePointLinks/index.json (portable file catalog)

**Gitignored (local/machine-specific):**
- `*-log.txt` files
- `sync-config.json`
- `confluence-credentials.json` (but shareable within team)
- SharePointLinks/config.json (OneDrive path)
- SharePointLinks/**/*.lnk (shortcuts)

---

## Common Tasks

### Daily SharePoint sync
```powershell
cd C:\Projects\ReidCEO\System\scripts
.\Sync-All.ps1 -IncrementalOnly
```

### Search SharePoint content
```powershell
# Keyword search
.\Search-SharePointContent.ps1 -Query "budget"

# Semantic search
.\Search-SemanticContent.ps1 -Query "customer satisfaction metrics"
```

### Set up Confluence for new team member
```powershell
.\Setup-ConfluenceMCP.ps1
# Use team credentials when prompted
```

### Force full rebuild
```powershell
.\Sync-All.ps1  # Full sync (no -IncrementalOnly)
```

---

## Troubleshooting

**SharePoint sync issues:**
- Check OneDrive sync status (system tray icon)
- Verify path in `SharePointLinks/config.json`
- View logs: `*-log.txt` files

**Confluence MCP issues:**
- Check credentials: `confluence-credentials.json`
- Test connection: Run `Setup-ConfluenceMCP.ps1` again
- Verify packages: `pip install mcp-atlassian pydantic==2.11.0`

---

## Need Help?

- **SharePoint**: See `../SystemGuides/SHAREPOINT-SYNC-GUIDE.md`
- **Confluence**: See `../SystemGuides/CONFLUENCE-MCP-SETUP-GUIDE.md`
- **General**: See `../SystemGuides/README.md`

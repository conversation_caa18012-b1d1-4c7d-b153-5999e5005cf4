# Email-DailyBriefing.ps1
# Automated daily briefing email generation and sending

param(
    [string]$To = "",  # If not specified, opens draft for review
    [string]$Cc = "",
    [switch]$Send,  # Send immediately without review
    [switch]$Silent
)

<#
.SYNOPSIS
Generate and email daily executive briefing

.DESCRIPTION
Reads CEO-Dashboard/daily-briefing.md and generates formatted email.
By default, opens draft for review. Use -Send to send automatically.

.EXAMPLE
.\Email-DailyBriefing.ps1

Opens draft email with today's briefing for review.

.EXAMPLE
.\Email-DailyBriefing.ps1 -To "<EMAIL>" -Send

Sends daily briefing immediately to specified address.

.NOTES
Requires:
- CEO-Dashboard/daily-briefing.md must exist
- Send-TemplatedEmail.ps1
- EmailTemplates/daily-briefing.txt
#>

function Write-Log {
    param($Message, $Level = "INFO")
    if (-not $Silent) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

function Extract-Section {
    param($Content, $SectionHeader)

    # Find section start - match markdown header with section name, capture content until next header
    # Pattern explanation: Match ## Header, capture everything after until next ## or end of file
    $pattern = "(?ms)^##\s+.*?($SectionHeader).*?`$(.+?)(?=^##|\z)"
    if ($Content -match $pattern) {
        $result = $matches[2]
        if ($result) {
            return $result.Trim()
        }
    }
    return "(No content found)"
}

function Convert-MarkdownTableToHTML {
    param($Content)

    # Check if content contains a markdown table
    if ($Content -notmatch '\|.*\|') {
        return $Content
    }

    $lines = $Content -split "`r?`n"
    $result = @()
    $inTable = $false
    $tableRows = @()

    foreach ($line in $lines) {
        if ($line -match '^\s*\|.*\|\s*$') {
            # This is a table row
            if (-not $inTable) {
                $inTable = $true
                $tableRows = @()
            }
            $tableRows += $line
        }
        else {
            # Not a table row
            if ($inTable) {
                # End of table, convert accumulated rows
                $result += Convert-TableRows $tableRows
                $tableRows = @()
                $inTable = $false
            }
            $result += $line
        }
    }

    # Handle table at end of content
    if ($inTable) {
        $result += Convert-TableRows $tableRows
    }

    return $result -join "`r`n"
}

function Convert-TableRows {
    param($Rows)

    if ($Rows.Count -lt 2) {
        return $Rows -join "`r`n"
    }

    $html = @()
    $html += '<table style="border-collapse: collapse; width: 100%; margin: 10px 0;">'

    # First row is header
    $headerCells = ($Rows[0] -split '\|').Where({ $_.Trim() -ne '' })
    $html += '  <thead>'
    $html += '    <tr style="background-color: #f0f7ff;">'
    foreach ($cell in $headerCells) {
        $html += "      <th style=`"border: 1px solid #dee2e6; padding: 8px; text-align: left; color: #2c3e50;`">$($cell.Trim())</th>"
    }
    $html += '    </tr>'
    $html += '  </thead>'

    # Skip separator row (row 1), process data rows (2+)
    $html += '  <tbody>'
    for ($i = 2; $i -lt $Rows.Count; $i++) {
        $dataCells = ($Rows[$i] -split '\|').Where({ $_.Trim() -ne '' })
        $html += '    <tr>'
        foreach ($cell in $dataCells) {
            $html += "      <td style=`"border: 1px solid #dee2e6; padding: 8px;`">$($cell.Trim())</td>"
        }
        $html += '    </tr>'
    }
    $html += '  </tbody>'
    $html += '</table>'

    return $html -join "`r`n"
}

function Clean-Content {
    param($Content)

    if (-not $Content -or $Content -eq "(No content found)") {
        return $Content
    }

    # Remove common emojis (they don't render well in Outlook)
    $cleaned = $Content -replace '[^\x00-\x7F]+', ''

    # Convert markdown tables to HTML tables
    $cleaned = Convert-MarkdownTableToHTML $cleaned

    # Convert markdown bold to HTML
    $cleaned = $cleaned -replace '\*\*([^*]+)\*\*', '<strong>$1</strong>'

    # Convert markdown links to HTML
    $cleaned = $cleaned -replace '\[([^\]]+)\]\(([^)]+)\)', '<a href="$2">$1</a>'

    # Convert line breaks to <br> for better HTML display (but not inside tables)
    if ($cleaned -notmatch '<table') {
        $cleaned = $cleaned -replace "`r`n", "<br>`r`n"
    }

    return $cleaned
}

try {
    Write-Log "Starting daily briefing email generation..."

    # Locate daily briefing file
    $repoRoot = Split-Path (Split-Path $PSScriptRoot -Parent) -Parent
    $briefingFile = Join-Path $repoRoot "CEO-Dashboard\daily-briefing.md"

    if (-not (Test-Path $briefingFile)) {
        Write-Log "ERROR: Daily briefing file not found: $briefingFile" "ERROR"
        Write-Log "Please create CEO-Dashboard/daily-briefing.md first" "ERROR"
        exit 1
    }

    Write-Log "Reading briefing from: $briefingFile"
    $briefingContent = Get-Content $briefingFile -Raw

    # Extract sections
    Write-Log "Extracting sections..."

    Write-Log "Extracting KPI Summary..."
    $kpiSummary = Extract-Section $briefingContent "KEY METRICS SNAPSHOT|Key Metrics|KPI Summary|Metrics"

    Write-Log "Extracting Urgent Items..."
    $urgentItems = Extract-Section $briefingContent "CRITICAL ALERTS|HIGH PRIORITY ITEMS|Urgent Items|Critical Items|Urgent"

    Write-Log "Extracting Today's Priorities..."
    $todaysPriorities = Extract-Section $briefingContent "TOP 3 PRIORITIES FOR TODAY|Today's Priorities|Today|Priorities"

    Write-Log "Extracting Alerts..."
    $alerts = Extract-Section $briefingContent "OVERNIGHT UPDATES|Alerts|Notifications|Alerts & Notifications"

    Write-Log "Extracting Upcoming Items..."
    $upcomingItems = Extract-Section $briefingContent "TODAY'S SCHEDULE HIGHLIGHTS|Upcoming|This Week|Coming Up"

    Write-Log "Preparing template variables..."

    # Clean content (remove emojis, convert markdown to HTML)
    $kpiSummary = Clean-Content $kpiSummary
    $urgentItems = Clean-Content $urgentItems
    $todaysPriorities = Clean-Content $todaysPriorities
    $alerts = Clean-Content $alerts
    $upcomingItems = Clean-Content $upcomingItems

    # Prepare template variables
    $variables = @{
        to = if ($To) { $To } else { $env:USERNAME + "@gainservicing.com" }
        cc = if ($Cc) { $Cc } else { "" }
        kpi_summary = if ($kpiSummary) { $kpiSummary } else { "(No content)" }
        urgent_items = if ($urgentItems) { $urgentItems } else { "(No content)" }
        todays_priorities = if ($todaysPriorities) { $todaysPriorities } else { "(No content)" }
        alerts = if ($alerts) { $alerts } else { "(No content)" }
        upcoming_items = if ($upcomingItems) { $upcomingItems } else { "(No content)" }
    }

    # Build parameters for Send-TemplatedEmail
    $templateParams = @{
        Template = "daily-briefing-html"
        Variables = $variables
        Silent = $Silent
    }

    if ($Send) { $templateParams["Send"] = $true }

    # Generate and send email
    Write-Log "Generating email from template..."
    $templateScript = Join-Path $PSScriptRoot "Send-TemplatedEmail.ps1"

    if (-not (Test-Path $templateScript)) {
        Write-Log "ERROR: Send-TemplatedEmail.ps1 not found" "ERROR"
        exit 1
    }

    & $templateScript @templateParams

    if ($LASTEXITCODE -eq 0) {
        Write-Log "Daily briefing email generated successfully" "SUCCESS"
        exit 0
    }
    else {
        Write-Log "ERROR: Failed to generate email" "ERROR"
        exit 1
    }
}
catch {
    Write-Log "ERROR: Failed to generate daily briefing email" "ERROR"
    Write-Log "Error details: $($_.Exception.Message)" "ERROR"
    exit 1
}

<#
.SYNOPSIS
    Extract image content with AI-generated descriptions

.DESCRIPTION
    This script:
    1. Finds all PNG/JPG images in SharePoint index
    2. Extracts technical metadata (dimensions, EXIF, etc.)
    3. For AI descriptions: YOU (the user) should ask <PERSON> to analyze
       images and this script will store the descriptions

.PARAMETER Department
    Filter to specific department

.PARAMETER ImagePath
    Analyze a specific image file

.EXAMPLE
    .\Extract-ImageContent.ps1
    Process all images

.EXAMPLE
    .\Extract-ImageContent.ps1 -Department Marketing
    Process only Marketing images

.NOTES
    For AI descriptions, this script extracts metadata and creates
    placeholders. You should then ask <PERSON> Code:
    "Analyze all images in docs/sharepoint-content/ and add AI descriptions"
#>

[CmdletBinding()]
param(
    [string]$Department = "",
    [string]$ImagePath = ""
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$IndexFile = Join-Path $ProjectRoot "SharePointLinks\index.json"
$ConfigFile = Join-Path $ProjectRoot "SharePointLinks\config.json"
$OutputRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$LogFile = Join-Path $PSScriptRoot "extract-image-log.txt"
$MetadataScript = Join-Path $PSScriptRoot "extract_image_metadata.py"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== Image Content Extraction Started ==="

# Check if processing single image
if ($ImagePath) {
    Write-Log "Processing single image: $ImagePath"

    if (-not (Test-Path $ImagePath)) {
        Write-Log "Image not found: $ImagePath" "ERROR"
        exit 1
    }

    # Extract metadata
    $result = python $MetadataScript $ImagePath | ConvertFrom-Json

    if ($result.success) {
        Write-Log "Metadata extracted successfully"
        Write-Log "Dimensions: $($result.metadata.width) x $($result.metadata.height)"
        Write-Log "Format: $($result.metadata.format)"
        Write-Log "Size: $($result.metadata.file_size_kb) KB"

        # Display result
        $result.metadata | ConvertTo-Json -Depth 5
    } else {
        Write-Log "Failed: $($result.error)" "ERROR"
    }

    exit 0
}

# Load index
if (-not (Test-Path $IndexFile)) {
    Write-Log "Index file not found: $IndexFile" "ERROR"
    exit 1
}

$index = Get-Content $IndexFile -Raw -Encoding UTF8 | ConvertFrom-Json
$config = Get-Content $ConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json

# Filter to images
$imageFiles = $index.files | Where-Object { $_.extension -in @(".png", ".jpg", ".jpeg") }

if ($Department) {
    $imageFiles = $imageFiles | Where-Object { $_.department -eq $Department }
}

Write-Log "Found $($imageFiles.Count) image files to process"

if ($imageFiles.Count -eq 0) {
    Write-Log "No images found" "WARN"
    exit 0
}

$extractedCount = 0
$skippedCount = 0
$errorCount = 0

foreach ($fileInfo in $imageFiles) {
    # Build paths
    $sourcePath = Join-Path $config.oneDriveRoot $fileInfo.path
    $mdFileName = "$($fileInfo.department)_$($fileInfo.path -replace '\\', '_' -replace ' ', '_').md"
    $outputPath = Join-Path $OutputRoot $mdFileName

    # Skip if already extracted
    if (Test-Path $outputPath) {
        $skippedCount++
        continue
    }

    Write-Log "Extracting metadata: $($fileInfo.name)"

    # Check if source exists
    if (-not (Test-Path $sourcePath)) {
        Write-Log "  Source not found: $sourcePath" "WARN"
        $errorCount++
        continue
    }

    # Extract metadata
    try {
        $result = python $MetadataScript $sourcePath | ConvertFrom-Json

        if ($result.success) {
            $meta = $result.metadata

            # Create markdown with metadata
            $metadataSection = @"

## Image Metadata

- **Dimensions**: $($meta.width) x $($meta.height) pixels ($($meta.megapixels) MP)
- **Format**: $($meta.format)
- **Color Mode**: $($meta.mode)
- **File Size**: $($meta.file_size_kb) KB
- **Aspect Ratio**: $($meta.aspect_ratio)
"@

            if ($meta.exif) {
                $metadataSection += "`n`n### EXIF Data`n`n"
                foreach ($key in $meta.exif.PSObject.Properties.Name | Sort-Object) {
                    $value = $meta.exif.$key
                    $metadataSection += "- **$key**: $value`n"
                }
            }

            $markdown = @"
# $($fileInfo.name)

**Source**: ``$($fileInfo.path)``
**Department**: $($fileInfo.department)
**Type**: $($fileInfo.extension) (Image)
**Size**: $($fileInfo.sizeMB) MB
**Modified**: $($fileInfo.modified)
**Extracted**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
$metadataSection

---

## AI-Generated Description

[Description pending - Ask Claude Code to analyze this image]

**To get AI description**: Read this image file and update this markdown with a detailed description.

---

*Image metadata extracted*
*For AI description, ask Claude Code to analyze: ``$sourcePath``*
"@

            $markdown | Set-Content -Path $outputPath -Encoding UTF8
            Write-Log "  Extracted metadata → $mdFileName" "SUCCESS"
            $extractedCount++

        } else {
            Write-Log "  Failed: $($result.error)" "ERROR"
            $errorCount++
        }

    } catch {
        Write-Log "  Error: $($_.Exception.Message)" "ERROR"
        $errorCount++
    }
}

Write-Log "=== Image Extraction Complete ==="
Write-Log "Extracted: $extractedCount images"
Write-Log "Skipped: $skippedCount (already extracted)"
Write-Log "Errors: $errorCount"
Write-Log ""
Write-Log "NEXT STEP: Ask Claude Code to analyze images and add descriptions"
Write-Log "  Example: 'Analyze all images in docs/sharepoint-content/ and add AI descriptions'"

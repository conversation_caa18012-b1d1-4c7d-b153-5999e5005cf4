"""
Analyze image using Claude <PERSON> (vision capabilities)
Extracts:
1. AI-generated description of image content
2. Image metadata (dimensions, format, EXIF data)
"""

import sys
import json
from pathlib import Path
from PIL import Image
from PIL.ExifTags import TAGS
import base64

def get_image_metadata(image_path):
    """Extract technical metadata from image"""
    try:
        img = Image.open(image_path)

        metadata = {
            "format": img.format,
            "mode": img.mode,
            "width": img.width,
            "height": img.height,
            "size_pixels": f"{img.width}x{img.height}",
            "megapixels": round((img.width * img.height) / 1_000_000, 2)
        }

        # Extract EXIF data if available
        exif_data = {}
        if hasattr(img, '_getexif') and img._getexif():
            exif = img._getexif()
            for tag_id, value in exif.items():
                tag = TAGS.get(tag_id, tag_id)
                exif_data[tag] = str(value)

        if exif_data:
            metadata["exif"] = exif_data

        return metadata

    except Exception as e:
        return {"error": f"Failed to read metadata: {e}"}

def encode_image_base64(image_path):
    """Encode image as base64 for API"""
    with open(image_path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')

def analyze_image_with_claude(image_path):
    """
    Generate AI description of image using Claude API

    NOTE: This requires ANTHROPIC_API_KEY environment variable
    If not available, returns a placeholder description based on filename
    """
    try:
        import anthropic
        import os

        api_key = os.environ.get('ANTHROPIC_API_KEY')
        if not api_key:
            # Fallback: generate description from filename
            filename = Path(image_path).stem
            return {
                "description": f"Image file: {filename}",
                "note": "Full AI description requires ANTHROPIC_API_KEY"
            }

        client = anthropic.Anthropic(api_key=api_key)

        # Encode image
        image_data = encode_image_base64(image_path)
        file_ext = Path(image_path).suffix.lower()
        media_type = "image/jpeg" if file_ext in ['.jpg', '.jpeg'] else "image/png"

        # Call Claude API with vision
        message = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=500,
            messages=[{
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": media_type,
                            "data": image_data
                        }
                    },
                    {
                        "type": "text",
                        "text": """Analyze this image and provide:
1. Main subject/content (what is shown)
2. Any visible text (transcribe all text you see)
3. Type of image (photo, screenshot, graphic, chart, logo, etc.)
4. Key details that would help someone search for this image

Format as a searchable description that captures all important content."""
                    }
                ]
            }]
        )

        description = message.content[0].text

        return {
            "description": description,
            "ai_model": "claude-3-5-sonnet-20241022",
            "has_api": True
        }

    except ImportError:
        # anthropic library not installed
        filename = Path(image_path).stem
        return {
            "description": f"Image file: {filename}",
            "note": "Install 'anthropic' library for AI descriptions: pip install anthropic"
        }
    except Exception as e:
        filename = Path(image_path).stem
        return {
            "description": f"Image file: {filename}",
            "error": str(e)
        }

def main():
    if len(sys.argv) < 2:
        print(json.dumps({"success": False, "error": "No image path provided"}))
        sys.exit(1)

    image_path = sys.argv[1]

    try:
        # Get technical metadata
        metadata = get_image_metadata(image_path)

        # Get AI description
        ai_analysis = analyze_image_with_claude(image_path)

        # Combine results
        result = {
            "success": True,
            "metadata": metadata,
            "ai_description": ai_analysis.get("description", ""),
            "ai_model": ai_analysis.get("ai_model", "N/A"),
            "has_api": ai_analysis.get("has_api", False),
            "length": len(ai_analysis.get("description", "")),
            "note": ai_analysis.get("note", "")
        }

        print(json.dumps(result, indent=2))

    except Exception as e:
        print(json.dumps({
            "success": False,
            "error": str(e)
        }))
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env bash
# cygpath shim for Git Bash (non-Cygwin environments)
# Converts Windows paths to Unix-style paths and vice versa

usage() {
    echo "Usage: cygpath [-u|-w|-m] PATH"
    echo "  -u    Convert to Unix path (C:\path -> /c/path)"
    echo "  -w    Convert to Windows path (/c/path -> C:\path)"
    echo "  -m    Convert to mixed path (C:\path -> C:/path)"
    exit 1
}

if [ $# -eq 0 ]; then
    usage
fi

mode="unix"
path=""

while [ $# -gt 0 ]; do
    case "$1" in
        -u)
            mode="unix"
            shift
            ;;
        -w)
            mode="windows"
            shift
            ;;
        -m)
            mode="mixed"
            shift
            ;;
        -*)
            # Ignore other flags for compatibility
            shift
            ;;
        *)
            path="$1"
            shift
            ;;
    esac
done

if [ -z "$path" ]; then
    usage
fi

case "$mode" in
    unix)
        # Convert Windows path to Unix: C:\path -> /c/path
        echo "$path" | sed -e 's|\\|/|g' -e 's|^\([A-Za-z]\):|/\L\1|'
        ;;
    windows)
        # Convert Unix path to Windows: /c/path -> C:\path
        echo "$path" | sed -e 's|^/\([a-z]\)/|\U\1:/|' -e 's|/|\\|g'
        ;;
    mixed)
        # Convert to mixed: C:\path -> C:/path
        echo "$path" | sed 's|\\|/|g'
        ;;
esac

<#
.SYNOPSIS
    Simple one-command setup for SharePoint links

.DESCRIPTION
    Guides user through creating Windows shortcuts to SharePoint files.
    Much simpler than copying files - just creates tiny shortcut files.

.EXAMPLE
    .\Setup-SharePointLinks.ps1

.NOTES
    No admin rights needed for this script (just creates shortcuts)
#>

[CmdletBinding()]
param()

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)

# Banner
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "   ReidCEO - SharePoint Links Setup" -ForegroundColor Cyan
Write-Host "═══════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "This creates Windows shortcuts to your SharePoint files." -ForegroundColor White
Write-Host "Click any shortcut → opens the file from OneDrive!" -ForegroundColor White
Write-Host ""

# Step 1: OneDrive Sync Check
Write-Host "STEP 1: OneDrive SharePoint Sync" -ForegroundColor Cyan
Write-Host "═════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "First, you need to sync your SharePoint folder to OneDrive:" -ForegroundColor White
Write-Host ""
Write-Host "  1. Open your SharePoint link in browser" -ForegroundColor Yellow
Write-Host "     Example: https://appriver...sharepoint.com/sites/FDrive" -ForegroundColor Gray
Write-Host ""
Write-Host "  2. Navigate to the folder (Marketing, Finance, etc.)" -ForegroundColor Yellow
Write-Host ""
Write-Host "  3. Click the 'Sync' button at the top" -ForegroundColor Yellow
Write-Host "     (Cloud icon with circular arrow)" -ForegroundColor Gray
Write-Host ""
Write-Host "  4. OneDrive will open and start syncing" -ForegroundColor Yellow
Write-Host "     Wait for initial sync to complete" -ForegroundColor Gray
Write-Host ""
Write-Host "  5. Files will appear in:" -ForegroundColor Yellow
Write-Host "     C:\Users\<USER>\OneDrive - Gain Servicing\" -ForegroundColor Gray
Write-Host ""

$syncDone = Read-Host "Have you completed the OneDrive sync? (yes/no)"

if ($syncDone -notlike "y*") {
    Write-Host ""
    Write-Host "No problem! Complete the OneDrive sync first, then run this script again." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Quick steps:" -ForegroundColor Cyan
    Write-Host "  1. Open SharePoint in browser" -ForegroundColor Gray
    Write-Host "  2. Click 'Sync' button" -ForegroundColor Gray
    Write-Host "  3. Wait for sync to complete" -ForegroundColor Gray
    Write-Host "  4. Run this script again" -ForegroundColor Gray
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 0
}

Write-Host ""
Write-Host "✅ Great! OneDrive sync completed" -ForegroundColor Green
Write-Host ""

# Step 2: Create Shortcuts
Write-Host "STEP 2: Creating Shortcuts..." -ForegroundColor Cyan
Write-Host "═════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "This will scan your OneDrive folder and create shortcuts..." -ForegroundColor White
Write-Host ""

$shortcutScript = Join-Path $PSScriptRoot "Create-SharePointShortcuts.ps1"

if (-not (Test-Path $shortcutScript)) {
    Write-Host "❌ ERROR: Shortcut script not found at: $shortcutScript" -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    Write-Host "Running shortcut generator..." -ForegroundColor Yellow
    Write-Host ""

    & $shortcutScript

    Write-Host ""
    Write-Host "══════════════════════════════════════" -ForegroundColor Green
    Write-Host "✅ SUCCESS! Shortcuts Created" -ForegroundColor Green
    Write-Host "══════════════════════════════════════" -ForegroundColor Green
    Write-Host ""

    Write-Host "📁 What's been created:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  SharePointLinks/" -ForegroundColor White
    Write-Host "  ├── Marketing/      ← Shortcuts to Marketing files" -ForegroundColor Gray
    Write-Host "  ├── Finance/        ← Shortcuts to Finance files" -ForegroundColor Gray
    Write-Host "  ├── Legal/          ← Shortcuts to Legal files" -ForegroundColor Gray
    Write-Host "  └── ..." -ForegroundColor Gray
    Write-Host ""

    Write-Host "🎯 How to use:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  1. Browse to SharePointLinks/ folder" -ForegroundColor Yellow
    Write-Host "  2. Click any .lnk file" -ForegroundColor Yellow
    Write-Host "  3. File opens from OneDrive!" -ForegroundColor Yellow
    Write-Host ""

    Write-Host "💡 Benefits:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  ✅ Tiny file size (shortcuts only, ~2KB each)" -ForegroundColor Green
    Write-Host "  ✅ No file duplication" -ForegroundColor Green
    Write-Host "  ✅ Always up-to-date (opens from OneDrive)" -ForegroundColor Green
    Write-Host "  ✅ Works in File Explorer and VS Code" -ForegroundColor Green
    Write-Host ""

    Write-Host "🔄 To refresh shortcuts later:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  cd System\scripts" -ForegroundColor Gray
    Write-Host "  .\Create-SharePointShortcuts.ps1 -Refresh" -ForegroundColor Gray
    Write-Host ""

    Write-Host "📚 Documentation:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  SharePointLinks/README.md" -ForegroundColor Gray
    Write-Host ""

} catch {
    Write-Host ""
    Write-Host "❌ ERROR: Shortcut creation failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "  • OneDrive not syncing yet → Complete sync first" -ForegroundColor Gray
    Write-Host "  • No SharePoint folders found → Check OneDrive sync location" -ForegroundColor Gray
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "All done! 🎉" -ForegroundColor Green
Write-Host ""
Write-Host "Go explore SharePointLinks/ folder and click some shortcuts!" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to exit"

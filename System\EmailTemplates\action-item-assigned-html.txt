SUBJECT: Action Item Assigned: {{task_title}}
TO: {{assignee_email}}
CC: {{cc}}
FORMAT: HTML
---
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        h2 {
            color: #2c3e50;
            font-size: 18px;
            margin-top: 25px;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 4px solid #3498db;
        }
        p {
            margin: 10px 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .detail-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .detail-row {
            margin: 8px 0;
        }
        .content {
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        strong {
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <h1>Action Item Assigned</h1>

    <p>Hi {{assignee_name}},</p>

    <p>You have been assigned a new action item:</p>

    <div class="detail-box">
        <h2>TASK DETAILS</h2>
        <div class="detail-row"><strong>Task:</strong> {{task_title}}</div>
        <div class="detail-row"><strong>Priority:</strong> {{priority}}</div>
        <div class="detail-row"><strong>Deadline:</strong> {{deadline}}</div>
        <div class="detail-row"><strong>Assigned by:</strong> {{assigned_by}}</div>
    </div>

    <div class="section">
        <h2>DESCRIPTION</h2>
        <div class="content">{{task_description}}</div>
    </div>

    <div class="section">
        <h2>CONTEXT</h2>
        <div class="content">{{task_context}}</div>
    </div>

    <div class="section">
        <h2>NEXT STEPS</h2>
        <div class="content">{{next_steps}}</div>
    </div>

    <p>Please confirm receipt and let me know if you have any questions.</p>

    <div class="footer">
        <p>Full details: <code>{{task_link}}</code></p>
        <p><em>Generated by ReidCEO Management System</em></p>
    </div>
</body>
</html>

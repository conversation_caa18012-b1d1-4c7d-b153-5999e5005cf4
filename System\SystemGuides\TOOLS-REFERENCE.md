# ReidCEO - Tools & Environment Reference

**Purpose**: Master list of tools available in your VSCode/Cursor environment for working with SharePoint files and project tasks.

**Last Updated**: 2025-10-11

---

## 🖥️ Environment Overview

**Editor**: Visual Studio Code
**OS**: Windows 10/11
**Python**: 3.12.10
**Git**: Available (Git Bash + Git CLI)
**PowerShell**: 5.1+ (Windows PowerShell)

---

## 📁 SharePoint Content Tools

### 1. Content Extraction

**Purpose**: Extract text from PDF, DOCX, XLSX, PPTX files for searchability

**Script**: `System/scripts/Extract-SharePointContent.ps1`

**Usage**:
```powershell
cd System\scripts

# Extract all files
.\Extract-SharePointContent.ps1

# Extract only PDFs from Marketing
.\Extract-SharePointContent.ps1 -FileType pdf -Department Marketing

# List what would be extracted
.\Extract-SharePointContent.ps1 -ListOnly

# Force re-extraction
.\Extract-SharePointContent.ps1 -Force
```

**Output**: `docs/sharepoint-content/` (gitignored)
- Creates markdown files with extracted text
- Searchable via grep or Claude Code
- Preserves metadata (department, date, size)

**Requirements**:
- Python 3.12+ (✅ installed)
- Python libraries (auto-installed if missing):
  - PyPDF2 (PDF extraction)
  - python-docx (Word documents)
  - openpyxl (Excel spreadsheets)
  - python-pptx (PowerPoint presentations)

---

### 2. SharePoint Index Management

**Purpose**: Maintain portable catalog of SharePoint files with shortcuts

**Script**: `System/scripts/Sync-SharePointIndex.ps1`

**Usage**:
```powershell
cd System\scripts

# Update index + create shortcuts
.\Sync-SharePointIndex.ps1

# Only update index (commit this)
.\Sync-SharePointIndex.ps1 -UpdateIndex

# Only create shortcuts (from existing index)
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

**Output**:
- `SharePointLinks/index.json` (committed) - Portable catalog
- `SharePointLinks/config.json` (gitignored) - Your OneDrive path
- `SharePointLinks/**/*.lnk` (gitignored) - Windows shortcuts

**Features**:
- Auto-detects OneDrive SharePoint paths
- UTF-8 support for special characters (™, ®, etc.)
- Portable across different machines
- Works with "F Drive - {Department}" folder structure

---

## 🔍 Search & Query Tools

### 1. Grep (Content Search)

**Purpose**: Search through extracted content

**Built-in Tool**: Git Bash provides grep

**Usage**:
```bash
# Search all extracted content
grep -r "search term" docs/sharepoint-content/

# Case-insensitive search
grep -ri "quarterly report" docs/sharepoint-content/

# Search specific department
grep -r "budget" docs/sharepoint-content/ | grep Marketing

# Show context (3 lines before/after)
grep -r -C 3 "revenue" docs/sharepoint-content/

# Count matches
grep -rc "contract" docs/sharepoint-content/
```

**Claude Code Integration**:
Ask Claude to use Grep tool:
- "Search extracted content for mentions of Q4 goals"
- "Find all budget references in Marketing documents"
- "What documents mention customer acquisition?"

---

### 2. Index Search (Metadata)

**Purpose**: Search file metadata (names, dates, sizes, departments)

**File**: `SharePointLinks/index.json`

**Usage with Claude Code**:
Ask Claude to search the index:
- "What PDF files were modified after October 1st?"
- "List all presentations from Marketing"
- "Find files larger than 5MB"
- "What files contain 'strategy' in the filename?"

**Manual PowerShell Search**:
```powershell
# Load index
$index = Get-Content SharePointLinks\index.json -Raw -Encoding UTF8 | ConvertFrom-Json

# Search by name
$index.files | Where-Object { $_.name -like '*budget*' }

# Search by department
$index.files | Where-Object { $_.department -eq 'Marketing' }

# Search by date
$index.files | Where-Object { [DateTime]$_.modified -gt '2024-10-01' }

# Search by file type
$index.files | Where-Object { $_.extension -eq '.pdf' }
```

---

## 🤖 Claude Code Capabilities

### What Claude Can Do

**File Operations**:
- ✅ Read any file in the project
- ✅ Search through extracted content (Grep tool)
- ✅ Parse JSON (index.json) to find files
- ✅ Create summaries from extracted content
- ✅ Edit markdown files
- ✅ Write new scripts

**Content Analysis**:
- ✅ Analyze extracted text from documents
- ✅ Summarize key points
- ✅ Compare documents
- ✅ Extract specific information
- ✅ Create reports

**Search & Query**:
- ✅ Grep through extracted content
- ✅ Filter index.json by metadata
- ✅ Find patterns across multiple files
- ✅ Generate lists/tables of results

**Limitations**:
- ❌ Cannot directly read binary files (PDF, DOCX, etc.)
  - **Solution**: Use Extract-SharePointContent.ps1 first
- ❌ Cannot open Office files directly
  - **Solution**: Click `.lnk` shortcuts to open in native apps

---

## 📋 Workflow: Inspecting SharePoint Content

### ⚡ Recommended: Automated Full Sync

**Use the master sync script to update everything at once:**

```powershell
cd System\scripts

# Full automated sync (updates index, extraction, shortcuts, search)
.\Sync-All.ps1
```

**What happens:**
1. ✅ Updates SharePoint index (scans OneDrive for new files)
2. ✅ Extracts content (PDF/DOCX/XLSX/PPTX → searchable markdown)
3. ✅ Creates/updates Windows shortcuts
4. ✅ Rebuilds keyword index (inverted word index)
5. ✅ Rebuilds semantic embeddings (AI semantic search)

**Performance**: ~30 seconds for incremental updates, ~5 minutes for full sync

**Options**:
```powershell
# Sync only Marketing department
.\Sync-All.ps1 -Department Marketing

# Skip content extraction (faster)
.\Sync-All.ps1 -SkipExtraction

# Skip semantic embeddings (faster)
.\Sync-All.ps1 -SkipSemanticIndex
```

---

### Step 1: Extract Content (Manual Method)

```powershell
cd System\scripts

# Extract all files
.\Extract-SharePointContent.ps1

# Or extract specific department
.\Extract-SharePointContent.ps1 -Department Marketing
```

**What happens**:
- Script reads `SharePointLinks/index.json`
- Extracts text from each file using Python
- Creates markdown files in `docs/sharepoint-content/`
- Each markdown has full text + metadata

**Time**: ~1-2 seconds per file (5 minutes for 284 files)

---

### Step 2: Search/Query with Built-in Search Tools

**Option A: Fast Keyword Search** (30-50ms search time)
```powershell
# Search using inverted word index
.\Search-SharePointContent.ps1 -Query "customer acquisition strategy"

# Filter by department
.\Search-SharePointContent.ps1 -Query "budget" -Department Finance

# Limit results
.\Search-SharePointContent.ps1 -Query "revenue" -Top 5

# Show context snippets
.\Search-SharePointContent.ps1 -Query "quarterly report" -ShowContext
```

**Option B: Semantic AI Search** (understands meaning, not just keywords)
```powershell
# Find conceptually similar documents
.\Search-SemanticContent.ps1 -Query "improving customer satisfaction"

# Natural language queries work
.\Search-SemanticContent.ps1 -Query "how to acquire new clients"

# Filter by department
.\Search-SemanticContent.ps1 -Query "risk management" -Department Finance -ShowContext
```

**Option C: Ask Claude to Search**
```
Ask Claude:
"Search for documents about customer acquisition"
"Find all mentions of 'Q4 revenue' in Finance documents"
"What do our strategy documents say about lead generation?"
```

Claude can use the search scripts or search extracted content directly.

---

### Step 3: Deep Analysis

```
Ask Claude:
"Compare the 2023 and 2024 marketing budgets - what changed?"
"Extract all action items from Q4 strategy documents"
"Create a summary table of all awards we've won"
```

Claude will:
1. Search extracted content
2. Analyze relevant documents
3. Generate summary/report
4. Save to `docs/` if requested

---

## 🛠️ Python Tools (Available)

**Installed**: Python 3.12.10

**Libraries for Content Extraction**:
- `PyPDF2` - PDF text extraction
- `python-docx` - Word document parsing
- `openpyxl` - Excel spreadsheet reading
- `python-pptx` - PowerPoint presentation parsing

**Usage**: Automatically used by `Extract-SharePointContent.ps1`

**Manual Python Script** (if needed):
```python
# Extract from PDF
from PyPDF2 import PdfReader
reader = PdfReader('file.pdf')
text = '\n'.join([page.extract_text() for page in reader.pages])

# Extract from DOCX
from docx import Document
doc = Document('file.docx')
text = '\n'.join([para.text for para in doc.paragraphs])

# Extract from XLSX
from openpyxl import load_workbook
wb = load_workbook('file.xlsx')
sheet = wb.active
# Read cells...

# Extract from PPTX
from pptx import Presentation
prs = Presentation('file.pptx')
text = []
for slide in prs.slides:
    for shape in slide.shapes:
        if hasattr(shape, "text"):
            text.append(shape.text)
```

---

## 📝 Git Tools

**Available**: Git Bash + Git CLI

**Common Operations**:
```bash
# Status
git status

# Commit changes
git add .
git commit -m "message"
git push

# View history
git log --oneline

# Search Git history
git log --all --grep="search term"
```

**Claude Code Integration**:
Claude can create commits following the project's commit message format.

---

## 📦 File Structure Reference

```
ReidCEO/
├── SharePointLinks/              # Shortcuts to SharePoint files
│   ├── index.json               # Catalog (committed)
│   ├── config.json              # Your OneDrive path (gitignored)
│   ├── F Drive - Marketing/     # Shortcuts (gitignored)
│   └── README.md                # Auto-generated docs
│
├── docs/                         # Analysis & extracted content (gitignored)
│   └── sharepoint-content/      # Extracted text from files
│       ├── README.md            # Content index
│       └── Marketing_*.md       # Extracted content files
│
├── System/
│   ├── scripts/                 # Automation scripts
│   │   ├── Sync-SharePointIndex.ps1
│   │   ├── Extract-SharePointContent.ps1
│   │   └── extract_content.py
│   │
│   └── SystemGuides/            # Documentation (this file)
│       └── TOOLS-REFERENCE.md
│
└── CLAUDE.md                    # AI assistant guidelines
```

---

## 🎯 Quick Reference: Common Tasks

### Task: "Find all 2024 budget documents"

**Step 1**: Search metadata
```
Ask Claude: "What files have 'budget' in the name from 2024?"
```

**Step 2**: Open files
Click `.lnk` shortcuts in `SharePointLinks/` to open in native apps

**Step 3**: Extract content (if needed)
```powershell
.\Extract-SharePointContent.ps1 -Department Finance
```

**Step 4**: Analyze content
```
Ask Claude: "Summarize the key budget items from the extracted Finance documents"
```

---

### Task: "Create a report on Q4 marketing initiatives"

**Step 1**: Extract Marketing content
```powershell
.\Extract-SharePointContent.ps1 -Department Marketing
```

**Step 2**: Search for Q4 mentions
```
Ask Claude: "Search Marketing content for Q4 initiatives and create a summary report"
```

Claude will:
- Grep through `docs/sharepoint-content/Marketing_*`
- Extract relevant sections
- Create structured report
- Save to `docs/marketing-q4-report.md`

---

### Task: "Find the latest version of a specific document"

**Step 1**: Search index
```
Ask Claude: "Find the file 'Leadership Financials' and tell me when it was last modified"
```

**Step 2**: Open file
Navigate to `SharePointLinks/` and click the `.lnk` shortcut

---

## 💡 Tips & Best Practices

### 1. Extract Content Strategically
- Don't extract all 284 files at once initially
- Start with one department: `.\Extract-SharePointContent.ps1 -Department Marketing`
- Extract as-needed for specific projects

### 2. Use Claude for Complex Queries
- Simple search: Use Windows search on shortcuts
- Metadata search: Ask Claude to query index.json
- Content search: Ask Claude to Grep extracted content
- Analysis: Ask Claude to summarize/compare documents

### 3. Keep Extracted Content Fresh
- Re-extract after SharePoint files are updated
- Use `-Force` flag to re-extract: `.\Extract-SharePointContent.ps1 -Force`
- Check `modified` date in index.json

### 4. Organize Your Findings
- Save Claude's analysis to `docs/`
- Create summary documents for recurring needs
- Reference original files via shortcuts

### 5. Leverage Git
- Commit analysis/reports to `docs/` if valuable
- Don't commit extracted content (it's gitignored)
- Use branches for experimental analysis

---

## 🆘 Troubleshooting

### "Python library missing"
```powershell
pip install PyPDF2 python-docx openpyxl python-pptx
```

### "Cannot extract content from file"
- Check if file is corrupted
- Try opening with native app (click `.lnk` shortcut)
- Some PDFs are image-based (OCR needed)

### "Grep not found"
Use Git Bash or install grep for Windows:
```powershell
# In PowerShell, use Select-String instead
Get-ChildItem docs\sharepoint-content\*.md | Select-String "search term"
```

### "Extracted content is empty"
- Some files may be image-based (scanned PDFs)
- Encrypted/protected documents won't extract
- Empty presentations/documents return minimal content

---

## 📚 Additional Resources

- **SharePoint Setup**: `SETUP-SHAREPOINT-LINKS.md`
- **Project Guidelines**: `CLAUDE.md`
- **Script Documentation**: Comments in each `.ps1` file

---

**Questions?**
Ask Claude Code! This tool reference helps Claude understand what tools are available to assist you.

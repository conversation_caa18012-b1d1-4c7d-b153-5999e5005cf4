param(
    [Parameter(Mandatory=$true)]
    [string]$To,
    [Parameter(Mandatory=$true)]
    [string]$Subject,
    [Parameter(Mandatory=$true)]
    [string]$Body,
    [string]$Cc = "",
    [string]$Bcc = "",
    [string[]]$Attachments = @(),
    [ValidateSet("Text", "HTML")]
    [string]$BodyFormat = "Text",
    [ValidateSet("Normal", "High", "Low")]
    [string]$Importance = "Normal",
    [switch]$Send,
    [switch]$SaveToDrafts,
    [switch]$Silent
)

function Write-Log {
    param($Message, $Level = "INFO")
    if (-not $Silent) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

try {
    Write-Log "Starting email composition..."

    $Outlook = New-Object -ComObject Outlook.Application
    Write-Log "Outlook application connected"

    $Mail = $Outlook.CreateItem(0)

    $Mail.To = $To
    if ($Cc) { $Mail.CC = $Cc }
    if ($Bcc) { $Mail.BCC = $Bcc }

    Write-Log "Recipients: $To"

    $Mail.Subject = $Subject
    Write-Log "Subject: $Subject"

    if ($BodyFormat -eq "HTML") {
        $Mail.HTMLBody = $Body
    } else {
        $Mail.Body = $Body
    }

    $ImportanceMap = @{ "Low" = 0; "Normal" = 1; "High" = 2 }
    $Mail.Importance = $ImportanceMap[$Importance]

    if ($Attachments.Count -gt 0) {
        foreach ($attachment in $Attachments) {
            if (Test-Path $attachment) {
                $fullPath = Resolve-Path $attachment
                $Mail.Attachments.Add($fullPath.Path)
                Write-Log "Attached: $(Split-Path $fullPath -Leaf)"
            }
        }
    }

    if ($Send) {
        $Mail.Send()
        Write-Log "Email sent successfully" "SUCCESS"
    } elseif ($SaveToDrafts) {
        $Mail.Save()
        Write-Log "Email saved to Drafts" "SUCCESS"
    } else {
        $Mail.Display()
        Write-Log "Draft email opened" "SUCCESS"
    }

    if (-not $Silent) {
        Write-Host ""
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host "Email Summary:" -ForegroundColor Cyan
        Write-Host "  To: $To" -ForegroundColor White
        Write-Host "  Subject: $Subject" -ForegroundColor White
        if ($Send) {
            Write-Host "  Status: SENT" -ForegroundColor Green
        } else {
            Write-Host "  Status: Draft opened" -ForegroundColor Yellow
        }
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
    }
} catch {
    Write-Log "ERROR: $($_.Exception.Message)" "ERROR"
    throw
} finally {
    if ($Mail) {
        try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($Mail) | Out-Null } catch {}
    }
    if ($Outlook) {
        try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($Outlook) | Out-Null } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

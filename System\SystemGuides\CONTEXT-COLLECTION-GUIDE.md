# Business Context Collection Guide

**Version**: 1.0
**Last Updated**: 2025-10-28
**Purpose**: Permanent strategy for capturing and organizing business context in ReidCEO system

---

## Overview

This guide establishes a **permanent, codified strategy** for gathering and organizing business context. It solves the problem of haphazard knowledge capture by providing a clear three-tier system: Capture → Organize → Integrate.

**Key Principle**: *Capture freely, organize regularly, integrate systematically.*

---

## The Three-Tier System

### Tier 1: Raw Capture (Unstructured)
**Location**: `TempDocs/context-capture/` (gitignored)
**Purpose**: Fast, frictionless capture of business knowledge as you learn it
**Rule**: No organization required - just dump it here!

### Tier 2: Organized Context (Structured)
**Location**: `System/BusinessContext/` (committed to Git)
**Purpose**: Curated, organized business knowledge following templates
**Rule**: Must follow templates and naming conventions

### Tier 3: AI Integration (Searchable)
**Location**: Serena AI memories (`.serena/memories/`)
**Purpose**: Distilled knowledge for AI assistant consumption
**Rule**: Updated when Tier 2 context changes

---

## Workflow: How Context Moves Through Tiers

```
┌─────────────────────────────────────────────────────────────┐
│ 1. CAPTURE (Tier 1 - TempDocs/context-capture/)            │
│    - Meeting notes about product features                   │
│    - Conversation snippets with CFO about KPIs              │
│    - Quick research on competitors                          │
│    - "Today I learned..." notes                             │
│    → No structure, just capture fast!                       │
└─────────────────────────────────────────────────────────────┘
                            ↓
                    (Weekly/Monthly Review)
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ 2. ORGANIZE (Tier 2 - System/BusinessContext/)             │
│    - Sort raw captures into domains                         │
│    - Apply templates                                        │
│    - Create cross-references                                │
│    - Archive outdated content                               │
│    → Structured, version-controlled knowledge               │
└─────────────────────────────────────────────────────────────┘
                            ↓
                    (As Context Stabilizes)
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ 3. INTEGRATE (Tier 3 - Serena AI Memories)                 │
│    - Write Serena memory files                              │
│    - Link from CEO-Dashboard                                │
│    - Enable AI-powered search and assistance                │
│    → AI can now use this context to help you                │
└─────────────────────────────────────────────────────────────┘
```

---

## Tier 1: Raw Capture - Fast & Frictionless

### Purpose
Capture business context **immediately** without worrying about organization. The goal is **speed and completeness**, not structure.

### Location Structure
```
TempDocs/context-capture/
├── README.md
├── meetings/                    # Meeting notes
│   └── YYYY-MM-DD-meeting-topic.md
├── conversations/              # 1-on-1 conversations
│   └── YYYY-MM-DD-person-name.md
├── research/                   # Quick research
│   └── YYYY-MM-DD-research-topic.md
├── quick-notes/                # Random captures
│   └── YYYY-MM-DD-topic.md
└── audio-transcripts/          # Transcribed recordings
    └── YYYY-MM-DD-recording.md
```

### Capture Methods

#### 1. Meeting Notes
```markdown
# Meeting with [Person/Team] - YYYY-MM-DD

**Attendees**:
**Topic**:
**Date**: YYYY-MM-DD

## Key Takeaways
- [Bullet point]
- [Bullet point]

## Context Learned
- Business: [What you learned about the business]
- Products: [What you learned about products]
- Processes: [What you learned about operations]
- People: [What you learned about team/org]

## Action Items
- [ ] [Follow-up item]

## Raw Notes
[Everything else - don't organize, just capture]
```

#### 2. Conversation Notes
```markdown
# Conversation with [Name] - YYYY-MM-DD

**Person**: [Name, Title]
**Topic**:
**Date**: YYYY-MM-DD

## What I Learned
[Free-form notes - capture everything they said]

## Important Context
- [Key insights about the business]
- [Process details]
- [Terminology or jargon explained]

## Questions to Follow Up
- [Unanswered questions]
```

#### 3. Quick Research
```markdown
# Research: [Topic] - YYYY-MM-DD

**Source**: [Where you found this]
**Date**: YYYY-MM-DD

## Findings
[Everything you learned - links, quotes, analysis]

## Relevance to Gain Servicing
[How this applies to our business]

## Next Steps
[What to do with this information]
```

#### 4. "Today I Learned" Notes
```markdown
# TIL: [Topic] - YYYY-MM-DD

[Quick note about what you learned - could be one sentence or several paragraphs]

**Category**: Company | Product | Process | Strategy | People

**Source**: Meeting | Conversation | Document | Research
```

### Capture Guidelines

**DO**:
- ✅ Capture immediately while it's fresh
- ✅ Include date stamps (YYYY-MM-DD format)
- ✅ Use simple file names describing the topic
- ✅ Write in whatever format is fastest (bullets, paragraphs, stream-of-consciousness)
- ✅ Include sources (who told you, what document, etc.)
- ✅ Mark unclear items with [?] or [VERIFY]

**DON'T**:
- ❌ Worry about perfect organization
- ❌ Edit or polish before saving
- ❌ Try to fit it into the "right" category yet
- ❌ Delay capture to "do it properly" later
- ❌ Delete anything (even if it seems trivial)

### When to Capture

**Always capture after**:
- Executive team meetings
- 1-on-1 conversations with direct reports
- Sales calls or client meetings
- Strategy discussions
- Financial reviews
- Product demonstrations
- Any "lightbulb moment" about how the business works

---

## Tier 2: Organized Context - Structured & Curated

### Purpose
Transform raw captures into **organized, searchable business knowledge** that follows consistent templates and is version-controlled in Git.

### Location Structure
```
System/BusinessContext/
├── README.md                          # Navigation guide
├── CONTEXT-COLLECTION-GUIDE.md        # This file
├── _templates/                        # Copy these to start new docs
│   ├── company-context-template.md
│   ├── product-context-template.md
│   ├── department-context-template.md
│   └── process-context-template.md
│
├── Company/                           # Company-level context
│   ├── company-overview.md
│   ├── business-model.md
│   ├── industry-landscape.md
│   ├── competitive-analysis.md
│   ├── stakeholders.md
│   └── company-history.md
│
├── Products/                          # Product/service details
│   ├── products-overview.md
│   ├── pa-purchased.md
│   ├── express-funding.md
│   ├── pca-purchase-book.md
│   ├── servicing.md
│   └── product-economics.md
│
├── Operations/                        # How work gets done
│   ├── operations-overview.md
│   ├── underwriting-process.md
│   ├── collections-process.md
│   ├── case-value-prediction.md
│   ├── bottlenecks-analysis.md
│   └── workflow-diagrams.md
│
├── Departments/                       # Department-specific context
│   ├── finance-context.md
│   ├── legal-context.md
│   ├── sales-context.md
│   ├── marketing-context.md
│   └── technology-context.md
│
├── Strategy/                          # Strategic context
│   ├── strategic-objectives.md
│   ├── growth-strategy.md
│   ├── technology-roadmap-context.md
│   ├── market-opportunities.md
│   └── competitive-advantages.md
│
├── Glossary/                          # Terminology & definitions
│   ├── business-glossary.md
│   ├── acronyms.md
│   ├── kpi-definitions.md
│   └── product-terminology.md
│
└── _archive/                          # Outdated context
    └── YYYY-MM/
```

### Organization Process

#### Step 1: Review Raw Captures
**Frequency**: Weekly (light) + Monthly (comprehensive)

1. Open all new files in `TempDocs/context-capture/`
2. Read through and identify themes
3. Tag each capture with domain: Company | Product | Operation | Department | Strategy | Glossary

#### Step 2: Sort into Domains
For each raw capture, ask:
- **Company context?** → `System/BusinessContext/Company/`
- **Product details?** → `System/BusinessContext/Products/`
- **Process/workflow?** → `System/BusinessContext/Operations/`
- **Department-specific?** → `System/BusinessContext/Departments/`
- **Strategic insight?** → `System/BusinessContext/Strategy/`
- **Definition/term?** → `System/BusinessContext/Glossary/`

#### Step 3: Apply Templates
Use templates from `_templates/` to structure the information:

**Standard File Header**:
```markdown
# [Title]

**Last Updated**: YYYY-MM-DD
**Owner**: [Who maintains this - usually CEO or department head]
**Status**: Draft | Active | Review Needed | Outdated
**Related Context**:
- [Link to related context file]
- [Link to related context file]

---

## Quick Summary
[2-3 sentence summary of this context]

---

[Rest of content following template...]
```

#### Step 4: Create Cross-References
Link related context files:
- Products reference Operations (e.g., "How PA products are underwritten")
- Departments reference Processes (e.g., "Finance runs FBR process")
- Strategy references Products and Company (e.g., "Growth strategy for Express Funding")

#### Step 5: Update Status
Mark files as:
- **Draft**: Still gathering information
- **Active**: Current and complete
- **Review Needed**: May be outdated, needs verification
- **Outdated**: No longer accurate (move to `_archive/`)

### Naming Conventions

**Files**:
- Use `kebab-case.md` (all lowercase, hyphens between words)
- Examples: `business-model.md`, `underwriting-process.md`, `competitive-analysis.md`

**Sections**:
- Use `## Title Case for Main Sections`
- Use `### Sentence case for subsections`

**Dates**:
- Always use `YYYY-MM-DD` format
- Example: `2025-10-28`

**Status Tags**:
- Use: `Draft | Active | Review Needed | Outdated`
- Don't invent new status tags

### Quality Standards

**Organized context must be**:
1. **Clear**: Anyone should understand it without you explaining
2. **Complete**: Covers the topic thoroughly
3. **Current**: Information is up-to-date and accurate
4. **Connected**: Links to related context files
5. **Sourced**: Notes where information came from (if relevant)

**Before committing organized context, verify**:
- [ ] File follows appropriate template
- [ ] Header is complete (Last Updated, Owner, Status, Related)
- [ ] Quick summary is present
- [ ] Cross-references to related files exist
- [ ] Terminology is consistent with Glossary
- [ ] Information is current and accurate

---

## Tier 3: AI Integration - Make Context Searchable

### Purpose
Enable AI assistants (like Serena and Claude) to **use your business context** to provide better assistance.

### Serena Memory Files
Serena uses memory files in `.serena/memories/` to understand the codebase and business.

**When to write Serena memories**:
- After organizing major context domains (Company, Products, Operations)
- When onboarding the project for the first time
- When business model or products change significantly
- Quarterly refresh to keep AI assistance current

**Memory file naming**:
- `project_overview.md` - High-level business summary
- `business_model.md` - How Gain Servicing makes money
- `products_services.md` - Product details
- `tech_stack.md` - Technology architecture
- `development_guidelines.md` - Coding standards (for scripts)

**Memory file format**:
```markdown
# [Topic]

[2-3 paragraph summary of key information AI needs to know]

## Key Points
- [Important bullet]
- [Important bullet]

## When This Matters
[Explain when AI assistant needs to use this context]

## Related Files
[Paths to relevant organized context files]
```

### CEO-Dashboard Integration
Link organized context from CEO Dashboard for quick access:

**Create**: `CEO-Dashboard/business-context-shortcuts.md`
```markdown
# Business Context Quick Links

## Company
- [Business Model](../System/BusinessContext/Company/business-model.md)
- [Competitive Analysis](../System/BusinessContext/Company/competitive-analysis.md)

## Products
- [Products Overview](../System/BusinessContext/Products/products-overview.md)
- [PA Purchased Details](../System/BusinessContext/Products/pa-purchased.md)

## Strategy
- [Strategic Objectives](../System/BusinessContext/Strategy/strategic-objectives.md)
- [Growth Strategy](../System/BusinessContext/Strategy/growth-strategy.md)

## Glossary
- [Business Glossary](../System/BusinessContext/Glossary/business-glossary.md)
- [KPI Definitions](../System/BusinessContext/Glossary/kpi-definitions.md)
```

---

## Review & Maintenance Schedule

### Weekly Review (15-30 minutes)
**Every Friday or Monday**:
1. Review `TempDocs/context-capture/` for the week
2. Identify urgent context needing immediate organization
3. Sort into domains (don't need to fully organize yet)
4. Mark any context requiring follow-up or verification

### Monthly Organization (1-2 hours)
**First Friday of each month**:
1. Review all raw captures from the past month
2. Fully organize into `System/BusinessContext/`
3. Apply templates and create cross-references
4. Update "Last Updated" dates on modified files
5. Archive outdated context to `_archive/YYYY-MM/`
6. Commit organized context to Git

### Quarterly Refresh (2-3 hours)
**Start of each quarter**:
1. Review all organized context for accuracy
2. Update status tags (Draft → Active, Active → Review Needed, etc.)
3. Refresh Serena memory files with current context
4. Update CEO-Dashboard shortcuts
5. Create quarterly context summary for executive team
6. Identify gaps in context coverage

---

## Common Scenarios

### Scenario 1: Learning About a New Product
**Capture (Tier 1)**:
```
TempDocs/context-capture/meetings/2025-10-28-cfo-express-funding-deep-dive.md

# CFO Deep Dive on Express Funding

CFO explained how Express Funding works:
- We buy cases from attorneys at discount
- Attorneys get cash upfront (30-60 days)
- We collect full amount when case settles
- Typical discount: 10-15%
- Average case value: $50K
- Settlement timeframe: 6-18 months
- Default rate: ~5%

Revenue model:
- Fee charged: discount % × case value
- Example: $50K case, 12% discount = $6K revenue

Risks:
- Case doesn't settle (rare)
- Case settles for less than expected
- Attorney defaults (very rare with our vetting)

Next: Need to understand underwriting process for Express Funding
```

**Organize (Tier 2)**:
- Create/update `System/BusinessContext/Products/express-funding.md`
- Follow product template
- Add revenue model section
- Add risk factors section
- Cross-reference to `Operations/underwriting-process.md`

**Integrate (Tier 3)**:
- Update Serena memory `products_services.md` with Express Funding details
- Add to `CEO-Dashboard/business-context-shortcuts.md`

---

### Scenario 2: Understanding a Key Process
**Capture (Tier 1)**:
```
TempDocs/context-capture/conversations/2025-10-28-cto-underwriting.md

# Conversation with CTO about Underwriting

Current underwriting process:
1. Attorney submits case via portal
2. Operations team reviews case documents
3. Manual value estimation (takes 2-4 hours)
4. Underwriter approval (Senior Underwriter for >$100K)
5. Offer sent to attorney
6. If accepted, funding within 48 hours

Pain points:
- Manual value estimation is slow
- Relies on senior underwriter expertise
- Hard to scale
- Inconsistent valuation methods

AI opportunity:
- Case value prediction model
- Train on 5 years of historical data
- Could reduce review time to 15 minutes
- More consistent valuations
- Allow junior staff to handle more cases

CTO says project is 60% complete, launch target Q1 2026
```

**Organize (Tier 2)**:
- Create/update `System/BusinessContext/Operations/underwriting-process.md`
- Add current process workflow
- Add pain points section
- Create `System/BusinessContext/Strategy/technology-roadmap-context.md`
- Add AI case value prediction details
- Cross-reference between Operations and Strategy

**Integrate (Tier 3)**:
- Update Serena memory with underwriting process details
- Add to `Technology/ai-initiatives/case-value-prediction/` for operational tracking

---

### Scenario 3: Quick Terminology Capture
**Capture (Tier 1)**:
```
TempDocs/context-capture/quick-notes/2025-10-28-terminology.md

# Terminology I learned today

LFBD = Letters From Borrowers' Doctors
- Legal term for medical documentation in PI cases
- Used to establish medical damages
- Required for case valuation

PA = Personal Injury Attorney
- Also called "PI Attorney"
- Main customer segment

PCA = Pre-settlement Case Advances
- Also called "lawsuit funding" or "litigation financing"
- We advance money to plaintiff while case pending
```

**Organize (Tier 2)**:
- Add to `System/BusinessContext/Glossary/acronyms.md`
- Add to `System/BusinessContext/Glossary/business-glossary.md`
- Update `System/BusinessContext/Glossary/product-terminology.md`

**Integrate (Tier 3)**:
- Serena memory already has glossary, update if needed
- Add to quick reference in CEO-Dashboard

---

## Tips for Success

### For CEO (Reid)
1. **Capture everything** - Don't filter, just save it
2. **Use voice memos** - Transcribe later into TempDocs
3. **End-of-day brain dump** - 5 minutes to capture what you learned
4. **Delegate organization** - Assistant can organize raw captures monthly
5. **Review organized context** - You verify accuracy, assistant does the work

### For AI Assistants (Claude/Serena)
1. **Proactively organize** - When CEO shares context, offer to organize it
2. **Suggest templates** - Point to appropriate template for the context
3. **Create cross-references** - Link related context automatically
4. **Update memories** - Keep Serena memories in sync with organized context
5. **Alert on gaps** - Identify missing context domains

### For Executive Assistant
1. **Meeting notes → Tier 1** - Capture meeting notes immediately after calls
2. **Weekly sort** - Quick 15-minute triage of raw captures
3. **Monthly organization** - Full organization session
4. **Verify with CEO** - Confirm accuracy before marking "Active"
5. **Archive old content** - Keep system clean

---

## Context Conflict Detection & Resolution

**Critical Principle**: Business context changes over time. Bad information can creep in. Conflicts between sources must be detected and resolved proactively.

### Why This Matters

**Common Scenarios**:
- New information contradicts existing documented context
- Different sources (meetings, documents, conversations) provide conflicting data
- Business processes change but documentation lags behind
- Numbers/metrics change (KPI targets, product economics, team sizes)
- Strategic priorities shift but old context remains "Active"

**Without Conflict Detection**:
- ❌ AI assistants work with outdated information
- ❌ CEO makes decisions based on incorrect context
- ❌ Team confusion from conflicting documentation
- ❌ Loss of trust in the context system

**With Proactive Conflict Detection**:
- ✅ Context stays current and accurate
- ✅ Conflicts resolved before causing problems
- ✅ CEO informed of important changes
- ✅ Documentation trusted as single source of truth

---

### How Conflict Detection Works

#### Automatic Triggers (AI Assistant Role)

**When organizing new context** (Tier 1 → Tier 2):
AI assistant MUST check for conflicts before creating/updating organized context:

1. **Search existing context** for related information
2. **Compare new vs. existing** data points
3. **Identify conflicts** (contradictions, discrepancies)
4. **Investigate** using available sources
5. **Resolve or escalate** to CEO

#### Types of Conflicts to Detect

**1. Factual Conflicts**
- **Example**: New capture says "5 products" but existing context documents 4 products
- **Detection**: Compare specific claims (numbers, names, dates)
- **Resolution Path**: Verify with authoritative source (CEO, department head, official docs)

**2. Process Conflicts**
- **Example**: New meeting notes describe different underwriting steps than documented process
- **Detection**: Compare workflow descriptions
- **Resolution Path**: Confirm current process (may have changed)

**3. Metric Conflicts**
- **Example**: New context says "Average case $75K" but existing says "$50K"
- **Detection**: Compare numerical values
- **Resolution Path**: Check recent dashboards, ask CFO for current figures

**4. Strategic Conflicts**
- **Example**: New priorities contradict documented strategic objectives
- **Detection**: Compare strategic statements
- **Resolution Path**: CEO confirms current strategy (priorities evolve)

**5. Definitional Conflicts**
- **Example**: "PA" used differently in new vs. existing context
- **Detection**: Compare terminology usage
- **Resolution Path**: Clarify definitions, update glossary

**6. Date/Timeline Conflicts**
- **Example**: Project launch date differs across sources
- **Detection**: Compare dates and milestones
- **Resolution Path**: Verify with project owner

---

### Conflict Resolution Protocol

#### Step 1: Detect the Conflict

**AI Assistant Must**:
- Identify specific conflicting statements
- Note sources of each version
- Assess severity (critical vs. minor)

**Example Detection**:
```
CONFLICT DETECTED:

Existing Context (products-overview.md, updated 2025-10-15):
"Express Funding average case size: $50K"

New Capture (cfo-meeting-2025-10-28.md):
"CFO said Express Funding average case now $75K"

Source Dates: Existing (13 days old) vs. New (today)
```

#### Step 2: Investigate Proactively

**AI Assistant Must**:
1. **Search for additional sources**:
   - Check other context files
   - Search SharePoint documents (if available)
   - Review recent captures in TempDocs
   - Check KEY-DOCUMENTS-REGISTRY for authoritative sources

2. **Assess currency**:
   - Which source is more recent?
   - Has "Last Updated" date lapsed?
   - Is this a known changing metric?

3. **Evaluate authority**:
   - Which source is more authoritative? (CEO > Department Head > Team Member)
   - Is this official data vs. estimate?
   - Is there a dashboard or report that's definitive?

**Example Investigation**:
```
INVESTIGATION:

Additional Sources Found:
- Finance/README.md mentions "$50K average" (no date)
- KEY-DOCUMENTS-REGISTRY: "Express Funding Dashboard" exists (monthly updates)

Assessment:
- New source (CFO) is more authoritative than old doc
- CFO statement is from today (fresher)
- Express Funding Dashboard would be definitive source

Recommendation:
- Update context to $75K with [VERIFY from Dashboard]
- Mark old $50K as outdated
- CEO should confirm with CFO or check Dashboard
```

#### Step 3: Resolve or Escalate

**Resolution Paths**:

**Path A: Clear Resolution (High Confidence)**
AI can resolve automatically when:
- New source is clearly more authoritative AND recent
- Conflict is factual and verifiable
- Change is expected (e.g., updated metrics, team sizes)

**AI Action**:
1. Update context with new information
2. Mark old information as outdated
3. Add "[Updated: YYYY-MM-DD - Source: X]" notation
4. Inform CEO in summary: "Context conflict resolved: [brief description]"

**Example Resolution Message**:
```markdown
✅ CONTEXT CONFLICT RESOLVED

**Topic**: Express Funding average case size
**Conflict**: $50K (old docs) vs. $75K (CFO today)
**Resolution**: Updated to $75K based on CFO statement (2025-10-28)
**Action**: Updated products-overview.md
**Verification**: Recommend CEO check Express Funding Dashboard to confirm

Updated: System/BusinessContext/Products/products-overview.md
```

---

**Path B: Uncertain Resolution (Ask CEO)**
AI must escalate to CEO when:
- Sources equally authoritative but contradictory
- Conflict involves strategic direction
- Potential significant business impact
- Unable to verify which source is correct
- Definition/terminology ambiguity

**AI Action**:
1. Do NOT update context yet
2. Document the conflict clearly
3. Present investigation findings to CEO
4. Ask CEO to resolve

**Example Escalation Message**:
```markdown
⚠️ CONTEXT CONFLICT DETECTED - CEO INPUT NEEDED

**Topic**: Definition of "PA" product

**Conflict**:
- Source A (products-overview.md): "PA = Purchased products"
- Source B (acronyms.md): "PA = Personal Injury Attorney (customer type)"
- Source C (CFO meeting notes): Used interchangeably for both

**Investigation**:
- Term used inconsistently across 5 documents
- Affects understanding of product portfolio
- No definitive source found

**Question for CEO**:
Is "PA" referring to:
1. A product line (Purchased products)
2. A customer type (Personal Injury Attorney)
3. Both (context-dependent)

**Recommendation**:
- Clarify definition
- Update glossary
- Update all affected context files
- Standardize usage going forward

**Impact**: Affects product documentation, KPI tracking, sales materials
```

---

**Path C: Context Evolution (Strategic Change)**
When conflict indicates business has evolved:

**AI Action**:
1. Recognize this is intentional change, not error
2. Archive old context to `_archive/YYYY-MM/`
3. Create new context with current information
4. Notify CEO: "Business context updated to reflect change in [X]"

**Example Evolution Message**:
```markdown
📊 BUSINESS CONTEXT EVOLVED

**Topic**: Strategic priorities for Q4 2025

**Change Detected**:
- Previous: "5 strategic priorities" (documented Q3)
- Current: "3 strategic priorities" (CEO meeting 2025-10-28)

**Assessment**:
Strategic priorities have been refined (not a conflict, but evolution)

**Action Taken**:
- Archived old strategic-objectives.md to _archive/2025-10/
- Created new strategic-objectives.md with current priorities
- Updated cross-references in related context files

**CEO Notification**: Strategic priorities documentation updated to reflect Q4 focus
```

---

### Conflict Prevention

#### Best Practices for Capture

**Include Source Attribution**:
```markdown
## Revenue Model
- Average case size: $75K
- **Source**: CFO (2025-10-28)
- **Verify**: Check Express Funding Dashboard
```

**Mark Uncertainty**:
```markdown
- Default rate: ~5% [ESTIMATE - needs CFO confirmation]
- Team size: 25-30 people [APPROXIMATE - get exact from HR]
```

**Note Context**:
```markdown
- Average settlement time: 6-18 months
- **Context**: This varies significantly by case type
- **Note**: Attorney cases faster than medical provider cases
```

#### Maintenance Tags

Use status tags to signal review needs:

- **[VERIFY]** - Needs confirmation from authoritative source
- **[ESTIMATE]** - Rough approximation, not precise data
- **[AS OF DATE]** - Time-sensitive information
- **[DEPRECATED]** - Old information kept for historical context

**Example**:
```markdown
## Product Economics [AS OF 2025-10-28]
- Average case size: $75K [VERIFY with Dashboard]
- Default rate: ~5% [ESTIMATE from CFO conversation]
```

---

### AI Assistant Conflict Detection Workflow

**Every time AI organizes context** (Tier 1 → Tier 2):

```
1. New context received
   ↓
2. Search existing organized context for related information
   ↓
3. Compare for conflicts
   ↓
   ├─ No conflict? → Proceed with organization
   │
   └─ Conflict detected? → PAUSE
       ↓
   4. Investigate proactively:
      - Search additional sources
      - Assess currency and authority
      - Evaluate severity
       ↓
   5. Resolution decision:
      ├─ High confidence? → Resolve automatically + Inform CEO
      ├─ Uncertain? → Escalate to CEO with investigation
      └─ Evolution? → Archive old + Update new + Notify CEO
```

**Mandatory Check Points**:
- ✅ Before updating ANY existing context file
- ✅ When new capture contradicts Serena memory
- ✅ When organizing captures with specific claims (numbers, dates, names)
- ✅ During monthly review of "Active" context files

---

### Conflict Resolution Templates

#### Template 1: Resolved Conflict Notification
```markdown
## Context Conflict Resolved

**Date**: YYYY-MM-DD
**Topic**: [Brief description]

**Conflict**:
- Old: [Previous information and source]
- New: [New information and source]

**Resolution**:
- Updated to: [Resolved information]
- Reasoning: [Why this resolution was chosen]

**Action Taken**:
- Updated: [File path]
- Marked outdated: [If applicable]

**Verification Recommended**: [If CEO should double-check]
```

#### Template 2: Conflict Escalation to CEO
```markdown
## Context Conflict - CEO Input Needed

**Date**: YYYY-MM-DD
**Priority**: Critical | High | Normal
**Topic**: [What the conflict is about]

**Conflicting Information**:

Source A: [Information + who/where/when]
Source B: [Information + who/where/when]
[Additional sources if relevant]

**Investigation Summary**:
- [What I searched]
- [What I found]
- [Why I cannot resolve]

**Questions for CEO**:
1. [Specific question]
2. [Specific question]

**Impact if Unresolved**: [Why this matters]

**Recommended Next Steps**: [Suggestion]
```

#### Template 3: Context Evolution Notice
```markdown
## Business Context Updated

**Date**: YYYY-MM-DD
**Topic**: [What changed]

**Previous Context**: [Old information]
**Current Context**: [New information]

**Change Type**: Strategic Shift | Process Change | Updated Metrics | Clarification

**Reason for Change**: [Why context evolved]

**Files Updated**:
- [File path]
- [File path]

**Archived**: [Old context location in _archive/]
```

---

### CEO's Role in Conflict Resolution

**What CEO Should Expect**:

1. **Automatic Resolutions** - Informed of minor conflicts AI resolved
   - Review in weekly/monthly summary
   - Verify important changes
   - Raise concerns if resolution seems wrong

2. **Escalations** - Active decision needed for uncertain conflicts
   - Respond to conflict questions
   - Provide authoritative answer
   - Clarify ambiguous situations

3. **Evolution Notices** - Business changes documented
   - Confirm major strategic shifts are captured correctly
   - Ensure team understands changes

**CEO Response Time**:
- **Critical conflicts**: Same day (affects decisions)
- **High priority**: Within 3 days
- **Normal conflicts**: During next weekly review

---

### Quarterly Context Audit

**Every quarter, perform systematic conflict check**:

1. **Review all "Active" context files**
   - Check "Last Updated" dates (>3 months old?)
   - Verify key facts still accurate
   - Update or mark "Review Needed"

2. **Cross-reference key metrics**
   - Compare numbers across context files
   - Check against latest dashboards
   - Resolve any discrepancies

3. **Validate terminology**
   - Ensure glossary matches usage
   - Update definitions if they've evolved
   - Standardize inconsistent usage

4. **Archive outdated context**
   - Move outdated files to `_archive/YYYY-QQ/`
   - Preserve for historical reference
   - Update cross-references

---

### Success Indicators

**Conflict detection is working when**:

✅ **Conflicts caught early** - Before causing confusion or wrong decisions
✅ **Context stays accurate** - Regular updates keep information current
✅ **CEO informed** - Aware of changes and conflicts
✅ **Trust maintained** - Context system is reliable single source of truth
✅ **Minimal CEO burden** - Most conflicts auto-resolved, only important ones escalated

---

## Context Gap Tracking & Open Items

**Critical Principle**: Systematically track what we DON'T know, not just what we do know. Gaps, ambiguities, and unanswered questions must be organized and prioritized for resolution.

### Why Gap Tracking Matters

**Without Systematic Gap Tracking**:
- ❌ Important questions remain unanswered indefinitely
- ❌ CEO doesn't know what context is missing
- ❌ Random resolution (whoever asks first gets answered)
- ❌ Critical gaps might block decisions
- ❌ No visibility into context system completeness

**With Systematic Gap Tracking**:
- ✅ Clear visibility into what's known vs. unknown
- ✅ Prioritized resolution (critical gaps first)
- ✅ CEO can batch-answer related questions
- ✅ Progress visible over time
- ✅ AI assistants know when context is incomplete

---

### Gap Tracking System

#### Central Tracker
**Location**: `System/BusinessContext/context-gaps-tracker.md`

**Purpose**: Executive summary of all open items across domains

**Contents**:
- Total open items count by domain
- Critical items requiring immediate action
- High-priority items for batch resolution
- Domain coverage assessment (% complete)
- Recently resolved items
- Quarterly gap analysis

#### Domain-Specific Trackers
**Locations**: Each domain has own open items file
- `Company/company-open-items.md`
- `Products/products-open-items.md`
- `Operations/operations-open-items.md`
- `Departments/departments-open-items.md`
- `Strategy/strategy-open-items.md`
- `Glossary/glossary-open-items.md`

**Purpose**: Detailed tracking of gaps in each domain

**Contents**:
- All open questions/gaps in that domain
- Priority (Critical/High/Normal)
- Type (Missing/Incomplete/Ambiguous/Unverified/Outdated/Conflicting)
- Investigation notes
- Resolution path
- Assignment and target date

---

### Types of Gaps to Track

**1. Missing Information**
- **Definition**: Question has no answer yet
- **Example**: "What is total team size?"
- **Tag**: [MISSING]

**2. Incomplete Information**
- **Definition**: Partial answer, needs more detail
- **Example**: "Know 4 products exist, need details on each"
- **Tag**: [INCOMPLETE]

**3. Ambiguous Information**
- **Definition**: Multiple interpretations possible
- **Example**: "PA" used inconsistently
- **Tag**: [AMBIGUOUS]

**4. Unverified Information**
- **Definition**: Information captured but not confirmed
- **Example**: "~5% default rate [ESTIMATE]"
- **Tag**: [VERIFY] or [ESTIMATE]

**5. Outdated Information**
- **Definition**: May be stale, needs refresh
- **Example**: Context >3 months old without update
- **Tag**: [REVIEW NEEDED] or [AS OF DATE]

**6. Conflicting Information**
- **Definition**: Multiple sources contradict
- **Example**: See Context Conflicts section
- **Tag**: [CONFLICT]

---

### Gap Priorities

#### 🔴 CRITICAL (Resolve Immediately)
**Criteria**:
- Blocking important decisions
- High business impact
- Safety/compliance concerns
- Investor/lender reporting needs

**Resolution Timeline**: Within days (same week)

**CEO Action**: Immediate attention, fast-track resolution

**Example**: "What is actual default rate for Express Funding?" (affects pricing, risk assessment, investor comms)

---

#### 🟡 HIGH (Resolve Soon)
**Criteria**:
- Important for complete understanding
- Affects strategic planning
- Needed for operations but not urgent
- Would improve decision quality

**Resolution Timeline**: Within 2-4 weeks

**CEO Action**: Batch resolution in scheduled session

**Example**: "What percentage of revenue comes from each product?" (affects strategic prioritization)

---

#### 🟢 NORMAL (Fill Opportunistically)
**Criteria**:
- Nice to have for completeness
- Doesn't affect current decisions
- Context enrichment
- Historical or background information

**Resolution Timeline**: Quarterly or when convenient

**CEO Action**: Fill in during related meetings/conversations

**Example**: "When was company founded?" (context, not decision-critical)

---

### How Gaps Are Identified

**Automatic Identification Triggers**:

1. **During Context Organization**
   - AI notices questions while organizing Tier 1 → Tier 2
   - Template sections remain unfilled
   - Related context doesn't exist

2. **During Conflict Detection**
   - Cannot resolve conflict due to missing information
   - Need authoritative source but don't have it

3. **During CEO Questions**
   - CEO asks about something not documented
   - AI cannot answer from existing context

4. **During Template Application**
   - Template reveals what's missing
   - Systematic structure highlights gaps

5. **During Cross-Referencing**
   - Referenced context file doesn't exist
   - Expected information not present

6. **During Quarterly Audit**
   - Systematic review of all domains
   - Coverage assessment reveals gaps
   - "Last Updated" dates reveal stale content

**AI Assistant Mandatory Actions When Gap Identified**:
1. Document gap in domain-specific open items file
2. Update central tracker with count
3. Categorize priority (Critical/High/Normal)
4. Categorize type (Missing/Incomplete/etc.)
5. Suggest resolution path
6. Assign owner if clear
7. Propose target date based on priority

---

### Gap Resolution Workflow

#### For Critical Gaps (🔴)

```
Gap identified → Flag to CEO immediately
                 ↓
           "CRITICAL GAP: [description]"
                 ↓
           CEO prioritizes resolution
                 ↓
           Fast-track answer (days, not weeks)
                 ↓
           Update context + Mark resolved
                 ↓
           Remove from tracker
```

**AI Action**: Alert CEO in conversation: "⚠️ CRITICAL GAP identified: [description] - needs immediate resolution"

---

#### For High Priority Gaps (🟡)

```
Gap identified → Add to HIGH priority list
                 ↓
           Batch with related questions
                 ↓
           Include in weekly CEO summary
                 ↓
           Schedule resolution session (30-60 min)
                 ↓
           CEO answers batch of questions
                 ↓
           Update context + Mark resolved
                 ↓
           Remove from tracker
```

**AI Action**: Include in weekly summary: "8 HIGH priority gaps - recommend batch resolution session"

**Recommended**: Schedule dedicated "Context Gap Resolution" session monthly

---

#### For Normal Priority Gaps (🟢)

```
Gap identified → Add to NORMAL priority list
                 ↓
           Track passively
                 ↓
           Fill opportunistically (meetings, conversations)
                 ↓
           No active pressure
                 ↓
           Update when resolved
```

**AI Action**: Include in monthly review, but don't actively push

---

### Open Items File Format

**Standard Entry**:

```markdown
### [Number]. [Gap Title]
**Priority**: 🔴 CRITICAL | 🟡 HIGH | 🟢 NORMAL
**Type**: Missing | Incomplete | Ambiguous | Unverified | Outdated | Conflicting

**Question**: [What do we need to know?]

**Current Status**: [What do we know currently?]

**Why [Priority]**: [Why this matters at this priority level]

**Resolution Path**: [How to resolve - who to ask, what to check]

**Assigned To**: [CEO → Person] or [CEO decides]

**Target Date**: [When should this be resolved?]

**Related Context**: [Links to context files that would be updated]
```

**Resolution Entry**:

```markdown
**Resolution Date**: YYYY-MM-DD
**Resolved By**: [Who provided answer]
**Answer**: [The resolution]
**Context Updated**: [Which files were updated]
**Quality**: Verified | Estimate | Partial
```

---

### Integration with Weekly/Monthly Reviews

#### Weekly CEO Summary

Include gap status:

```markdown
## Context Gaps Update
**Total Open Items**: 65

**Priority Breakdown**:
- 🔴 CRITICAL: 2 items (action needed this week)
- 🟡 HIGH: 8 items (batch resolution recommended)
- 🟢 NORMAL: 55 items (fill opportunistically)

**Critical Actions This Week**:
1. Express Funding default rate - need verified number from CFO
2. PCA product differentiation - need CFO/VP Sales explanation

**Recommended**: Schedule 45-min gap resolution session with CFO to answer 8 HIGH priority product questions
```

---

#### Monthly Organization Session

**Agenda**:
1. Review newly identified gaps (15 min)
2. Prioritize/re-prioritize open items (10 min)
3. Resolve batch of HIGH priority items (30 min)
4. Archive resolved items (5 min)

**AI Preparation**:
- Updated gap counts by domain
- Prioritized list of questions to resolve
- Suggested batch resolution by topic (e.g., "all product questions")

---

#### Quarterly Context Audit

**Include**:
1. **Coverage Assessment**: % complete by domain
2. **Gap Analysis**: Where are biggest gaps?
3. **Resolution Progress**: How many gaps resolved this quarter?
4. **Priority Adjustment**: Should priorities change based on business needs?
5. **Target Setting**: Coverage targets for next quarter

**Output**: Quarterly gap report + updated priorities

---

### CEO Dashboard Integration

**Recommended**: Create `CEO-Dashboard/context-status.md`

**Contents**:
```markdown
# Context System Health

**Last Updated**: YYYY-MM-DD

## Domain Coverage
- Company: 30% (9 open items)
- Products: 40% (20+ open items)
- Operations: 0% (not started)
- Departments: 0% (not started)
- Strategy: 0% (not started)
- Glossary: 20% (5 open items)

## Critical Items (Action Needed)
1. [Item requiring immediate attention]
2. [Item requiring immediate attention]

## High Priority Batch
[8 items ready for 45-min resolution session]

## Quick Links
- [Full Gap Tracker](../System/BusinessContext/context-gaps-tracker.md)
- [Product Open Items](../System/BusinessContext/Products/products-open-items.md)
- [Company Open Items](../System/BusinessContext/Company/company-open-items.md)
```

---

### Success Indicators

**Gap tracking is working when**:

✅ **No surprises** - CEO knows what's missing, not just what exists
✅ **Prioritized resolution** - Critical gaps resolved first, normal gaps filled opportunistically
✅ **Visible progress** - Coverage % increasing over time
✅ **Systematic capture** - All questions documented, not lost
✅ **Efficient resolution** - Batch sessions vs. one-off questions
✅ **Quality control** - Unverified info flagged until confirmed

**Target State**:
- All domains >50% coverage within 6 months
- No critical gaps >1 week old
- High priority gaps resolved within 4 weeks
- Normal gaps filled within 3 months or less

---

## Success Metrics

**You'll know the system is working when**:

✅ **Capture is effortless** - No friction to dump knowledge
✅ **Context is findable** - You can locate information in <2 minutes
✅ **AI is helpful** - Claude/Serena give context-aware assistance
✅ **Onboarding is faster** - New team members can learn from organized context
✅ **Decisions are informed** - Business context readily available when needed
✅ **Nothing is lost** - Important insights are preserved and accessible

---

## Troubleshooting

### "TempDocs is getting full of unorganized notes"
→ **Solution**: Schedule monthly organization session, don't worry about perfect organization

### "I'm not sure which domain to put this in"
→ **Solution**: Put it in the closest match, add cross-references to other relevant domains

### "Context seems outdated but I'm not sure"
→ **Solution**: Mark as "Review Needed" and ask the relevant department head

### "AI doesn't seem to know the business context"
→ **Solution**: Update Serena memory files from organized context

### "I learned something but don't have time to write it down"
→ **Solution**: Voice memo it, transcribe later, or one-sentence TIL note

---

## Related Documentation

- **CLAUDE.md** - AI assistant guidelines (includes context rules)
- **System/SystemGuides/README.md** - System overview
- **System/SystemGuides/DATA-COLLECTION-MASTER-PLAN.md** - Data strategy
- **System/BusinessContext/_templates/** - Templates for organizing context

---

## Questions or Improvements?

Document feedback in: `TempDocs/notes/context-guide-feedback.md`

The CEO can modify this guide at any time to reflect evolving needs.

---

**Version History**:
- **v1.0** (2025-10-28): Initial guide created - permanent strategy codified

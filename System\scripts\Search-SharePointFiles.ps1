<#
.SYNOPSIS
    Smart search for SharePoint files - metadata first, then content

.DESCRIPTION
    Two-step search approach:
    1. Search index.json for matching files (fast)
    2. Optionally extract and search content (targeted)

.PARAMETER Query
    Search term for filenames

.PARAMETER Department
    Filter by department

.PARAMETER FileType
    Filter by file type (pdf, docx, xlsx, pptx)

.PARAMETER After
    Only files modified after this date (yyyy-MM-dd)

.PARAMETER Before
    Only files modified before this date (yyyy-MM-dd)

.PARAMETER ContentSearch
    Search inside file contents (requires extraction)

.PARAMETER ExtractMatches
    Auto-extract matching files for content search

.EXAMPLE
    .\Search-SharePointFiles.ps1 -Query "strategy"
    Finds files with "strategy" in filename

.EXAMPLE
    .\Search-SharePointFiles.ps1 -Department Marketing -FileType pdf -After "2024-01-01"
    Finds Marketing PDFs from 2024

.EXAMPLE
    .\Search-SharePointFiles.ps1 -Query "budget" -ContentSearch "Q4" -ExtractMatches
    Finds files named "budget", extracts them, searches for "Q4" inside

.NOTES
    Fast metadata search first, then targeted content search
#>

[CmdletBinding()]
param(
    [string]$Query = "",
    [string]$Department = "",
    [string]$FileType = "",
    [string]$After = "",
    [string]$Before = "",
    [string]$ContentSearch = "",
    [switch]$ExtractMatches
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$IndexFile = Join-Path $ProjectRoot "SharePointLinks\index.json"
$ContentRoot = Join-Path $ProjectRoot "docs\sharepoint-content"

# Check if index exists
if (-not (Test-Path $IndexFile)) {
    Write-Host "ERROR: Index file not found: $IndexFile" -ForegroundColor Red
    Write-Host "Run: .\Sync-SharePointIndex.ps1 first" -ForegroundColor Yellow
    exit 1
}

# Load index
Write-Host "Loading SharePoint index..." -ForegroundColor Cyan
$index = Get-Content $IndexFile -Raw -Encoding UTF8 | ConvertFrom-Json
$results = $index.files

Write-Host "Total files in index: $($results.Count)" -ForegroundColor Gray

# Apply filters
if ($Query) {
    $results = $results | Where-Object { $_.name -like "*$Query*" -or $_.path -like "*$Query*" }
    Write-Host "Filtered by query '$Query': $($results.Count) files" -ForegroundColor Gray
}

if ($Department) {
    $results = $results | Where-Object { $_.department -eq $Department }
    Write-Host "Filtered by department '$Department': $($results.Count) files" -ForegroundColor Gray
}

if ($FileType) {
    $results = $results | Where-Object { $_.extension -eq ".$FileType" }
    Write-Host "Filtered by type '$FileType': $($results.Count) files" -ForegroundColor Gray
}

if ($After) {
    $afterDate = [DateTime]::Parse($After)
    $results = $results | Where-Object { [DateTime]::Parse($_.modified) -gt $afterDate }
    Write-Host "Filtered by date after '$After': $($results.Count) files" -ForegroundColor Gray
}

if ($Before) {
    $beforeDate = [DateTime]::Parse($Before)
    $results = $results | Where-Object { [DateTime]::Parse($_.modified) -lt $beforeDate }
    Write-Host "Filtered by date before '$Before': $($results.Count) files" -ForegroundColor Gray
}

# Display results
Write-Host ""
Write-Host "=== SEARCH RESULTS: $($results.Count) files ===" -ForegroundColor Green
Write-Host ""

if ($results.Count -eq 0) {
    Write-Host "No files found matching criteria" -ForegroundColor Yellow
    exit 0
}

# Display matches
foreach ($file in $results | Sort-Object department, path) {
    Write-Host "[$($file.extension)] " -NoNewline -ForegroundColor Cyan
    Write-Host "$($file.department)/" -NoNewline -ForegroundColor Yellow
    Write-Host "$($file.name) " -NoNewline -ForegroundColor White
    Write-Host "($($file.sizeMB) MB, $($file.modified))" -ForegroundColor Gray
}

Write-Host ""

# Content search if requested
if ($ContentSearch) {
    Write-Host "=== CONTENT SEARCH: '$ContentSearch' ===" -ForegroundColor Green
    Write-Host ""

    # Check if extraction needed
    $extractNeeded = @()
    $searchableFiles = @()

    foreach ($file in $results) {
        $relativePath = $file.path -replace '\\', '_' -replace ' ', '_'
        $mdFileName = "$($file.department)_$relativePath.md"
        $mdPath = Join-Path $ContentRoot $mdFileName

        if (Test-Path $mdPath) {
            $searchableFiles += @{file = $file; mdPath = $mdPath}
        } else {
            $extractNeeded += $file
        }
    }

    if ($extractNeeded.Count -gt 0) {
        Write-Host "Need to extract $($extractNeeded.Count) files for content search" -ForegroundColor Yellow

        if ($ExtractMatches) {
            Write-Host "Extracting files..." -ForegroundColor Cyan
            # TODO: Call Extract-SharePointContent.ps1 for specific files
            Write-Host "NOTE: Auto-extraction not yet implemented" -ForegroundColor Yellow
            Write-Host "Run this to extract matching files:" -ForegroundColor Yellow
            Write-Host "  .\Extract-SharePointContent.ps1 -Department $Department -FileType $FileType" -ForegroundColor Gray
        } else {
            Write-Host ""
            Write-Host "Run with -ExtractMatches to auto-extract, or manually extract:" -ForegroundColor Yellow
            Write-Host "  .\Extract-SharePointContent.ps1 -Department $Department" -ForegroundColor Gray
        }
    }

    if ($searchableFiles.Count -gt 0) {
        Write-Host ""
        Write-Host "Searching $($searchableFiles.Count) extracted files for: '$ContentSearch'" -ForegroundColor Cyan
        Write-Host ""

        $matchCount = 0
        foreach ($item in $searchableFiles) {
            $content = Get-Content $item.mdPath -Raw -Encoding UTF8

            if ($content -match $ContentSearch) {
                $matchCount++
                Write-Host "MATCH: $($item.file.name)" -ForegroundColor Green

                # Find context around match
                $lines = $content -split "`n"
                for ($i = 0; $i -lt $lines.Count; $i++) {
                    if ($lines[$i] -match $ContentSearch) {
                        $start = [Math]::Max(0, $i - 2)
                        $end = [Math]::Min($lines.Count - 1, $i + 2)

                        Write-Host "  Context:" -ForegroundColor Gray
                        for ($j = $start; $j -le $end; $j++) {
                            if ($j -eq $i) {
                                Write-Host "  >>> $($lines[$j])" -ForegroundColor Yellow
                            } else {
                                Write-Host "      $($lines[$j])" -ForegroundColor Gray
                            }
                        }
                        Write-Host ""
                        break
                    }
                }
            }
        }

        Write-Host ""
        Write-Host "Content matches: $matchCount / $($searchableFiles.Count) files" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== SEARCH COMPLETE ===" -ForegroundColor Green

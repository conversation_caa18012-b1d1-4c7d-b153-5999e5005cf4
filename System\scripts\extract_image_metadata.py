"""
Extract image metadata without API calls
This extracts technical metadata and basic information
For AI descriptions, <PERSON> will analyze images directly during extraction
"""

import sys
import json
from pathlib import Path
from PIL import Image
from PIL.ExifTags import TAGS
import datetime

def get_image_metadata(image_path):
    """Extract comprehensive metadata from image"""
    try:
        img = Image.open(image_path)
        path = Path(image_path)

        metadata = {
            "format": img.format,
            "mode": img.mode,
            "width": img.width,
            "height": img.height,
            "size_pixels": f"{img.width}x{img.height}",
            "megapixels": round((img.width * img.height) / 1_000_000, 2),
            "file_size_kb": round(path.stat().st_size / 1024, 2),
            "aspect_ratio": f"{img.width}:{img.height}"
        }

        # Extract EXIF data if available
        exif_data = {}
        try:
            if hasattr(img, '_getexif') and img._getexif():
                exif = img._getexif()
                for tag_id, value in exif.items():
                    tag_name = TAGS.get(tag_id, tag_id)
                    # Convert to string to avoid JSON serialization issues
                    exif_data[str(tag_name)] = str(value)
        except:
            pass  # No EXIF data or error reading it

        if exif_data:
            metadata["exif"] = exif_data

            # Extract common EXIF fields for easier access
            if "DateTime" in exif_data:
                metadata["date_taken"] = exif_data["DateTime"]
            if "Make" in exif_data:
                metadata["camera_make"] = exif_data["Make"]
            if "Model" in exif_data:
                metadata["camera_model"] = exif_data["Model"]

        return {"success": True, "metadata": metadata}

    except Exception as e:
        return {"success": False, "error": f"Failed to read image: {e}"}

def main():
    if len(sys.argv) < 2:
        print(json.dumps({"success": False, "error": "No image path provided"}))
        sys.exit(1)

    image_path = sys.argv[1]
    result = get_image_metadata(image_path)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()

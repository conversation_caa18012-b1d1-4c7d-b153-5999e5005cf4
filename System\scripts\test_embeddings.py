"""
Quick test of local embeddings for semantic search
Tests performance on a few sample documents
"""

import time
import json
from pathlib import Path

print("=" * 60)
print("LOCAL EMBEDDINGS PERFORMANCE TEST")
print("=" * 60)
print()

# Test 1: Check if sentence-transformers is available
print("Test 1: Checking dependencies...")
try:
    from sentence_transformers import SentenceTransformer
    print("[OK] sentence-transformers installed")
    has_transformers = True
except ImportError:
    print("[FAIL] sentence-transformers not installed")
    print("   Installing now (this takes 2-3 minutes first time)...")
    import subprocess
    subprocess.run(['pip', 'install', 'sentence-transformers', '--quiet'])
    from sentence_transformers import SentenceTransformer
    print("[OK] Installed successfully")
    has_transformers = True

print()

# Test 2: Load model and measure time
print("Test 2: Loading embedding model...")
start_time = time.time()

# Use lightweight model for testing
model = SentenceTransformer('all-MiniLM-L6-v2')

load_time = time.time() - start_time
print(f"[OK] Model loaded in {load_time:.2f} seconds")
print(f"   Model: all-MiniLM-L6-v2 (384 dimensions)")
print()

# Test 3: Read sample documents
print("Test 3: Reading sample documents...")
content_dir = Path(__file__).parent.parent.parent / "docs" / "sharepoint-content"

# Get first 5 markdown files
md_files = list(content_dir.glob("Marketing_*.md"))[:5]

documents = []
doc_names = []

for md_file in md_files:
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
        # Extract just the content section
        if '## Content' in content:
            content = content.split('## Content')[1].split('---')[0]
        documents.append(content[:2000])  # First 2000 chars
        doc_names.append(md_file.stem)

print(f"[OK] Read {len(documents)} documents")
for i, name in enumerate(doc_names):
    print(f"   {i+1}. {name[:60]}...")
print()

# Test 4: Generate embeddings
print("Test 4: Generating embeddings...")
start_time = time.time()

embeddings = model.encode(documents, show_progress_bar=False)

embed_time = time.time() - start_time
print(f"[OK] Generated {len(embeddings)} embeddings in {embed_time:.2f} seconds")
print(f"   Average: {embed_time/len(embeddings):.3f} seconds per document")
print(f"   Embedding shape: {embeddings[0].shape}")
print()

# Test 5: Semantic search
print("Test 5: Semantic search test...")
queries = [
    "customer experience and satisfaction",
    "awards and recognition",
    "marketing strategy planning"
]

for query in queries:
    print(f"\nQuery: '{query}'")

    start_time = time.time()
    query_embedding = model.encode(query, show_progress_bar=False)

    # Calculate cosine similarity
    from sklearn.metrics.pairwise import cosine_similarity
    similarities = cosine_similarity([query_embedding], embeddings)[0]

    search_time = time.time() - start_time

    # Get top 3 results
    top_indices = similarities.argsort()[-3:][::-1]

    print(f"  Search time: {search_time*1000:.1f}ms")
    print(f"  Top 3 results:")
    for rank, idx in enumerate(top_indices, 1):
        print(f"    {rank}. {doc_names[idx][:50]}... (similarity: {similarities[idx]:.3f})")

print()
print("=" * 60)
print("PERFORMANCE SUMMARY")
print("=" * 60)
print(f"Model load time:        {load_time:.2f}s (one-time)")
print(f"Embedding generation:   {embed_time/len(embeddings):.3f}s per document")
print(f"Search time:            ~{search_time*1000:.1f}ms per query")
print(f"Total for {len(documents)} docs:   {embed_time:.2f}s")
print()

# Extrapolate to full dataset
total_docs = 284
estimated_time = (embed_time / len(documents)) * total_docs
print("PROJECTION FOR 284 FILES:")
print(f"  Embedding generation: ~{estimated_time:.1f}s ({estimated_time/60:.1f} minutes)")
print(f"  Storage needed:       ~{total_docs * 384 * 4 / 1024 / 1024:.1f} MB")
print(f"  Search time:          < 100ms per query")
print()
print("[OK] Local embeddings are FAST and FREE!")

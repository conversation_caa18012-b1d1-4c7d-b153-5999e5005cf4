# Email Style Guide

**Purpose**: Ensure all HTML emails from the ReidCEO system have a consistent, professional appearance.

---

## Design Principles

1. **Minimal Colors**: Use colors sparingly - only for subtle accents and essential highlights
2. **Professional Appearance**: Business document aesthetic, not web page
3. **Reserved Backgrounds**: Gray backgrounds ONLY for fully boxed sections that need emphasis
4. **Clean Typography**: Calibri/Segoe UI for readability
5. **No Emojis**: Avoid emoji characters (encoding issues in Outlook)

---

## Color Palette

### Primary Colors
- **Body Text**: `#333` (dark gray)
- **Headings**: `#2c3e50` (darker gray-blue)
- **Accent/Borders**: `#3498db` (subtle blue)

### Background Colors
- **Body**: `#ffffff` (white)
- **Highlight Boxes ONLY**: `#f8f9fa` (very light gray)
- **Code Backgrounds**: `#f4f4f4` (light gray)

### Code Colors
- **Inline Code Text**: `#c7254e` (red - for visibility)

### Footer/Meta Text
- **Footer Text**: `#6c757d` (medium gray)

---

## Typography

### Fonts
```css
body {
    font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
}
code {
    font-family: 'Consolas', 'Courier New', monospace;
}
```

### Font Sizes
- **H1**: 24px
- **H2**: 18px
- **H3**: 16px
- **Body**: Default (16px)
- **Code**: 13px
- **Footer**: 14px

---

## HTML Structure

### Required Meta Tag
```html
<head>
    <meta charset="UTF-8">
    <!-- Prevents emoji/unicode rendering issues -->
</head>
```

### Heading Styles

**H1 - Email Title**
```css
h1 {
    color: #2c3e50;
    font-size: 24px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}
```

**H2 - Section Headers**
```css
h2 {
    color: #2c3e50;
    font-size: 18px;
    padding-left: 10px;
    border-left: 4px solid #3498db;
}
```

**H3 - Subsections**
```css
h3 {
    color: #2c3e50;
    font-size: 16px;
}
```

---

## Component Patterns

### Inline Code (Script Names, File Paths)
```html
<code>New-Email.ps1</code>
<code>CEO-Dashboard/daily-briefing.md</code>
```

**Style**:
```css
code {
    background: #f4f4f4;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 13px;
    color: #c7254e;
}
```

### Script Lists (NO BACKGROUND!)
```html
<div class="scripts">
    <strong>Scripts:</strong>
    <code>Script1.ps1</code>,
    <code>Script2.ps1</code>
</div>
```

**Style**:
```css
.scripts {
    padding: 8px 0;
    margin: 10px 0;
    /* NO background color! */
}
```

### Highlight Boxes (USE SPARINGLY)
Use ONLY when something truly needs emphasis or is a self-contained reference section.

```html
<div class="highlight-box">
    <h3>Important Section Title</h3>
    <p>Content that needs to stand out...</p>
</div>
```

**Style**:
```css
.highlight-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
}
```

**Examples of Appropriate Use**:
- ✅ "Key Capabilities Summary" box at bottom of system overview
- ✅ "Getting Started" quick reference box
- ✅ Critical alerts or urgent action items
- ❌ Script lists (use plain text instead)
- ❌ Regular sections (use H2 headers instead)

---

## Complete Template

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        h2 {
            color: #2c3e50;
            font-size: 18px;
            margin-top: 25px;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #2c3e50;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        p {
            margin: 10px 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        ul li {
            margin: 5px 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .scripts {
            padding: 8px 0;
            margin: 10px 0;
        }
        code {
            background: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            color: #c7254e;
        }
        .highlight-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        strong {
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <h1>Email Title</h1>

    <p>Opening paragraph...</p>

    <div class="section">
        <h2>Section Header</h2>
        <ul>
            <li>Bullet point</li>
            <li>Another point</li>
        </ul>
        <div class="scripts">
            <strong>Scripts:</strong> <code>Script1.ps1</code>, <code>Script2.ps1</code>
        </div>
    </div>

    <div class="highlight-box">
        <h3>Highlighted Section</h3>
        <p>Use sparingly for emphasis...</p>
    </div>

    <div class="footer">
        <p><em>Generated by ReidCEO Management System</em></p>
    </div>
</body>
</html>
```

---

## Tables

**IMPORTANT**: All tables in emails should use proper HTML `<table>` elements, not markdown tables.

**Outlook-Friendly Table Style**:
```html
<table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
  <thead>
    <tr style="background-color: #f0f7ff;">
      <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; color: #2c3e50;">Header 1</th>
      <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; color: #2c3e50;">Header 2</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #dee2e6; padding: 8px;">Data 1</td>
      <td style="border: 1px solid #dee2e6; padding: 8px;">Data 2</td>
    </tr>
  </tbody>
</table>
```

**Key Table Styling Rules**:
- Always use `border-collapse: collapse`
- Light blue header background: `#f0f7ff`
- Gray borders: `#dee2e6`
- 8px padding for readability
- Dark text in headers: `#2c3e50`
- 100% width for full-width tables
- Left-aligned text (use `text-align: right` for numbers)

**Markdown Tables**: If extracting content from markdown files, tables MUST be converted to HTML. The `Email-DailyBriefing.ps1` script includes a `Convert-MarkdownTableToHTML` function that automatically handles this conversion.

## Footer Guidelines

**Important**: The email body footer should only identify that the content was generated by the ReidCEO Management System. Do NOT include "Best regards" or other sign-offs, as Reid's personal Outlook signature will appear below the email body.

**Correct Footer**:
```html
<div class="footer">
    <p><em>Generated by ReidCEO Management System</em></p>
</div>
```

**Incorrect Footer**:
```html
<!-- DON'T DO THIS - Reid's signature will appear below -->
<div class="footer">
    <p>Best regards,<br>Reid</p>
</div>
```

---

## Common Mistakes to Avoid

1. ❌ **Adding emojis** - Causes encoding issues in Outlook
2. ❌ **Gray backgrounds on regular sections** - Reserve for highlight boxes only
3. ❌ **Bright/garish colors** - Keep it professional and minimal
4. ❌ **Forgetting UTF-8 meta tag** - Causes character encoding problems
5. ❌ **Overusing highlight boxes** - Dilutes their impact
6. ❌ **Adding "Best regards" or sign-offs** - Reid's Outlook signature handles this
7. ❌ **Using markdown tables in HTML emails** - Always convert to HTML `<table>` elements
8. ❌ **Leaving emojis in extracted markdown content** - Strip them before inserting into emails

---

## Reusable Helper Functions

When creating email scripts that extract content from markdown files, use these helper functions to ensure proper formatting:

### Convert-MarkdownTableToHTML
Converts markdown tables to properly styled HTML tables.

**Location**: `Email-DailyBriefing.ps1` (lines 65-141)

**Usage**:
```powershell
$htmlContent = Convert-MarkdownTableToHTML $markdownContent
```

### Clean-Content
Removes emojis, converts markdown formatting to HTML, and handles line breaks.

**Location**: `Email-DailyBriefing.ps1` (lines 143-168)

**Usage**:
```powershell
$cleanedContent = Clean-Content $extractedContent
```

**What it does**:
- Strips non-ASCII characters (emojis)
- Converts markdown tables to HTML tables
- Converts `**bold**` to `<strong>bold</strong>`
- Converts `[link](url)` to `<a href="url">link</a>`
- Adds `<br>` tags for line breaks (except in tables)

### Extract-Section
Extracts content between markdown headers.

**Location**: `Email-DailyBriefing.ps1` (lines 50-63)

**Usage**:
```powershell
$section = Extract-Section $fileContent "SECTION NAME|Alternative Name|Another Alternative"
```

**Pro Tip**: When creating new automated email scripts that pull from markdown files, copy these functions from `Email-DailyBriefing.ps1` to ensure consistent formatting.

---

## When Creating New Email Templates

1. Start with the complete template above
2. Replace placeholder content with actual email content
3. Use H2 headers for main sections
4. Use `.scripts` divs (without background) for script/file lists
5. Reserve `.highlight-box` for truly important callouts
6. Keep color usage minimal and consistent
7. If creating HTML templates, add `FORMAT: HTML` header
8. If extracting from markdown, use helper functions to clean content
9. Test in Outlook before finalizing

---

## Reference Files

**Email Template Standard**: `TempDocs/test-email-body-html-v3.html`
- Approved email design standard for ReidCEO system emails

**Helper Functions**: `System/scripts/Email-DailyBriefing.ps1`
- Reusable functions for markdown-to-HTML conversion
- Copy these when creating new automated email scripts

---

**Last Updated**: 2025-10-29
**Version**: 1.1

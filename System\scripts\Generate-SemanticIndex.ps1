<#
.SYNOPSIS
    Generates semantic embeddings for SharePoint content using local AI model

.DESCRIPTION
    Creates vector embeddings for semantic search:
    1. Reads extracted markdown files from docs/sharepoint-content/
    2. Uses sentence-transformers to generate 384-dim vectors
    3. Stores embeddings in JSON for fast semantic search
    4. Enables finding conceptually similar documents

.PARAMETER Rebuild
    Force rebuild of embeddings even if they exist

.EXAMPLE
    .\Generate-SemanticIndex.ps1
    Generates embeddings for all extracted content

.EXAMPLE
    .\Generate-SemanticIndex.ps1 -Rebuild
    Forces complete rebuild

.NOTES
    Requires: Python with sentence-transformers library
    Model: all-MiniLM-L6-v2 (384 dimensions)
    Speed: ~0.03s per document
    Storage: ~1.5 KB per document
    Cost: $0 (runs locally)
#>

[CmdletBinding()]
param(
    [switch]$Rebuild
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$ContentRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$EmbeddingsFile = Join-Path $ContentRoot "semantic-embeddings.json"
$MetadataFile = Join-Path $ProjectRoot "SharePointLinks\index.json"
$LogFile = Join-Path $PSScriptRoot "semantic-log.txt"
$PythonScript = Join-Path $PSScriptRoot "generate_embeddings.py"

# Initialize log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== Semantic Embeddings Generation Started ==="

# Check if content directory exists
if (-not (Test-Path $ContentRoot)) {
    Write-Log "Content directory not found: $ContentRoot" "ERROR"
    Write-Log "Run .\Extract-SharePointContent.ps1 first" "ERROR"
    exit 1
}

# Check if embeddings exist and if rebuild needed
if ((Test-Path $EmbeddingsFile) -and -not $Rebuild) {
    $embeddingsAge = (Get-Date) - (Get-Item $EmbeddingsFile).LastWriteTime
    if ($embeddingsAge.TotalHours -lt 24) {
        Write-Log "Embeddings are recent (< 24 hours old), use -Rebuild to force rebuild" "INFO"
        Write-Log "Embeddings: $EmbeddingsFile" "INFO"
        exit 0
    }
}

Write-Log "Building semantic embeddings..."

# Check Python
Write-Log "Checking Python environment..."
try {
    $pythonVersion = python --version 2>&1
    Write-Log "Found: $pythonVersion" "SUCCESS"
} catch {
    Write-Log "Python not found!" "ERROR"
    exit 1
}

# Check sentence-transformers
Write-Log "Checking sentence-transformers library..."
$checkLib = python -c "import sentence_transformers; print('OK')" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Log "Installing sentence-transformers (first-time setup, ~2 minutes)..." "WARN"
    pip install sentence-transformers scikit-learn --quiet
    Write-Log "Installation complete" "SUCCESS"
}

# Create Python script if needed
if (-not (Test-Path $PythonScript)) {
    Write-Log "Creating embedding generator script..."

    $pythonCode = @'
"""
Generate semantic embeddings for SharePoint content
Uses sentence-transformers for local, free semantic search
"""

import json
import time
from pathlib import Path
from sentence_transformers import SentenceTransformer
import sys

def main():
    content_root = Path(sys.argv[1])
    output_file = Path(sys.argv[2])

    print("Loading model...")
    start_time = time.time()

    # Load lightweight model (384 dimensions)
    model = SentenceTransformer('all-MiniLM-L6-v2')

    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f}s")

    # Get all markdown files
    md_files = list(content_root.glob("Marketing_*.md"))
    md_files = [f for f in md_files if f.name != "README.md"]

    print(f"Found {len(md_files)} files to embed")

    documents = []
    file_keys = []

    # Read all documents
    for md_file in md_files:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()

            # Extract main content (skip metadata)
            if '## Content' in content:
                parts = content.split('## Content')
                if len(parts) > 1:
                    main_content = parts[1].split('---')[0] if '---' in parts[1] else parts[1]
                else:
                    main_content = content
            else:
                main_content = content

            # Use first 2000 chars (most relevant content)
            documents.append(main_content[:2000].strip())
            file_keys.append(md_file.stem)

    # Generate embeddings
    print(f"Generating embeddings for {len(documents)} documents...")
    start_time = time.time()

    embeddings = model.encode(documents, show_progress_bar=True)

    embed_time = time.time() - start_time
    print(f"Generated embeddings in {embed_time:.2f}s")
    print(f"Average: {embed_time/len(documents):.3f}s per document")

    # Build embeddings structure
    embeddings_data = {
        "version": "1.0",
        "model": "all-MiniLM-L6-v2",
        "dimensions": 384,
        "created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_files": len(file_keys),
        "embeddings": {}
    }

    # Store embeddings
    for file_key, embedding in zip(file_keys, embeddings):
        # Convert to list for JSON serialization
        embeddings_data["embeddings"][file_key] = embedding.tolist()

    # Save to file
    print(f"Saving embeddings to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(embeddings_data, f, indent=2)

    file_size_mb = output_file.stat().st_size / 1024 / 1024
    print(f"Saved: {file_size_mb:.2f} MB")

    print(f"\nSUMMARY:")
    print(f"  Files: {len(file_keys)}")
    print(f"  Dimensions: 384")
    print(f"  Total time: {embed_time:.2f}s")
    print(f"  Storage: {file_size_mb:.2f} MB")

if __name__ == "__main__":
    main()
'@

    $pythonCode | Set-Content -Path $PythonScript -Encoding UTF8
    Write-Log "Created: $PythonScript" "SUCCESS"
}

# Run embedding generation
Write-Log "Running embedding generator..."
Write-Log "This may take 10-30 seconds depending on number of files..."

try {
    python $PythonScript $ContentRoot $EmbeddingsFile

    if ($LASTEXITCODE -eq 0) {
        Write-Log "Embeddings generated successfully" "SUCCESS"

        # Read embeddings file to get stats
        $embeddings = Get-Content $EmbeddingsFile -Raw | ConvertFrom-Json

        Write-Log "=== Embedding Statistics ===" "INFO"
        Write-Log "Model: $($embeddings.model)"
        Write-Log "Dimensions: $($embeddings.dimensions)"
        Write-Log "Total files: $($embeddings.total_files)"
        Write-Log "Created: $($embeddings.created)"

        $fileSizeMB = [math]::Round((Get-Item $EmbeddingsFile).Length / 1MB, 2)
        Write-Log "File size: $fileSizeMB MB"

        Write-Log "=== Semantic Search Ready ===" "SUCCESS"
        Write-Log "Use: .\Search-SemanticContent.ps1 -Query 'your search'"

    } else {
        Write-Log "Embedding generation failed" "ERROR"
        exit 1
    }

} catch {
    Write-Log "ERROR: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "=== Complete ==="

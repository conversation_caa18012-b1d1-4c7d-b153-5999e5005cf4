# Key Documents Tracker

**Last Updated**: 2025-10-18
**Purpose**: Quick access to critical business documents
**Status**: Active

---

## 🔴 CRITICAL - Daily/Weekly Documents

### Daily Cash Forecast
**Frequency**: Daily
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [FP&A/Daily cash forecast](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/FP%26A/Daily%20cash%20forecast?csf=1&web=1&e=1UcbBJ)

**Search Command**:
```powershell
cd System/scripts
.\Search-SharePointContent.ps1 -Query "daily cash forecast"
```

**Notes**:
- Review each morning
- Monitor liquidity position
- Flag any concerns immediately

---

### Weekly Financials
**Frequency**: Weekly
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [Historical Data/Sales Performance](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/Historical%20Data/Sales%20Performance?csf=1&web=1&e=dGgPts)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "weekly financials"
```

**Key Metrics**:
- Cash in
- Cash out
- Collections
- LFBD metrics

---

### Weekly AP (Accounts Payable)
**Frequency**: Weekly
**Last Checked**: -
**Status**: ⚪ Not checked
**Approval Required**: ✅ YES

**SharePoint**: [Weekly AP/2025](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/Weekly%20AP/2025?csf=1&web=1&e=1AIbpE)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "weekly AP" -Department Finance
```

**Action Required**:
- Review cash forecast
- Approve accounts payable
- Sign off on weekly spend

---

## 🟡 HIGH PRIORITY - Monthly Documents

### Monthly Financials
**Frequency**: Monthly
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [Historical Data/Sales Performance](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/Historical%20Data/Sales%20Performance?csf=1&web=1&e=dGgPts)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "monthly financials"
```

**Review Items**:
- Cash ingested
- Cash out
- Collections
- LFBD metrics

---

### FBR (Financial Business Review)
**Frequency**: Monthly
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [FP&A/Monthly performance reporting](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/FP%26A/Monthly%20performance%20reporting?csf=1&web=1&e=cPbnEF)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "FBR" -Department Finance
```

**Use Case**:
- Board presentation prep
- Strategic planning
- Performance analysis

---

### Operational Metrics
**Frequency**: Monthly
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [FP&A/Operation Metrics](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/FP%26A/Operation%20Metrics?csf=1&web=1&e=PCGP1b)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "operational metrics"
```

**Focus Areas**:
- Collection analysis by opportunity
- Performance trends
- Efficiency metrics

---

### Lender Update
**Frequency**: Monthly
**Last Checked**: -
**Status**: ⚪ Not checked

**SharePoint**: [Lender Communication/2025](https://appriver3651007941.sharepoint.com/:f:/r/sites/FinanceSharepoint/Shared%20Documents/Lender%20Communication/2025?csf=1&web=1&e=IvpHhd)

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "lender update"
```

**Contents**:
- Sales update
- Finance update
- Operations update

---

## 📊 Dashboards (Monthly)

### PA & Purchase Financials
**Frequency**: Monthly
**Last Checked**: -
**SharePoint**: [Finance Dashboards](https://appriver3651007941.sharepoint.com/sites/FinanceDashboards)

**Metrics**: Detailed performance for PA and Purchased products

---

### Express Funding Dashboard
**Frequency**: Monthly
**Last Checked**: -
**SharePoint**: [Finance Dashboards](https://appriver3651007941.sharepoint.com/sites/FinanceDashboards)

**Metrics**: Express funding detailed metrics

---

### PCA Purchase Book Dashboard
**Frequency**: Monthly
**Last Checked**: -
**SharePoint**: [Finance Dashboards](https://appriver3651007941.sharepoint.com/sites/FinanceDashboards)

**Metrics**: Mustang, Jordan, Sigma performance

---

### Servicing Dashboard
**Frequency**: Monthly
**Last Checked**: -
**SharePoint**: [Finance Dashboards](https://appriver3651007941.sharepoint.com/sites/FinanceDashboards)

**Metrics**: Detailed servicing performance

---

## 🟢 NORMAL PRIORITY - Quarterly/Annual

### Quarterly Covenants
**Frequency**: Quarterly
**Last Checked**: -
**SharePoint**: Finance Sharepoint → Documents → Covenant → All Documents

**Purpose**: Quarterly covenants financials for EWB (East West Bank)

---

### Marketing Materials
**Frequency**: Quarterly updates
**Last Checked**: -
**SharePoint**: [Marketing Sharepoint](https://appriver3651007941.sharepoint.com/sites/Marketing)

**Key Assets**:
- General Sales Sheet - Oct 2025
- Provider Sales Sheet - Oct 2025
- Attorney Sales Sheet - Oct 2025
- Gain Portal Walkthroughs (Provider, Attorney)
- Path to Partnership - Eliezer Nerenberg

**Search Command**:
```powershell
.\Search-SharePointContent.ps1 -Query "sales sheet" -Department Marketing
```

---

### Annual Compliance Documents
**Frequency**: Annual
**Last Checked**: -
**SharePoint**: Finance Sharepoint → Documents

**Documents**:
- Annual Audit Report (Final Documents)
- Tax Returns (Tax Returns folder)
- K1s (K1s folder)
- 1099s - Investors/Employees (2024 folder)
- 1099s - Vendors (1099/Vendors folder)

---

## Quick Search Commands

### Search All Key Documents
```powershell
cd System/scripts

# Daily operations
.\Search-SharePointContent.ps1 -Query "daily cash"
.\Search-SharePointContent.ps1 -Query "weekly financials"

# Monthly performance
.\Search-SharePointContent.ps1 -Query "FBR"
.\Search-SharePointContent.ps1 -Query "operational metrics"
.\Search-SharePointContent.ps1 -Query "lender update"

# Marketing assets
.\Search-SharePointContent.ps1 -Query "sales sheet" -Department Marketing
.\Search-SharePointContent.ps1 -Query "gain portal walkthrough"

# Compliance
.\Search-SharePointContent.ps1 -Query "audit report"
.\Search-SharePointContent.ps1 -Query "quarterly covenant"
```

### Semantic Search (Conceptual)
```powershell
# Find documents about concepts, not just keywords
.\Search-SemanticContent.ps1 -Query "liquidity analysis"
.\Search-SemanticContent.ps1 -Query "collection performance trends"
.\Search-SemanticContent.ps1 -Query "lender compliance reporting"
```

---

## Tracking Workflow

### Daily Morning Routine
1. ✅ Check Daily Cash Forecast
2. ✅ Update "Last Checked" timestamp above
3. ✅ Note any urgent items in daily-briefing.md

### Weekly Review
1. ✅ Review Weekly Financials
2. ✅ Approve Weekly AP
3. ✅ Update status in this tracker
4. ✅ Flag any variances in kpi-alerts.md

### Monthly Review
1. ✅ Review all Monthly documents (Financials, FBR, Operational Metrics)
2. ✅ Check all Dashboards
3. ✅ Review Lender Update before submission
4. ✅ Update CEO-Dashboard/monthly-review.md with findings

### Quarterly Review
1. ✅ Verify Quarterly Covenants submitted
2. ✅ Update Marketing materials if needed
3. ✅ Review strategic documents (Path to Partnership, etc.)

---

## Alerts & Notifications

### Configure Alerts For:

**Missing Documents**:
- ⚠️ Daily Cash Forecast > 24 hours old
- ⚠️ Weekly Financials > 7 days old
- ⚠️ Monthly FBR not updated this month

**Approval Required**:
- 🔔 Weekly AP awaiting approval
- 🔔 Lender Update draft ready for review
- 🔔 Quarterly Covenants due

---

## Automation Setup

### Current Automation
```powershell
# Automated SharePoint sync (if configured)
# Runs: Hourly
# Script: System/scripts/Sync-SharePoint.ps1
```

### Recommended Additions

**Document Freshness Checker** (future):
```powershell
# Check-DocumentFreshness.ps1
# - Scans for daily/weekly/monthly docs
# - Alerts if missing or outdated
# - Updates this tracker automatically
```

**Quick Links Generator** (future):
```powershell
# Generate-QuickLinks.ps1
# - Finds most recent version of each document
# - Creates quick-access markdown with links
# - Updates CEO-Dashboard automatically
```

---

## Notes

**Document Locations**:
- Full registry: `System/SystemGuides/KEY-DOCUMENTS-REGISTRY.md`
- Search guide: `System/SystemGuides/SEARCH-SYSTEMS-GUIDE.md`
- SharePoint sync: `System/SystemGuides/SHAREPOINT-SYNC-GUIDE.md`

**Search Systems**:
- SharePoint docs: Use PowerShell search scripts
- Repository files: Use Serena via Claude Code

**Priority Legend**:
- 🔴 CRITICAL: Daily/Weekly review required
- 🟡 HIGH: Monthly strategic review
- 🟢 NORMAL: Quarterly/Annual reference

---

*This tracker provides quick access to all critical business documents for CEO oversight.*

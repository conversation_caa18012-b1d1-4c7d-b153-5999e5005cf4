﻿"""
Generate semantic embeddings for SharePoint content
Uses sentence-transformers for local, free semantic search
"""

import json
import time
from pathlib import Path
from sentence_transformers import SentenceTransformer
import sys

def main():
    content_root = Path(sys.argv[1])
    output_file = Path(sys.argv[2])

    print("Loading model...")
    start_time = time.time()

    # Load lightweight model (384 dimensions)
    model = SentenceTransformer('all-MiniLM-L6-v2')

    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f}s")

    # Get all markdown files
    md_files = list(content_root.glob("Marketing_*.md"))
    md_files = [f for f in md_files if f.name != "README.md"]

    print(f"Found {len(md_files)} files to embed")

    documents = []
    file_keys = []

    # Read all documents
    for md_file in md_files:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()

            # Extract main content (skip metadata)
            if '## Content' in content:
                parts = content.split('## Content')
                if len(parts) > 1:
                    main_content = parts[1].split('---')[0] if '---' in parts[1] else parts[1]
                else:
                    main_content = content
            else:
                main_content = content

            # Use first 2000 chars (most relevant content)
            documents.append(main_content[:2000].strip())
            file_keys.append(md_file.stem)

    # Generate embeddings
    print(f"Generating embeddings for {len(documents)} documents...")
    start_time = time.time()

    embeddings = model.encode(documents, show_progress_bar=True)

    embed_time = time.time() - start_time
    print(f"Generated embeddings in {embed_time:.2f}s")
    print(f"Average: {embed_time/len(documents):.3f}s per document")

    # Build embeddings structure
    embeddings_data = {
        "version": "1.0",
        "model": "all-MiniLM-L6-v2",
        "dimensions": 384,
        "created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_files": len(file_keys),
        "embeddings": {}
    }

    # Store embeddings
    for file_key, embedding in zip(file_keys, embeddings):
        # Convert to list for JSON serialization
        embeddings_data["embeddings"][file_key] = embedding.tolist()

    # Save to file
    print(f"Saving embeddings to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(embeddings_data, f, indent=2)

    file_size_mb = output_file.stat().st_size / 1024 / 1024
    print(f"Saved: {file_size_mb:.2f} MB")

    print(f"\nSUMMARY:")
    print(f"  Files: {len(file_keys)}")
    print(f"  Dimensions: 384")
    print(f"  Total time: {embed_time:.2f}s")
    print(f"  Storage: {file_size_mb:.2f} MB")

if __name__ == "__main__":
    main()

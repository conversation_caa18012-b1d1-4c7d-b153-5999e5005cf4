﻿import sys
import json
from pathlib import Path

def extract_pdf(file_path):
    """Extract text from PDF"""
    try:
        import PyPDF2
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = []
            for page in reader.pages:
                text.append(page.extract_text())
            return '\n'.join(text)
    except Exception as e:
        return f"Error extracting PDF: {e}"

def extract_docx(file_path):
    """Extract text from DOCX"""
    try:
        from docx import Document
        doc = Document(file_path)
        text = []
        for para in doc.paragraphs:
            text.append(para.text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting DOCX: {e}"

def extract_xlsx(file_path):
    """Extract text from XLSX"""
    try:
        from openpyxl import load_workbook
        wb = load_workbook(file_path, read_only=True, data_only=True)
        text = []
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]
            text.append(f"\n## Sheet: {sheet_name}\n")
            for row in sheet.iter_rows(values_only=True):
                row_text = '\t'.join([str(cell) if cell is not None else '' for cell in row])
                if row_text.strip():
                    text.append(row_text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting XLSX: {e}"

def extract_pptx(file_path):
    """Extract text from PPTX"""
    try:
        from pptx import Presentation
        prs = Presentation(file_path)
        text = []
        for i, slide in enumerate(prs.slides):
            text.append(f"\n## Slide {i+1}\n")
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text.append(shape.text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting PPTX: {e}"

def extract_content(file_path):
    """Extract content based on file type"""
    path = Path(file_path)
    ext = path.suffix.lower()

    extractors = {
        '.pdf': extract_pdf,
        '.docx': extract_docx,
        '.xlsx': extract_xlsx,
        '.pptx': extract_pptx
    }

    if ext in extractors:
        return extractors[ext](file_path)
    else:
        return f"Unsupported file type: {ext}"

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: extract_content.py <file_path>"}))
        sys.exit(1)

    file_path = sys.argv[1]

    try:
        content = extract_content(file_path)
        result = {
            "success": True,
            "content": content,
            "length": len(content)
        }
        print(json.dumps(result))
    except Exception as e:
        result = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(result))

<#
.SYNOPSIS
    Creates Windows shortcuts to SharePoint files synced via OneDrive

.DESCRIPTION
    This script:
    1. Scans OneDrive SharePoint sync folders
    2. Creates .lnk shortcut files in SharePointLinks/
    3. Organizes shortcuts by department/folder structure
    4. Tiny file size (shortcuts only, not actual files)
    5. Click shortcut = opens file from OneDrive

.PARAMETER DepartmentFilter
    Only create shortcuts for specific departments (e.g., "Marketing", "Finance")

.PARAMETER DaysOld
    Only create shortcuts for files modified within X days (default: 365)

.PARAMETER Refresh
    Delete existing shortcuts and recreate all

.EXAMPLE
    .\Create-SharePointShortcuts.ps1
    Creates shortcuts for all departments

.EXAMPLE
    .\Create-SharePointShortcuts.ps1 -DepartmentFilter "Marketing"
    Creates shortcuts only for Marketing files

.EXAMPLE
    .\Create-SharePointShortcuts.ps1 -DaysOld 90 -Refresh
    Recreates shortcuts for files modified in last 90 days

.NOTES
    Author: ReidCEO Project
    Date: 2025-10-11
    Version: 1.0
#>

[CmdletBinding()]
param(
    [string]$DepartmentFilter = "",
    [int]$DaysOld = 365,
    [switch]$Refresh
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$ShortcutsRoot = Join-Path $ProjectRoot "SharePointLinks"
$LogFile = Join-Path $PSScriptRoot "shortcuts-log.txt"

# Initialize log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== SharePoint Shortcuts Creation Started ==="

# Function to create a Windows shortcut
function Create-Shortcut {
    param(
        [string]$TargetPath,
        [string]$ShortcutPath
    )

    try {
        $WScriptShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WScriptShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = $TargetPath
        $Shortcut.Save()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($WScriptShell) | Out-Null
        return $true
    } catch {
        Write-Log "Failed to create shortcut: $ShortcutPath - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to find OneDrive SharePoint folders
function Find-SharePointFolders {
    Write-Log "Searching for OneDrive SharePoint sync locations..."

    $possiblePaths = @()

    # Check common OneDrive business locations
    $oneDrivePaths = @(
        "$env:USERPROFILE\OneDrive - Gain Servicing",
        "$env:USERPROFILE\Gain Servicing",
        "$env:OneDriveCommercial"
    )

    foreach ($path in $oneDrivePaths) {
        if ($path -and (Test-Path $path)) {
            $resolvedPaths = Resolve-Path $path -ErrorAction SilentlyContinue
            foreach ($resolved in $resolvedPaths) {
                if ($resolved -and $resolved.Path) {
                    $possiblePaths += $resolved.Path
                }
            }
        }
    }

    # Also check registry for OneDrive business accounts
    $registryPaths = @(
        "HKCU:\Software\Microsoft\OneDrive\Accounts\Business1",
        "HKCU:\Software\Microsoft\OneDrive\Accounts\Business2"
    )

    foreach ($regPath in $registryPaths) {
        try {
            $userFolder = Get-ItemProperty -Path $regPath -Name "UserFolder" -ErrorAction SilentlyContinue
            if ($userFolder -and (Test-Path $userFolder.UserFolder)) {
                $possiblePaths += $userFolder.UserFolder
            }
        } catch {
            # Registry key doesn't exist, continue
        }
    }

    return $possiblePaths | Select-Object -Unique
}

# Function to find specific SharePoint library folders
function Find-SharePointLibraries {
    param([string[]]$SearchPaths)

    $libraries = @{}

    foreach ($basePath in $SearchPaths) {
        Write-Log "Searching in: $basePath"

        # Look for common SharePoint library patterns
        $patterns = @("Marketing", "Finance", "Legal", "Sales", "Technology", "FDrive", "Shared Documents")

        foreach ($pattern in $patterns) {
            $found = Get-ChildItem -Path $basePath -Directory -Filter "*$pattern*" -ErrorAction SilentlyContinue
            foreach ($folder in $found) {
                $libraries[$folder.Name] = $folder.FullName
                Write-Log "Found SharePoint library: $($folder.Name) at $($folder.FullName)" "SUCCESS"
            }
        }
    }

    return $libraries
}

# Function to create shortcuts for a folder
function Create-ShortcutsForFolder {
    param(
        [string]$SourcePath,
        [string]$DestPath,
        [string]$Category,
        [int]$DaysOld = 365
    )

    Write-Log "Creating shortcuts for $Category from $SourcePath to $DestPath"

    if (-not (Test-Path $SourcePath)) {
        Write-Log "Source path does not exist: $SourcePath" "WARN"
        return 0
    }

    # Create destination if it doesn't exist
    if (-not (Test-Path $DestPath)) {
        New-Item -ItemType Directory -Path $DestPath -Force | Out-Null
        Write-Log "Created destination: $DestPath"
    }

    $cutoffDate = (Get-Date).AddDays(-$DaysOld)
    $shortcutCount = 0
    $skippedCount = 0

    # Get all files recursively
    $files = Get-ChildItem -Path $SourcePath -Recurse -File -ErrorAction SilentlyContinue |
             Where-Object { $_.LastWriteTime -gt $cutoffDate }

    foreach ($file in $files) {
        $relativePath = $file.FullName.Substring($SourcePath.Length).TrimStart('\')
        $shortcutName = $file.Name + ".lnk"
        $shortcutFullPath = Join-Path $DestPath $relativePath
        $shortcutFolder = Split-Path -Parent $shortcutFullPath
        $shortcutPath = Join-Path $shortcutFolder $shortcutName

        # Create subfolder structure if needed
        if (-not (Test-Path $shortcutFolder)) {
            New-Item -ItemType Directory -Path $shortcutFolder -Force | Out-Null
        }

        # Check if shortcut already exists and target hasn't changed
        $shouldCreate = $false
        if (-not (Test-Path $shortcutPath)) {
            $shouldCreate = $true
            $reason = "New"
        } elseif ($Refresh) {
            Remove-Item $shortcutPath -Force
            $shouldCreate = $true
            $reason = "Refresh"
        } else {
            # Check if target file is newer than shortcut
            $shortcutFile = Get-Item $shortcutPath
            if ($file.LastWriteTime -gt $shortcutFile.LastWriteTime) {
                Remove-Item $shortcutPath -Force
                $shouldCreate = $true
                $reason = "Updated"
            } else {
                $skippedCount++
                continue
            }
        }

        if ($shouldCreate) {
            $success = Create-Shortcut -TargetPath $file.FullName -ShortcutPath $shortcutPath
            if ($success) {
                Write-Log "  [$reason] Created shortcut: $relativePath" "SUCCESS"
                $shortcutCount++
            }
        }
    }

    Write-Log "  Total: $($files.Count) files found, $shortcutCount shortcuts created, $skippedCount skipped"
    return $shortcutCount
}

# Main execution
try {
    # Find SharePoint folders
    $sharePointPaths = Find-SharePointFolders

    if ($sharePointPaths.Count -eq 0) {
        Write-Log "No OneDrive SharePoint sync folders found!" "ERROR"
        Write-Log "Please sync your SharePoint library via OneDrive first." "ERROR"
        Write-Log "Steps:" "INFO"
        Write-Log "  1. Open SharePoint in browser" "INFO"
        Write-Log "  2. Navigate to the library (Marketing, Finance, etc.)" "INFO"
        Write-Log "  3. Click 'Sync' button" "INFO"
        Write-Log "  4. Wait for OneDrive to sync" "INFO"
        Write-Log "  5. Run this script again" "INFO"
        exit 1
    }

    Write-Log "Found OneDrive paths: $($sharePointPaths -join ', ')"

    # Find specific libraries
    $libraries = Find-SharePointLibraries -SearchPaths $sharePointPaths

    if ($libraries.Count -eq 0) {
        Write-Log "No SharePoint libraries found in synced folders" "WARN"
        Write-Log "OneDrive paths checked: $($sharePointPaths -join ', ')" "INFO"
        exit 1
    }

    # Create shortcuts root folder
    if (-not (Test-Path $ShortcutsRoot)) {
        New-Item -ItemType Directory -Path $ShortcutsRoot -Force | Out-Null
        Write-Log "Created SharePointLinks folder: $ShortcutsRoot"
    }

    # If refresh, clean out old shortcuts
    if ($Refresh) {
        Write-Log "Refresh mode: Removing existing shortcuts..." "WARN"
        Get-ChildItem -Path $ShortcutsRoot -Recurse -Filter "*.lnk" | Remove-Item -Force
    }

    # Create shortcuts for each library
    $totalShortcuts = 0

    foreach ($libName in $libraries.Keys) {
        # Apply department filter if specified
        if ($DepartmentFilter -and $libName -notlike "*$DepartmentFilter*") {
            Write-Log "Skipping $libName (filtered)" "INFO"
            continue
        }

        $sourcePath = $libraries[$libName]
        $destPath = Join-Path $ShortcutsRoot $libName

        $count = Create-ShortcutsForFolder `
            -SourcePath $sourcePath `
            -DestPath $destPath `
            -Category $libName `
            -DaysOld $DaysOld

        $totalShortcuts += $count
    }

    Write-Log "=== Shortcut Creation Complete ===" "SUCCESS"
    Write-Log "Total shortcuts created: $totalShortcuts"
    Write-Log "Shortcuts location: $ShortcutsRoot"

    # Create README in shortcuts folder
    $readmePath = Join-Path $ShortcutsRoot "README.md"
    $readmeContent = @"
# SharePoint Links

This folder contains Windows shortcuts (.lnk files) to files synced from SharePoint via OneDrive.

## How to Use

**Click any .lnk file** to open the actual file from OneDrive in its default application.

## Folder Structure

Each subfolder corresponds to a SharePoint library:
- Marketing/ - Marketing files
- Finance/ - Finance files
- Legal/ - Legal files
- Sales/ - Sales files
- Technology/ - Technology files

## Benefits

✅ Tiny file size (shortcuts only, ~2KB each)
✅ Click to open files instantly
✅ No file duplication
✅ Always up-to-date (points to OneDrive files)
✅ Works in File Explorer and VS Code

## Updating Shortcuts

To refresh shortcuts when new files are added:

``````powershell
cd System\scripts
.\Create-SharePointShortcuts.ps1 -Refresh
``````

## Last Updated

Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Total shortcuts: $totalShortcuts
"@

    $readmeContent | Set-Content -Path $readmePath
    Write-Log "Created README.md in shortcuts folder"

    Write-Log "=== Success! ===" "SUCCESS"
    Write-Log "You can now browse SharePointLinks/ and click shortcuts to open files!"

} catch {
    Write-Log "ERROR: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}

Write-Log "=== SharePoint Shortcuts Creation Finished ==="

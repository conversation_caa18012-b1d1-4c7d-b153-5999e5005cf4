# Install-CygpathShim.ps1
# Installs cygpath shim for Claude Code compatibility with Git Bash

Write-Host "=== Cygpath Shim Installer ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "This will make Claude Code work properly with Git Bash on Windows."
Write-Host ""

# Find Git Bash bin directory
$gitBashPaths = @(
    "C:\Dev\Git\usr\bin",
    "C:\Program Files\Git\usr\bin",
    "C:\Program Files (x86)\Git\usr\bin",
    "$env:LOCALAPPDATA\Programs\Git\usr\bin"
)

$gitBinPath = $null
foreach ($path in $gitBashPaths) {
    if (Test-Path $path) {
        $gitBinPath = $path
        break
    }
}

if (-not $gitBinPath) {
    Write-Host "❌ Could not find Git Bash installation." -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Git for Windows from: https://git-scm.com/download/win"
    exit 1
}

Write-Host "✅ Found Git Bash at: $gitBinPath" -ForegroundColor Green

# Copy cygpath shim
$sourceShim = "$PSScriptRoot\cygpath"
$targetShim = "$gitBinPath\cygpath"

if (-not (Test-Path $sourceShim)) {
    Write-Host "❌ Cygpath shim not found at: $sourceShim" -ForegroundColor Red
    exit 1
}

try {
    Write-Host ""
    Write-Host "Installing cygpath shim to Git Bash bin directory..."

    # Check if we need admin rights
    if (-not (Test-Path $targetShim -ErrorAction SilentlyContinue)) {
        Copy-Item $sourceShim $targetShim -Force -ErrorAction Stop
    } else {
        Write-Host "⚠️  Cygpath already exists. Replacing..." -ForegroundColor Yellow
        Copy-Item $sourceShim $targetShim -Force -ErrorAction Stop
    }

    Write-Host "✅ Cygpath shim installed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Testing installation..." -ForegroundColor Cyan

    # Test the shim
    $testPath = "C:\Users\<USER>\file.txt"
    $result = & bash -c "cygpath -u '$testPath'" 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test passed: $testPath -> $result" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎉 Installation complete! Claude Code should now work properly." -ForegroundColor Green
    } else {
        Write-Host "⚠️  Test failed, but shim was installed." -ForegroundColor Yellow
        Write-Host "Error: $result"
    }

} catch {
    Write-Host ""
    Write-Host "❌ Installation failed: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "You may need to run this script as Administrator." -ForegroundColor Yellow
    Write-Host "Right-click PowerShell and select 'Run as Administrator', then try again."
    exit 1
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

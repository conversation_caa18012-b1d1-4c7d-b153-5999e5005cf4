﻿"""
Semantic search using pre-generated embeddings
"""

import json
import sys
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

def main():
    query = sys.argv[1]
    embeddings_file = sys.argv[2]
    top_n = int(sys.argv[3])
    threshold = float(sys.argv[4])

    # Load embeddings
    with open(embeddings_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Load model
    model = SentenceTransformer(data['model'])

    # Generate query embedding
    query_embedding = model.encode(query)

    # Convert stored embeddings to numpy array
    file_keys = list(data['embeddings'].keys())
    doc_embeddings = np.array([data['embeddings'][key] for key in file_keys])

    # Calculate similarities
    similarities = cosine_similarity([query_embedding], doc_embeddings)[0]

    # Get results above threshold
    results = []
    for i, (file_key, similarity) in enumerate(zip(file_keys, similarities)):
        if similarity >= threshold:
            results.append({
                "file_key": file_key,
                "similarity": float(similarity),
                "rank": 0
            })

    # Sort by similarity
    results.sort(key=lambda x: x['similarity'], reverse=True)

    # Add ranks
    for rank, result in enumerate(results[:top_n], 1):
        result['rank'] = rank

    # Output as JSON
    print(json.dumps(results[:top_n], indent=2))

if __name__ == "__main__":
    main()

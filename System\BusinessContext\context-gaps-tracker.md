# Context Gaps & Open Items Tracker

**Last Updated**: 2025-10-29
**Purpose**: Master tracking file for context gaps, ambiguities, conflicts, and unanswered questions across all domains
**Status**: Active - Updated continuously as gaps identified

---

## Overview

This file maintains a **consolidated view of all open context items** across the business context system. Each domain (Company, Products, Operations, Departments, Strategy, Glossary) has its own detailed open items file, and this tracker provides the executive summary.

**Purpose**: Ensure context gaps are systematically identified, tracked, and resolved.

---

## Quick Summary Statistics

**Total Open Items**: 85+ (as of 2025-10-29) - **10 items resolved from financials!**
**Last Updates**:
- 2025-10-29: Leadership Financials 251006 (Company/Products)
- 2025-10-29: Department READMEs populated (Legal/Sales/Marketing/Technology)

| Domain | Open Items | Priority Breakdown | Status |
|--------|------------|-------------------|--------|
| Company | 8 | 🔴 0 \| 🟡 3 \| 🟢 5 | Metrics populated ✅ |
| Products | 10 | 🔴 1 \| 🟡 3 \| 🟢 6 | Major update ✅ |
| Operations | 0 (not started) | - | - |
| Departments | 30+ | 🔴 0 \| 🟡 10 \| 🟢 20 | **Foundations captured ✅** |
| Strategy | 0 (not started) | - | - |
| Glossary | 5 | 🔴 0 \| 🟡 2 \| 🟢 3 | - |
| Conflicts | 1 (PA definition) | 🔴 0 \| 🟡 1 \| 🟢 0 | Clarified ✅ |

**Priority Legend**:
- 🔴 **CRITICAL** - Blocking important decisions or high-impact area
- 🟡 **HIGH** - Important for complete understanding, should resolve soon
- 🟢 **NORMAL** - Nice to have, fill in when convenient

---

## Critical Open Items (🔴 Action Needed)

### Products Domain

#### 1. Express Funding Default Rate
**Priority**: 🔴 CRITICAL
**Question**: What is the actual default rate for Express Funding?
**Current Status**: Estimated at ~5% from CFO conversation [UNVERIFIED]
**Why Critical**: Affects risk assessment and pricing decisions
**Resolution Path**: CFO to provide verified number from Express Funding Dashboard
**Assigned To**: CEO → CFO
**Target Date**: Within 1 week
**Tracking**: [products-open-items.md](#)

#### 2. PCA Sub-Products Differentiation [PARTIALLY RESOLVED ✅]
**Priority**: 🟡 HIGH (downgraded from CRITICAL)
**Question**: What differentiates Mustang, Jordan (not found), and Sigma products functionally?
**Current Status**: RESOLVED - Performance data captured from Leadership Financials 251006:
- Mustang Funding (Facility): $1.51M collections, 85.4% of target
- Sigma Funding LLC (PCA): $168K collections, 69.7% of target
- "Express Funding (PCA)" found (may overlap with main Express Funding)
- "Jordan" product NOT found in financials - may be discontinued or renamed
**Still Needed**: Product positioning and differentiation strategy from VP Sales
**Assigned To**: CEO → VP Sales (for strategic differentiation)
**Target Date**: Next product review
**Tracking**: [products-overview.md](./Products/products-overview.md) - Lines 125-140

---

## High Priority Open Items (🟡 Resolve Soon)

### Company Domain

#### 1. Company Founding & History
**Priority**: 🟡 HIGH
**Questions**:
- When was Gain Servicing founded?
- Key historical milestones?
- How has company evolved?
**Why Important**: Context for understanding current strategy and culture
**Resolution Path**: CEO to provide history or point to documents
**Tracking**: [company-open-items.md](#)

#### 2. Total Assets Under Management
**Priority**: 🟡 HIGH
**Question**: What is total AUM? Total annual revenue?
**Why Important**: Understanding scale and growth trajectory
**Resolution Path**: CFO financial summary
**Tracking**: [company-open-items.md](#)

#### 3. Investor Structure
**Priority**: 🟡 HIGH
**Question**: Who are the investors? Ownership structure?
**Why Important**: Understanding governance and stakeholder relationships
**Resolution Path**: CEO summary (may be confidential - decide what to document)
**Tracking**: [company-open-items.md](#)

---

### Products Domain

#### 4. PA Product Definition
**Priority**: 🟡 HIGH
**Question**: Is "PA" a product line (Purchased products) or customer type (Personal Injury Attorney)?
**Current Status**: Used interchangeably across documents
**Why Important**: Affects product documentation, KPIs, sales materials
**Resolution Path**: CEO to clarify definition, update glossary
**Assigned To**: CEO
**Target Date**: Next review session
**Tracking**: [products-open-items.md](#) + [glossary-open-items.md](#)
**Related Conflict**: See context conflicts section

#### 5. Express Funding Volume Targets
**Priority**: 🟡 HIGH
**Question**: What are volume targets (deals per month/year)?
**Why Important**: Understanding growth expectations and capacity planning
**Resolution Path**: CFO or VP Sales to provide targets
**Tracking**: [products-open-items.md](#)

#### 6. Product Revenue Mix [RESOLVED ✅]
**Priority**: ~~🟡 HIGH~~ **COMPLETED**
**Question**: What % of revenue comes from each product line?
**Resolution Date**: 2025-10-29
**Answer** (from Leadership Financials 251006 - 2025 YTD):
- **Servicing**: $43.8M (56% of collections)
- **PCA Inorganic**: $8.5M (11%)
- **PCA Organic**: $8.9M (11%)
- **Partial Advance**: $11.6M (15%)
- **Single Medical**: $2.2M (3%)
- **Purchased Medical**: $1.8M (2%)
- **Asset Sale**: $0.8M (1%)
**Total Collections**: $77.6M
**Context Updated**: [company-overview.md](./Company/company-overview.md) - Lines 125-138
**Quality**: Verified from official financial report

---

### Glossary Domain

#### 7. PA Definition Conflict
**Priority**: 🟡 HIGH
**Issue**: "PA" used for both product and customer type
**Status**: Identified conflict, awaiting CEO clarification
**Resolution Path**: CEO to decide on standard usage
**Tracking**: [glossary-open-items.md](#)
**Related Conflict**: Context conflicts section

---

## Normal Priority Open Items (🟢 Fill When Convenient)

### Company Domain

- Company headquarters location
- Total team size and org structure
- Office footprint (locations, remote policy)
- Company mission and values statement
- 3-5 year vision

**Tracking**: [company-open-items.md](#)

---

### Products Domain

- Detailed underwriting criteria for each product
- Customer satisfaction metrics (NPS if tracked)
- Product lifecycle stage assessment
- Cross-sell/upsell opportunities
- New product pipeline or ideas

**Tracking**: [products-open-items.md](#)

---

### Glossary Domain

- Medical/healthcare acronyms used in operations
- Legal terminology shortcuts
- Industry-specific jargon

**Tracking**: [glossary-open-items.md](#)

---

## Unresolved Context Conflicts

### Active Conflicts Awaiting Resolution

#### 1. PA Definition (Duplicate - See Above)
**Conflict Type**: Definitional
**Priority**: 🟡 HIGH
**Status**: Escalated to CEO
**Date Identified**: 2025-10-28
**Tracking**: Context conflicts log + Products open items + Glossary open items

---

## Domain Coverage Assessment

### Domains with Context

| Domain | Coverage | Quality | Open Items | Status |
|--------|----------|---------|------------|--------|
| **Company** | 55% | Good | 8 | **Major update** - financials populated ✅ |
| **Products** | 70% | Good | 10 | **Major update** - all products detailed ✅ |
| **Operations** | 5% | Draft | Unknown | Started - LFBD team captured |
| **Departments** | 40% | Good | 30+ | **NEW** - 4 departments documented ✅ |
| **Strategy** | 0% | - | Unknown | Not started |
| **Glossary** | 20% | Draft | 5 | Started - acronyms documented |

**Coverage Assessment**:
- **0-25%**: Minimal - needs major work
- **26-50%**: Foundation - basic structure exists, needs details
- **51-75%**: Good - most questions answered, some gaps
- **76-100%**: Comprehensive - few gaps, well documented

---

## Recently Resolved Items

### Resolved on 2025-10-29 (Source: Leadership Financials 251006)

**10 items resolved through financial report analysis**:

1. **Product Revenue Mix** (🟡 HIGH → ✅ RESOLVED)
   - Captured complete revenue breakdown by product
   - Updated: company-overview.md, products-overview.md

2. **Partial Advance Product Details** (🟢 NORMAL → ✅ RESOLVED)
   - $14.8M cash out, $11.6M collections, 1.69x MOIC
   - 17,440 fundings YTD, $4,855 avg invoice

3. **Single Medical Product Economics** (🟢 NORMAL → ✅ RESOLVED)
   - $1.80M cash out, $2.22M collections (120% of target!)
   - 2,162 invoices, $2,082 avg, 49% collection rate

4. **Purchased Medical Product Economics** (🟢 NORMAL → ✅ RESOLVED)
   - $189K cash out (minimal - likely legacy), 1,419 invoices
   - 51% collection rate, 1.52x MOIC

5. **PCA Organic vs Inorganic Differentiation** (🟡 HIGH → ✅ RESOLVED)
   - Organic: Direct originations, 1,446 cases, $3,440 avg
   - Inorganic: Purchased portfolios, 859 cases, $20,836 avg

6. **PCA Sub-Products Performance** (🔴 CRITICAL → 🟡 HIGH partial)
   - Mustang, Sigma performance captured
   - Jordan not found (discontinued or renamed)

7. **Servicing Product Details** (🟢 NORMAL → ✅ RESOLVED)
   - $43.8M collections, $130.6M invoices managed
   - Three clients: ATI (58%), GSO, Other

8. **Company Financial Metrics** (🟢 NORMAL → ✅ RESOLVED)
   - Q4 2025 YTD: $77.6M collections, $39.6M cash out
   - Overall 1.58x MOIC, meeting/exceeding most targets

9. **PA Product Definition Clarity** (🟡 HIGH → ✅ RESOLVED)
   - PA = "PA & Purchase Financials" dashboard tracking Single + Purchased Medical
   - Clarified it's a product category, not just customer type

10. **Servicing Clients & Volume** (🟢 NORMAL → ✅ RESOLVED)
    - ATI: $22.8M collections, $102.1M invoices
    - GSO: $19.0M collections, $20.8M invoices
    - Other: $1.9M collections, $7.7M invoices

### Department Context Populated on 2025-10-29 (Source: Department READMEs)

**4 department context files created** - Departments domain moved from 0% to 40% coverage:

1. **Legal Department** (NEW ✅)
   - Direct report: General Counsel
   - Key objectives: Litigation, new account docs, lender relations (East West Bank), securitization
   - KPIs: Active cases, documentation metrics, covenant compliance, securitization status
   - CEO alert criteria: Lawsuits, covenant violations, regulatory inquiries
   - Source: `Legal/README.md`
   - Created: `System/BusinessContext/Departments/legal-context.md`

2. **Sales Department** (NEW ✅)
   - Direct report: VP Sales
   - 3-tier target system: Tier 1 (>$500K, CEO-engaged), Tier 2 ($250-$500K), Tier 3 (<$250K)
   - Target: 30% conversion of high-value targets
   - 10-component proposal framework with ROI demonstration
   - CEO alert criteria: Tier 1 opportunities, competitive threats, major deals
   - Source: `Sales/README.md`
   - Created: `System/BusinessContext/Departments/sales-context.md`

3. **Marketing Department** (NEW ✅)
   - Direct report: VP Marketing
   - 5 core value propositions: Superior collections, cost efficiency, technology advantage, compliance, client service
   - ROI calculator framework: Collection improvement + cost savings
   - Sales collateral: Product one-pagers (quarterly updates - Oct 2025 current)
   - Competitive positioning: "Technology-Enabled Industry Expertise"
   - Source: `Marketing/README.md`
   - Created: `System/BusinessContext/Departments/marketing-context.md`

4. **Technology Department** (NEW ✅)
   - Direct report: CTO
   - **Critical priority**: AI Case Value Prediction (60% complete, Q1 2026 launch)
   - Goal: Reduce underwriting from 2-4 hours → 15 minutes with >85% accuracy
   - Automated underwriting: 70% automation target
   - Technology roadmap: Q4 2025 - Q2 2026 detailed
   - CEO alert criteria: System outages, security breaches, AI project risks
   - Sources: `Technology/README.md` + `Technology/roadmap/technology-roadmap.md`
   - Created: `System/BusinessContext/Departments/technology-context.md`

**Key Gaps Identified** (30+ total, by department):

**Legal (7-8 gaps)**:
- Team size and composition
- Target turnaround times for agreements
- Current active litigation count
- Covenant compliance ratios
- Securitization structures details
- Outside counsel relationships

**Sales (8-10 gaps)**:
- Team size and territories
- Current pipeline by tier (quantity and value)
- Current conversion rates vs targets
- Primary competitors identification
- Win/loss analysis results
- Geographic focus areas

**Marketing (7-8 gaps)**:
- Team size and composition
- Case studies inventory
- Win rate with/without ROI demo
- Competitor analysis details
- Material effectiveness metrics
- Actual collection rate improvements achieved

**Technology (8-10 gaps)**:
- Team size and composition
- Current technology stack details
- AI model architecture specifics
- Current performance baselines
- Technology vendors and partners
- Annual technology budget

---

## Systematic Gap Identification

### When Gaps Are Identified

**Automatic triggers for gap identification**:

1. **During context organization** - AI notices questions while organizing captures
2. **During conflict detection** - Missing information prevents resolution
3. **During CEO questions** - CEO asks about something not documented
4. **During quarterly audit** - Systematic review reveals gaps
5. **During template application** - Template sections remain unfilled
6. **During cross-referencing** - Referenced context doesn't exist

**AI Assistant Actions**:
- Add to domain-specific open items file
- Update this tracker
- Categorize by priority (Critical/High/Normal)
- Suggest resolution path
- Assign if clear owner

---

## Resolution Process

### For Critical Items (🔴)

1. **Immediate flag to CEO**: "Critical context gap identified: [description]"
2. **Block related decisions**: Note that decisions in this area lack complete context
3. **Fast-track resolution**: CEO prioritizes getting answers
4. **Update promptly**: Resolve within days, not weeks

### For High Priority Items (🟡)

1. **Flag in weekly review**: Include in CEO's weekly context summary
2. **Batch resolution**: Group related questions for single meeting
3. **Set timeline**: Resolve within 2-4 weeks
4. **Track progress**: Follow up if not resolved

### For Normal Priority Items (🟢)

1. **Monthly review**: Include in monthly organization session
2. **Opportunistic resolution**: Fill in when convenient (e.g., during related meetings)
3. **No pressure**: These are nice-to-have improvements
4. **Track passively**: Monitor but don't actively push

---

## Integration with Other Systems

### CEO Dashboard Integration

**Recommended**: Create `CEO-Dashboard/context-status.md` with:
- Count of critical open items
- Quick links to high-priority gaps
- Recent resolutions
- Context health score

### Weekly CEO Summary

Include section:
```markdown
## Context Gaps Status
- 🔴 Critical: 2 items (action needed this week)
- 🟡 High: 8 items (batch resolution recommended)
- 🟢 Normal: 15 items (fill opportunistically)

**Critical Actions This Week**:
1. [Item description] - needs [person]
2. [Item description] - needs [person]
```

### Monthly Organization Session

Agenda item:
- Review all open items
- Prioritize any newly identified gaps
- Resolve batch of high-priority items
- Archive resolved items

---

## Domain-Specific Open Items Files

Each domain has its own detailed tracking file:

- **Company**: [company-open-items.md](./Company/company-open-items.md)
- **Products**: [products-open-items.md](./Products/products-open-items.md)
- **Operations**: [operations-open-items.md](./Operations/operations-open-items.md)
- **Departments**: [departments-open-items.md](./Departments/departments-open-items.md)
- **Strategy**: [strategy-open-items.md](./Strategy/strategy-open-items.md)
- **Glossary**: [glossary-open-items.md](./Glossary/glossary-open-items.md)

**What's in domain files**:
- Detailed questions and context
- Investigation notes
- Partial answers or theories
- Related sources to check
- More granular priority/status tracking

---

## Gap Categories

### Types of Gaps

**1. Missing Information**
- Question has no answer yet
- Example: "What is team size?"

**2. Incomplete Information**
- Partial answer, needs more detail
- Example: "Know 4 products exist, need details on each"

**3. Ambiguous Information**
- Multiple interpretations possible
- Example: "PA" used inconsistently

**4. Unverified Information**
- Information captured but not confirmed
- Example: "~5% default rate [ESTIMATE]"

**5. Outdated Information**
- Marked [REVIEW NEEDED], may be stale
- Example: Context >3 months old without verification

**6. Conflicting Information**
- Multiple sources contradict
- Example: "$50K vs $75K average case size"

---

## Quarterly Gap Analysis

### Q4 2025 Gap Analysis (Current)

**Date**: 2025-10-28
**Status**: Initial system creation

**Major Gap Areas**:
1. **Operations** - No context captured yet (0% coverage)
2. **Departments** - No context captured yet (0% coverage)
3. **Strategy** - No context captured yet (0% coverage)
4. **Products** - Foundation exists but needs significant detail (40% coverage)

**Recommended Priorities for Next Quarter**:
1. Resolve critical product questions (default rates, product differentiation)
2. Begin Operations context (underwriting, collections processes)
3. Capture strategic context (objectives, growth plans)
4. Fill company history and structure details

---

## Maintenance

### AI Assistant Responsibilities

**Continuous**:
- Identify gaps during context organization
- Add to domain-specific files
- Update this tracker
- Categorize priority

**Weekly**:
- Flag critical items to CEO
- Include gaps in weekly summary

**Monthly**:
- Consolidate gap count
- Review resolution progress
- Suggest batch resolution sessions

**Quarterly**:
- Comprehensive gap analysis
- Domain coverage assessment
- Update priorities based on business needs

---

## Success Metrics

**Gap tracking is working when**:

✅ **No surprises** - CEO knows what context exists and what doesn't
✅ **Systematic resolution** - Gaps filled progressively, not randomly
✅ **Prioritized effort** - Critical gaps resolved first
✅ **Clear ownership** - Each gap has resolution path and owner
✅ **Visible progress** - Can track improvement over time
✅ **Quality control** - Ambiguities and conflicts tracked until resolved

**Target Coverage by End of Q1 2026**:
- Company: 75%+ (comprehensive)
- Products: 75%+ (comprehensive)
- Operations: 60%+ (good)
- Departments: 60%+ (good)
- Strategy: 60%+ (good)
- Glossary: 80%+ (comprehensive)

---

## Templates for Gap Documentation

### Template: Adding New Gap

```markdown
#### [Gap Number]. [Gap Title]
**Priority**: 🔴 CRITICAL | 🟡 HIGH | 🟢 NORMAL
**Type**: Missing | Incomplete | Ambiguous | Unverified | Outdated | Conflicting
**Question**: [What do we need to know?]
**Current Status**: [What do we know currently?]
**Why [Priority]**: [Why this matters at this priority level]
**Resolution Path**: [How to resolve - who to ask, what to check]
**Assigned To**: [CEO → Person] or [CEO decides]
**Target Date**: [When should this be resolved?]
**Tracking**: [Link to domain-specific file]
**Related**: [Links to related gaps or conflicts]
```

### Template: Resolving Gap

```markdown
**Resolution Date**: YYYY-MM-DD
**Resolved By**: [Who provided answer]
**Answer**: [The resolution]
**Context Updated**: [Which files were updated]
**Quality**: [Verified | Estimate | Partial]
```

---

## Related Documentation

- **CONTEXT-COLLECTION-GUIDE.md** - Overall context management process
- **Domain-specific open items files** - Detailed tracking per domain
- **CONTEXT-CONFLICT-TEMPLATE.md** - For unresolved conflicts
- **CLAUDE.md** - AI assistant guidelines for gap detection

---

## Current Status

**System Created**: 2025-10-28
**Current Phase**: Initial gap identification from existing context
**Next Action**: CEO review of critical gaps, batch resolution session for high-priority items

---

*This tracker is the "TODO list" for business context. Keep it current, keep it prioritized, keep resolving gaps.*

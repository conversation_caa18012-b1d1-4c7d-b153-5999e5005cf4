# Business Context - Organized Knowledge Base

**Last Updated**: 2025-10-28
**Purpose**: Structured repository of business knowledge about Gain Servicing
**Status**: Active

---

## Overview

This folder contains **organized, curated business context** following the three-tier system defined in `CONTEXT-COLLECTION-GUIDE.md`.

**This is Tier 2** - structured, searchable, version-controlled knowledge.

## Folder Structure

```
BusinessContext/
├── README.md (this file)
├── _templates/              # Templates for organizing context
│   ├── company-context-template.md
│   ├── product-context-template.md
│   ├── department-context-template.md
│   └── process-context-template.md
│
├── Company/                 # Company-level context
│   ├── company-overview.md
│   ├── business-model.md
│   ├── industry-landscape.md
│   ├── competitive-analysis.md
│   └── stakeholders.md
│
├── Products/                # Product/service details
│   ├── products-overview.md
│   ├── pa-purchased.md
│   ├── express-funding.md
│   ├── pca-purchase-book.md
│   └── servicing.md
│
├── Operations/              # How work gets done
│   ├── operations-overview.md
│   ├── underwriting-process.md
│   ├── collections-process.md
│   └── case-value-prediction.md
│
├── Departments/             # Department-specific context
│   ├── finance-context.md
│   ├── legal-context.md
│   ├── sales-context.md
│   ├── marketing-context.md
│   └── technology-context.md
│
├── Strategy/                # Strategic context
│   ├── strategic-objectives.md
│   ├── growth-strategy.md
│   └── technology-roadmap-context.md
│
├── Glossary/                # Terminology & definitions
│   ├── business-glossary.md
│   ├── acronyms.md
│   ├── kpi-definitions.md
│   └── product-terminology.md
│
└── _archive/                # Outdated context
    └── YYYY-MM/
```

## How to Use This Folder

### When Organizing Context (Weekly/Monthly)

1. **Start with raw captures** in `TempDocs/context-capture/`
2. **Determine domain**: Company, Product, Operation, Department, Strategy, or Glossary
3. **Use appropriate template** from `_templates/`
4. **Create or update file** in correct domain folder
5. **Add cross-references** to related files
6. **Commit to Git** with descriptive message

### When Creating New Context

**Always include**:
- Standard header (Last Updated, Owner, Status, Related)
- Quick summary (2-3 sentences)
- Proper cross-references
- Sources (where information came from)

**Quality checklist**:
- [ ] Clear and complete
- [ ] Current and accurate
- [ ] Cross-referenced to related files
- [ ] Consistent terminology (check Glossary)
- [ ] Follows template structure

### When Searching for Context

**By Domain**:
- Company info? → `Company/`
- Product details? → `Products/`
- Process/workflow? → `Operations/`
- Department-specific? → `Departments/`
- Strategic insight? → `Strategy/`
- Term definition? → `Glossary/`

**By AI Search**:
Ask Claude or use Serena tools to search semantically across all context.

## Maintenance

### Weekly Review
- Review new captures in `TempDocs/context-capture/`
- Identify urgent context needing immediate organization
- Update "Last Updated" dates on modified files

### Monthly Organization
- Full organization session (1-2 hours)
- Apply templates to raw captures
- Create cross-references
- Archive outdated content to `_archive/YYYY-MM/`
- Commit organized context to Git

### Quarterly Refresh
- Review all context for accuracy
- Update status tags (Draft → Active, etc.)
- Identify gaps in coverage
- Update Serena memories
- Update CEO-Dashboard shortcuts

## Quality Standards

### Status Tags
- **Draft** - Still gathering information
- **Active** - Current and complete
- **Review Needed** - May be outdated, needs verification
- **Outdated** - No longer accurate (move to `_archive/`)

### Naming Conventions
- Use `kebab-case.md` (lowercase, hyphens)
- Examples: `business-model.md`, `underwriting-process.md`
- Dates: Always `YYYY-MM-DD` format

### Cross-Referencing
Link related context files:
```markdown
**Related Context**:
- [Business Model](../Company/business-model.md)
- [Express Funding](../Products/express-funding.md)
```

## Integration with Other Systems

### CEO-Dashboard
Create shortcuts in `CEO-Dashboard/business-context-shortcuts.md` for quick access to frequently-used context.

### Serena AI Memories
When context stabilizes (quarterly), update Serena memory files in `.serena/memories/` to keep AI assistance current.

### SharePoint Documents
Context files may reference SharePoint documents tracked in `KEY-DOCUMENTS-REGISTRY.md`.

## Current Status

**Created**: 2025-10-28
**Files**: Templates and folder structure created
**Next Step**: Begin organizing context from `TempDocs/context-capture/`

---

## Related Documentation

- **CONTEXT-COLLECTION-GUIDE.md** - Complete three-tier strategy
- **CLAUDE.md** - AI assistant guidelines (includes context rules)
- **KEY-DOCUMENTS-REGISTRY.md** - Critical SharePoint documents

---

*This is your organized business knowledge repository. Keep it current, keep it clean, keep it useful.*

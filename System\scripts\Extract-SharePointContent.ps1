<#
.SYNOPSIS
    Extracts text content from SharePoint files (PDF, DOCX, XLSX, PPTX) for searchability

.DESCRIPTION
    This script:
    1. Reads index.json to find all SharePoint files
    2. Extracts text content using Python libraries
    3. Creates markdown summaries in docs/sharepoint-content/
    4. Makes content searchable via grep/Claude Code

.PARAMETER FileType
    Extract only specific file types (pdf, docx, xlsx, pptx)

.PARAMETER Department
    Extract only from specific department (Marketing, Finance, etc.)

.PARAMETER Force
    Re-extract files even if already extracted

.PARAMETER ListOnly
    Just list files without extracting

.EXAMPLE
    .\Extract-SharePointContent.ps1
    Extracts all files

.EXAMPLE
    .\Extract-SharePointContent.ps1 -FileType pdf -Department Marketing
    Extracts only PDF files from Marketing

.EXAMPLE
    .\Extract-SharePointContent.ps1 -ListOnly
    Lists what would be extracted without doing it

.NOTES
    Requires: Python 3.12+ with libraries (script will check/install)
    Output: docs/sharepoint-content/ (gitignored)
#>

[CmdletBinding()]
param(
    [string]$FileType = "",
    [string]$Department = "",
    [switch]$Force,
    [switch]$ListOnly
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$IndexFile = Join-Path $ProjectRoot "SharePointLinks\index.json"
$ConfigFile = Join-Path $ProjectRoot "SharePointLinks\config.json"
$OutputRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$LogFile = Join-Path $PSScriptRoot "extract-log.txt"
$PythonScript = Join-Path $PSScriptRoot "extract_content.py"

# Initialize log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== SharePoint Content Extraction Started ==="

# Check if index exists
if (-not (Test-Path $IndexFile)) {
    Write-Log "Index file not found: $IndexFile" "ERROR"
    Write-Log "Run .\Sync-SharePointIndex.ps1 first" "ERROR"
    exit 1
}

# Check if config exists
if (-not (Test-Path $ConfigFile)) {
    Write-Log "Config file not found: $ConfigFile" "ERROR"
    Write-Log "Run .\Sync-SharePointIndex.ps1 first" "ERROR"
    exit 1
}

# Check Python
Write-Log "Checking Python installation..."
try {
    $pythonVersion = python --version 2>&1
    Write-Log "Found: $pythonVersion" "SUCCESS"
} catch {
    Write-Log "Python not found! Please install Python 3.12+" "ERROR"
    exit 1
}

# Check/Install Python libraries
Write-Log "Checking Python libraries..."
$requiredLibraries = @("PyPDF2", "python-docx", "openpyxl", "python-pptx")
$missingLibraries = @()

foreach ($lib in $requiredLibraries) {
    $checkLib = python -c "import $($lib.Replace('-', '_'))" 2>&1
    if ($LASTEXITCODE -ne 0) {
        $missingLibraries += $lib
    }
}

if ($missingLibraries.Count -gt 0) {
    Write-Log "Missing Python libraries: $($missingLibraries -join ', ')" "WARN"
    Write-Log "Installing libraries..." "INFO"

    foreach ($lib in $missingLibraries) {
        Write-Log "  Installing $lib..."
        pip install $lib --quiet
        if ($LASTEXITCODE -eq 0) {
            Write-Log "    Installed $lib" "SUCCESS"
        } else {
            Write-Log "    Failed to install $lib" "ERROR"
        }
    }
}

# Load index and config
Write-Log "Loading index and config..."
$index = Get-Content $IndexFile -Raw -Encoding UTF8 | ConvertFrom-Json
$config = Get-Content $ConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json

Write-Log "Found $($index.files.Count) files in index"

# Filter files
$filesToProcess = $index.files

if ($FileType) {
    $filesToProcess = $filesToProcess | Where-Object { $_.extension -eq ".$FileType" }
    Write-Log "Filtered to $($filesToProcess.Count) $FileType files"
}

if ($Department) {
    $filesToProcess = $filesToProcess | Where-Object { $_.department -eq $Department }
    Write-Log "Filtered to $($filesToProcess.Count) files from $Department"
}

# Only process supported file types
$supportedExtensions = @(".pdf", ".docx", ".xlsx", ".pptx", ".png", ".jpg", ".jpeg")
$filesToProcess = $filesToProcess | Where-Object { $_.extension -in $supportedExtensions }
Write-Log "Processing $($filesToProcess.Count) supported files"

if ($ListOnly) {
    Write-Log "=== Files to Process (List Only Mode) ===" "INFO"
    foreach ($file in $filesToProcess) {
        Write-Log "  [$($file.extension)] $($file.path)"
    }
    Write-Log "=== List Complete (no extraction performed) ===" "SUCCESS"
    exit 0
}

# Create output directory
if (-not (Test-Path $OutputRoot)) {
    New-Item -ItemType Directory -Path $OutputRoot -Force | Out-Null
    Write-Log "Created output directory: $OutputRoot"
}

# Create Python extraction script if it doesn't exist
if (-not (Test-Path $PythonScript)) {
    Write-Log "Creating Python extraction script..."

    $pythonCode = @'
import sys
import json
from pathlib import Path

def extract_pdf(file_path):
    """Extract text from PDF"""
    try:
        import PyPDF2
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = []
            for page in reader.pages:
                text.append(page.extract_text())
            return '\n'.join(text)
    except Exception as e:
        return f"Error extracting PDF: {e}"

def extract_docx(file_path):
    """Extract text from DOCX"""
    try:
        from docx import Document
        doc = Document(file_path)
        text = []
        for para in doc.paragraphs:
            text.append(para.text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting DOCX: {e}"

def extract_xlsx(file_path):
    """Extract text from XLSX"""
    try:
        from openpyxl import load_workbook
        wb = load_workbook(file_path, read_only=True, data_only=True)
        text = []
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]
            text.append(f"\n## Sheet: {sheet_name}\n")
            for row in sheet.iter_rows(values_only=True):
                row_text = '\t'.join([str(cell) if cell is not None else '' for cell in row])
                if row_text.strip():
                    text.append(row_text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting XLSX: {e}"

def extract_pptx(file_path):
    """Extract text from PPTX"""
    try:
        from pptx import Presentation
        prs = Presentation(file_path)
        text = []
        for i, slide in enumerate(prs.slides):
            text.append(f"\n## Slide {i+1}\n")
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text.append(shape.text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error extracting PPTX: {e}"

def extract_content(file_path):
    """Extract content based on file type"""
    path = Path(file_path)
    ext = path.suffix.lower()

    extractors = {
        '.pdf': extract_pdf,
        '.docx': extract_docx,
        '.xlsx': extract_xlsx,
        '.pptx': extract_pptx
    }

    if ext in extractors:
        return extractors[ext](file_path)
    else:
        return f"Unsupported file type: {ext}"

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: extract_content.py <file_path>"}))
        sys.exit(1)

    file_path = sys.argv[1]

    try:
        content = extract_content(file_path)
        result = {
            "success": True,
            "content": content,
            "length": len(content)
        }
        print(json.dumps(result))
    except Exception as e:
        result = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(result))
'@

    $pythonCode | Set-Content -Path $PythonScript -Encoding UTF8
    Write-Log "Created Python script: $PythonScript" "SUCCESS"
}

# Process files
Write-Log "=== Starting Content Extraction ===" "INFO"
$extractedCount = 0
$skippedCount = 0
$errorCount = 0

foreach ($fileInfo in $filesToProcess) {
    $sourcePath = Join-Path $config.oneDriveRoot $fileInfo.path

    # Create markdown filename
    $relativePath = $fileInfo.path -replace '\\', '_' -replace ' ', '_'
    $mdFileName = "$($fileInfo.department)_$relativePath.md"
    $outputPath = Join-Path $OutputRoot $mdFileName

    # Check if already extracted
    if ((Test-Path $outputPath) -and -not $Force) {
        $skippedCount++
        continue
    }

    Write-Log "Extracting: $($fileInfo.name)" "INFO"

    # Check if source file exists
    try {
        $sourceFile = Get-Item $sourcePath -ErrorAction Stop
    } catch {
        Write-Log "  Source file not found: $sourcePath" "WARN"
        $errorCount++
        continue
    }

    # Extract content using Python (different script for images)
    try {
        $isImage = $fileInfo.extension -in @(".png", ".jpg", ".jpeg")

        if ($isImage) {
            # Use image metadata extraction script
            $imageScript = Join-Path $PSScriptRoot "extract_image_metadata.py"
            $metaResult = python $imageScript $sourcePath | ConvertFrom-Json

            if ($metaResult.success) {
                # Create result object with metadata
                $result = @{
                    success = $true
                    metadata = $metaResult.metadata
                    ai_description = "[AI description pending - will be added during image analysis phase]"
                    length = 50
                }
            } else {
                $result = @{
                    success = $false
                    error = $metaResult.error
                }
            }
        } else {
            # Use document extraction script
            $result = python $PythonScript $sourcePath | ConvertFrom-Json
        }

        if ($result.success) {
            # Create markdown summary
            if ($isImage) {
                # Image-specific markdown with metadata
                $metadataSection = ""
                if ($result.metadata) {
                    $meta = $result.metadata
                    $metadataSection = @"

## Image Metadata

- **Dimensions**: $($meta.width) x $($meta.height) pixels ($($meta.megapixels) MP)
- **Format**: $($meta.format)
- **Color Mode**: $($meta.mode)
- **File Size**: $($meta.file_size_kb) KB
"@
                    if ($meta.exif) {
                        $metadataSection += "`n`n### EXIF Data`n`n"
                        foreach ($key in ($meta.exif.PSObject.Properties.Name | Sort-Object)) {
                            $value = $meta.exif.$key
                            $metadataSection += "- **$key**: $value`n"
                        }
                    }
                }

                $markdown = @"
# $($fileInfo.name)

**Source**: ``$($fileInfo.path)``
**Department**: $($fileInfo.department)
**Type**: $($fileInfo.extension) (Image)
**Size**: $($fileInfo.sizeMB) MB
**Modified**: $($fileInfo.modified)
**Extracted**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
$metadataSection

---

## AI-Generated Description

$($result.ai_description)

**To complete**: Ask Claude Code to analyze this image and update the description.
Image location: ``$sourcePath``

---

*Image metadata extracted - AI description pending*
"@
            } else {
                # Document markdown
                $markdown = @"
# $($fileInfo.name)

**Source**: ``$($fileInfo.path)``
**Department**: $($fileInfo.department)
**Type**: $($fileInfo.extension)
**Size**: $($fileInfo.sizeMB) MB
**Modified**: $($fileInfo.modified)
**Extracted**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

---

## Content

$($result.content)

---

*Content extracted from SharePoint via OneDrive*
*Length: $($result.length) characters*
"@
            }

            $markdown | Set-Content -Path $outputPath -Encoding UTF8
            Write-Log "  Extracted $($result.length) characters → $mdFileName" "SUCCESS"
            $extractedCount++

            # Progress indicator
            if ($extractedCount % 10 -eq 0) {
                Write-Log "  Progress: $extractedCount extracted, $skippedCount skipped, $errorCount errors"
            }
        } else {
            Write-Log "  Extraction failed: $($result.error)" "ERROR"
            $errorCount++
        }
    } catch {
        Write-Log "  Python error: $($_.Exception.Message)" "ERROR"
        $errorCount++
    }
}

Write-Log "=== Content Extraction Complete ===" "SUCCESS"
Write-Log "Extracted: $extractedCount files"
Write-Log "Skipped: $skippedCount files (already extracted)"
Write-Log "Errors: $errorCount files"
Write-Log "Output: $OutputRoot"

# Create index file
$indexContent = @"
# SharePoint Content Index

**Last Updated**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Total Files**: $($filesToProcess.Count)
**Extracted**: $extractedCount
**Errors**: $errorCount

---

## Usage

### Search Content
``````bash
# Search all extracted content
grep -r "search term" docs/sharepoint-content/

# Search specific department
grep -r "budget" docs/sharepoint-content/ | grep Marketing

# Case-insensitive search
grep -ri "quarterly report" docs/sharepoint-content/
``````

### Using Claude Code
Ask Claude to search through the content:
- "Find all mentions of 'Q4 goals' in Marketing documents"
- "What are the key points from the 2024 budget presentations?"
- "Summarize all legal documents mentioning contracts"

---

## Files Extracted

"@

foreach ($file in $filesToProcess | Sort-Object department, path) {
    $relativePath = $file.path -replace '\\', '_' -replace ' ', '_'
    $mdFileName = "$($file.department)_$relativePath.md"

    if (Test-Path (Join-Path $OutputRoot $mdFileName)) {
        $indexContent += "- [$($file.name)]($mdFileName) - $($file.department) - $($file.sizeMB) MB`n"
    }
}

$indexPath = Join-Path $OutputRoot "README.md"
$indexContent | Set-Content -Path $indexPath -Encoding UTF8
Write-Log "Created index: $indexPath"

Write-Log "=== Finished ==="

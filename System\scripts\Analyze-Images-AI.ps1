<#
.SYNOPSIS
    Analyze images with AI and update markdown files with descriptions

.DESCRIPTION
    This script:
    1. Finds all image markdown files with pending AI descriptions
    2. Locates the original image files
    3. Uses analyze_image.py with Claude <PERSON> to generate descriptions
    4. Updates the markdown files with AI-generated descriptions
    5. Makes images searchable via the search index

.PARAMETER Force
    Re-analyze images even if they already have descriptions

.PARAMETER MaxImages
    Limit number of images to process (default: all)

.PARAMETER Department
    Only process images from a specific department

.EXAMPLE
    .\Analyze-Images-AI.ps1
    Process all images with pending descriptions

.EXAMPLE
    .\Analyze-Images-AI.ps1 -Department Marketing -MaxImages 5
    Process first 5 Marketing images

.EXAMPLE
    .\Analyze-Images-AI.ps1 -Force
    Re-analyze all images, even those with descriptions

.NOTES
    Requires: ANTHROPIC_API_KEY environment variable
    Uses: analyze_image.py for AI vision analysis
    Cost: ~$0.004 per image (Claude Sonnet 3.5)
#>

[CmdletBinding()]
param(
    [switch]$Force,
    [int]$MaxImages = 0,
    [string]$Department = ""
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$ContentRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$ConfigFile = Join-Path $ProjectRoot "SharePointLinks\config.json"
$AnalyzeScript = Join-Path $PSScriptRoot "analyze_image.py"
$LogFile = Join-Path $PSScriptRoot "analyze-images-log.txt"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== AI Image Analysis Started ==="

# Check for API key
if (-not $env:ANTHROPIC_API_KEY) {
    Write-Log "WARNING: ANTHROPIC_API_KEY not set" "WARN"
    Write-Log "Set it with: `$env:ANTHROPIC_API_KEY = 'your-api-key'" "WARN"
    Write-Log "Proceeding with fallback descriptions..." "WARN"
    Write-Host ""
}

# Check if analyze script exists
if (-not (Test-Path $AnalyzeScript)) {
    Write-Log "ERROR: analyze_image.py not found: $AnalyzeScript" "ERROR"
    exit 1
}

# Check if content directory exists
if (-not (Test-Path $ContentRoot)) {
    Write-Log "ERROR: Content directory not found: $ContentRoot" "ERROR"
    exit 1
}

# Load config for OneDrive path
$config = $null
if (Test-Path $ConfigFile) {
    $config = Get-Content $ConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json
}

# Find all image markdown files
$imageExtensions = @(".jpg", ".jpeg", ".png")
$allMarkdownFiles = Get-ChildItem -Path $ContentRoot -Filter "*.md" -File | Where-Object { $_.Name -ne "README.md" }

Write-Log "Scanning $($allMarkdownFiles.Count) markdown files for images..."

$imagesToProcess = @()

foreach ($mdFile in $allMarkdownFiles) {
    # Check if this is an image file by looking at the filename pattern
    $hasImageExt = $false
    foreach ($ext in $imageExtensions) {
        if ($mdFile.BaseName -like "*$ext") {
            $hasImageExt = $true
            break
        }
    }

    if (-not $hasImageExt) { continue }

    # Read the file to check if it needs analysis
    $content = Get-Content $mdFile.FullName -Raw -Encoding UTF8

    # Check if AI description is pending
    $needsAnalysis = $Force -or ($content -match '\[AI description pending')

    if (-not $needsAnalysis) {
        # Check if description section is empty or has placeholder text
        if ($content -match '## AI-Generated Description\s+\[(Description|AI description)') {
            $needsAnalysis = $true
        }
    }

    if ($needsAnalysis) {
        # Extract the source path from the markdown
        if ($content -match '\*\*Source\*\*:\s*`(.+?)`') {
            $sourcePath = $matches[1]

            # Filter by department if specified
            if ($Department) {
                if ($content -notmatch "\*\*Department\*\*:\s*$Department") {
                    continue
                }
            }

            $imagesToProcess += @{
                MarkdownFile = $mdFile.FullName
                SourcePath = $sourcePath
                FileName = $mdFile.Name
            }
        }
    }
}

Write-Log "Found $($imagesToProcess.Count) images to analyze"

if ($imagesToProcess.Count -eq 0) {
    Write-Log "No images need analysis" "INFO"
    if (-not $Force) {
        Write-Log "Use -Force to re-analyze all images" "INFO"
    }
    exit 0
}

# Limit if MaxImages specified
if ($MaxImages -gt 0 -and $imagesToProcess.Count -gt $MaxImages) {
    Write-Log "Limiting to first $MaxImages images"
    $imagesToProcess = $imagesToProcess | Select-Object -First $MaxImages
}

$processedCount = 0
$errorCount = 0
$skippedCount = 0

foreach ($imageInfo in $imagesToProcess) {
    $imageNum = $processedCount + $errorCount + $skippedCount + 1
    $total = $imagesToProcess.Count

    Write-Log "Processing [$imageNum/$total]: $($imageInfo.FileName)"

    # Build full path to original image
    $imagePath = ""
    if ($config -and $config.oneDriveRoot) {
        $imagePath = Join-Path $config.oneDriveRoot $imageInfo.SourcePath
    }

    # Check if image exists
    if (-not (Test-Path $imagePath)) {
        Write-Log "  ERROR: Image not found: $imagePath" "ERROR"
        $errorCount++
        continue
    }

    # Analyze image with AI
    Write-Log "  Analyzing with Claude API..."
    try {
        $result = python $AnalyzeScript $imagePath | ConvertFrom-Json

        if ($result.success) {
            $description = $result.ai_description
            $hasAPI = $result.has_api

            Write-Log "  Description generated ($($description.Length) chars)"

            # Read current markdown
            $markdown = Get-Content $imageInfo.MarkdownFile -Raw -Encoding UTF8

            # Replace the AI description section
            $newDescription = @"
## AI-Generated Description

$description

**Analysis Date**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Model**: $($result.ai_model)
**API Available**: $hasAPI

---

*Image analyzed and indexed for search*
"@

            # Find and replace the AI description section
            if ($markdown -match '(## AI-Generated Description)([\s\S]+?)(---\s*\*Image)') {
                $markdown = $markdown -replace '(## AI-Generated Description)([\s\S]+?)(---\s*\*Image)', "$newDescription`n`n$($matches[3])"
            } else {
                # If pattern not found, append at the end
                $markdown += "`n`n$newDescription"
            }

            # Save updated markdown
            $markdown | Set-Content -Path $imageInfo.MarkdownFile -Encoding UTF8

            Write-Log "  Updated markdown file" "SUCCESS"
            $processedCount++

        } else {
            Write-Log "  ERROR: Analysis failed: $($result.error)" "ERROR"
            $errorCount++
        }

    } catch {
        Write-Log "  ERROR: Exception: $($_.Exception.Message)" "ERROR"
        $errorCount++
    }

    # Progress update
    if (($processedCount + $errorCount) % 5 -eq 0) {
        Write-Log "  Progress: Analyzed $processedCount, Errors $errorCount"
    }

    # Small delay to avoid rate limiting
    if ($env:ANTHROPIC_API_KEY) {
        Start-Sleep -Milliseconds 500
    }
}

Write-Log "=== AI Image Analysis Complete ==="
Write-Log "Processed: $processedCount images"
Write-Log "Errors: $errorCount images"
Write-Log "Total: $($processedCount + $errorCount) images"
Write-Log ""

if ($processedCount -gt 0) {
    Write-Log "NEXT STEP: Rebuild search index to include new descriptions"
    Write-Log "  Run: .\Index-SharePointContent.ps1 -Rebuild"
    Write-Host ""
    Write-Host "Rebuild index now? (Y/N): " -NoNewline -ForegroundColor Cyan
    $response = Read-Host

    if ($response -eq "Y" -or $response -eq "y") {
        Write-Log "Rebuilding search index..."
        & "$PSScriptRoot\Index-SharePointContent.ps1" -Rebuild
    } else {
        Write-Log "Skipped index rebuild. Run manually when ready."
    }
}

Write-Log "=== Complete ===" "SUCCESS"

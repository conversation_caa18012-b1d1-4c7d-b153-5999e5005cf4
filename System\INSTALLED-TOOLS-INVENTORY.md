# Installed Tools Inventory - ReidCEO System

**Last Updated**: 2025-10-28
**Purpose**: Master list of all installed tools, packages, and dependencies
**Status**: Active - Documents required tooling for ReidCEO system

---

## Overview

This inventory documents **all external tools, runtimes, and packages** required for the ReidCEO management system to function.

**Tool Categories**:
1. **Core Runtimes** - Python, Node.js, PowerShell
2. **Version Control** - Git, GitHub CLI
3. **AI & MCP Tools** - Claude Code, Serena, Confluence MCP
4. **Python Packages** - Data processing, AI, file handling
5. **Node.js Packages** - MCP servers
6. **Windows Tools** - PowerShell modules, OneDrive
7. **Optional Tools** - Cygwin, development utilities

---

## Quick Reference

| Tool | Version | Required | Used For |
|------|---------|----------|----------|
| **Python** | 3.8+ | ✅ Yes | Content extraction, AI embeddings, search |
| **Node.js** | 18+ | ✅ Yes | Serena MCP server |
| **PowerShell** | 5.1+ | ✅ Yes | Automation scripts |
| **Git** | Latest | ✅ Yes | Version control |
| **Claude Code** | Latest | ✅ Yes | AI assistant interface |
| **OneDrive** | Latest | ✅ Yes | SharePoint sync |
| **Cygwin** | Latest | ⚪ Optional | Unix-like environment |
| **GitHub CLI** | Latest | ⚪ Optional | Git operations |

---

## Category 1: Core Runtimes

### Python
**Version Required**: 3.8 or higher
**Status**: ✅ REQUIRED
**Current Version**: (Check with `python --version`)

**What It's Used For**:
- Content extraction from PDF/DOCX/XLSX
- Semantic embeddings generation (AI search)
- Image analysis with Claude Vision API
- Confluence MCP server
- Vector similarity search

**Installation**:
```powershell
# Download from https://www.python.org/downloads/
# Or use winget:
winget install Python.Python.3.12
```

**Critical**: Must be added to PATH during installation

**Verify Installation**:
```powershell
python --version
# Should output: Python 3.x.x
```

**Related Scripts**:
- All Python modules in `System/scripts/`
- Called by PowerShell orchestration scripts

**Documentation**: https://www.python.org/doc/

---

### Node.js
**Version Required**: 18 or higher
**Status**: ✅ REQUIRED (for Serena MCP)
**Current Version**: (Check with `node --version`)

**What It's Used For**:
- Serena MCP server (AI code intelligence)
- NPX package execution
- MCP server runtime

**Installation**:
```powershell
# Download from https://nodejs.org/
# Or use winget:
winget install OpenJS.NodeJS
```

**Includes**: npm (Node Package Manager) and npx

**Verify Installation**:
```powershell
node --version
npm --version
npx --version
```

**Related Tools**:
- Serena MCP server
- MCP Atlassian (Confluence)

**Documentation**: https://nodejs.org/docs/

---

### PowerShell
**Version Required**: 5.1+ (Windows) or 7+ (cross-platform)
**Status**: ✅ REQUIRED
**Current Version**: (Check with `$PSVersionTable.PSVersion`)

**What It's Used For**:
- All automation scripts (32 scripts)
- SharePoint sync orchestration
- Search systems
- Setup wizards

**Installation**:
- Pre-installed on Windows 10/11
- PowerShell Core 7+: https://github.com/PowerShell/PowerShell

**Verify Installation**:
```powershell
$PSVersionTable.PSVersion
# Should output: 5.1.x or higher
```

**Related Scripts**: All `.ps1` files in `System/scripts/`

**Documentation**: https://docs.microsoft.com/powershell/

---

## Category 2: Version Control Tools

### Git
**Version Required**: Latest stable
**Status**: ✅ REQUIRED
**Current Version**: (Check with `git --version`)

**What It's Used For**:
- Version control for ReidCEO repository
- Tracking changes to CEO management system
- Collaboration and backup

**Installation**:
```powershell
# Download from https://git-scm.com/
# Or use winget:
winget install Git.Git
```

**Configuration**:
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

**Verify Installation**:
```bash
git --version
# Should output: git version 2.x.x
```

**Used By**: All development work, Claude Code integration

**Documentation**: https://git-scm.com/doc

---

### GitHub CLI (gh)
**Version Required**: Latest
**Status**: ⚪ OPTIONAL (nice to have)
**Current Version**: (Check with `gh --version`)

**What It's Used For**:
- Command-line GitHub operations
- Creating issues, PRs
- Repository management

**Installation**:
```powershell
winget install GitHub.cli
```

**Verify Installation**:
```bash
gh --version
```

**Authentication**:
```bash
gh auth login
```

**Used By**: Advanced Git workflows

**Documentation**: https://cli.github.com/manual/

---

## Category 3: AI & MCP Tools

### Claude Code (Claude Desktop with MCP)
**Version Required**: Latest
**Status**: ✅ REQUIRED
**Current Version**: Check application

**What It's Used For**:
- Primary AI assistant interface
- Serena MCP integration
- Confluence MCP integration
- Business context assistance
- Code navigation and editing

**Installation**:
- Download from Anthropic
- Or install via provided installer

**Configuration Files**:
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

**MCP Servers Configured**:
- Serena (code intelligence)
- Confluence (knowledge base)

**Related Scripts**:
- `Setup-ConfluenceMCP.ps1`
- `Setup-Serena-QuickStart.ps1`

**Documentation**: https://claude.ai/docs

---

### Serena MCP Server
**Version Required**: Latest
**Status**: ✅ REQUIRED (for code intelligence)
**Package**: `@serenaai/mcp-server`

**What It's Used For**:
- Symbol-based code navigation
- Finding functions, classes, markdown headers
- Reference finding
- Intelligent code editing
- Repository structure analysis

**Installation**:
```powershell
# Installed via script:
.\System\scripts\Setup-Serena-QuickStart.ps1

# Or manually:
npx @serenaai/mcp-server
```

**Dependencies**:
- Node.js 18+
- Claude Code

**Configuration**: Automatic via setup script

**Cache Location**: `.serena/cache/` (gitignored)

**Memory Location**: `.serena/memories/` (committed)

**Related Documentation**: SERENA-AI-ASSISTANT-GUIDE.md

**Project**: https://github.com/serenaai/serena

---

### Confluence MCP Server
**Version Required**: Latest
**Status**: ✅ REQUIRED (for Confluence access)
**Package**: `mcp-atlassian`

**What It's Used For**:
- Access Gain Servicing Confluence
- Search knowledge base
- Read pages and spaces
- Integration with Claude Code

**Installation**:
```powershell
# Installed via script:
.\System\scripts\Setup-ConfluenceMCP.ps1

# Or manually:
pip install mcp-atlassian pydantic==2.11.0
```

**Dependencies**:
- Python 3.8+
- API credentials (team-shared)

**Configuration Files**:
- `System/scripts/confluence-credentials.json` (gitignored)
- `System/scripts/confluence-spaces-config.json`

**Related Documentation**: CONFLUENCE-MCP-SETUP-GUIDE.md

---

## Category 4: Python Packages

### Document Processing

#### pypdf
**Version**: Latest
**Purpose**: Extract text from PDF files
**Used By**: `extract_content.py`

**Installation**:
```bash
pip install pypdf
```

---

#### python-docx
**Version**: Latest
**Purpose**: Read and convert DOCX files to markdown
**Used By**: `extract_content.py`

**Installation**:
```bash
pip install python-docx
```

---

#### openpyxl
**Version**: Latest
**Purpose**: Read Excel files (XLSX) and extract data
**Used By**: `extract_content.py`

**Installation**:
```bash
pip install openpyxl
```

---

#### markdown
**Version**: Latest
**Purpose**: Markdown text processing and conversion
**Used By**: Content extraction pipeline

**Installation**:
```bash
pip install markdown
```

---

### AI & Machine Learning

#### sentence-transformers
**Version**: Latest
**Purpose**: Generate semantic embeddings for similarity search
**Used By**: `generate_embeddings.py`, `semantic_search.py`

**Installation**:
```bash
pip install sentence-transformers
```

**Model Used**: `all-MiniLM-L6-v2` (22MB, downloaded first time)

**What It Does**:
- Converts text to 384-dimensional vectors
- Enables "find similar concepts" search
- Runs locally (no API costs)

**Dependencies**: torch, numpy

---

#### torch
**Version**: Latest (CPU version)
**Purpose**: PyTorch - required for sentence-transformers
**Used By**: Semantic embeddings generation

**Installation**:
```bash
# Installed automatically with sentence-transformers
# Or manually:
pip install torch
```

**Note**: CPU version is sufficient (GPU not needed)

---

#### numpy
**Version**: Latest
**Purpose**: Numerical computing and array operations
**Used By**: Embeddings generation and similarity calculations

**Installation**:
```bash
pip install numpy
```

---

#### scipy
**Version**: Latest
**Purpose**: Scientific computing (cosine similarity calculations)
**Used By**: `semantic_search.py`

**Installation**:
```bash
pip install scipy
```

---

#### anthropic
**Version**: Latest
**Purpose**: Claude API client for image analysis
**Used By**: `analyze_image.py`

**Installation**:
```bash
pip install anthropic
```

**Requires**: Anthropic API key
**Cost**: ~$0.001 per image analyzed

---

#### Pillow (PIL)
**Version**: Latest
**Purpose**: Image processing and metadata extraction
**Used By**: `extract_image_metadata.py`, `analyze_image.py`

**Installation**:
```bash
pip install Pillow
```

---

### MCP & Integration

#### mcp-atlassian
**Version**: Latest
**Purpose**: Confluence MCP server implementation
**Used By**: Confluence integration

**Installation**:
```bash
pip install mcp-atlassian
```

**Dependencies**: pydantic

---

#### pydantic
**Version**: 2.11.0 (specific version required)
**Purpose**: Data validation for MCP servers
**Used By**: `mcp-atlassian`

**Installation**:
```bash
pip install pydantic==2.11.0
```

**Note**: Version 2.11.0 specifically required for compatibility

---

### Quick Install: All Python Packages

```bash
# Document processing
pip install pypdf python-docx openpyxl markdown

# AI & ML
pip install sentence-transformers torch numpy scipy

# Image processing
pip install Pillow anthropic

# MCP integration
pip install mcp-atlassian pydantic==2.11.0
```

---

## Category 5: Node.js Packages

### Serena Dependencies
**Installed via**: npm/npx (automatic)

**Packages** (managed automatically):
- `@serenaai/mcp-server` - Main Serena server
- Dependencies handled by npm

**Installation**:
```bash
# Serena installs its own dependencies
npx @serenaai/mcp-server
```

**No manual package management needed** - npx handles it

---

## Category 6: Windows Tools

### OneDrive
**Version**: Latest (Windows built-in)
**Status**: ✅ REQUIRED
**What It's Used For**:
- SharePoint folder synchronization
- Local access to SharePoint content
- Automatic sync of company documents

**Setup**:
1. Sign in with Gain Servicing account
2. Enable sync for SharePoint sites
3. Verify folder: `C:\Users\<USER>\OneDrive - Gain Servicing\`

**Used By**:
- All SharePoint sync scripts
- Content extraction pipeline

**Configuration**: OneDrive system tray icon

**Documentation**: https://support.microsoft.com/onedrive

---

### Windows Script Host (WScript)
**Version**: Built-in to Windows
**Status**: ✅ REQUIRED
**What It's Used For**:
- Creating .lnk shortcut files
- COM object access in PowerShell

**Used By**:
- `Create-SharePointShortcuts.ps1`

**Verify**: Pre-installed on all Windows systems

---

## Category 7: Optional Tools

### Cygwin
**Version**: Latest
**Status**: ⚪ OPTIONAL (advanced use only)
**What It's Used For**:
- Unix-like environment on Windows
- Cross-platform script testing
- Advanced development workflows

**Installation**:
```powershell
# Via script:
.\System\scripts\Install-Cygwin.ps1

# Or download from:
https://www.cygwin.com/
```

**Used By**: Advanced users only

**Related Scripts**:
- `Install-Cygwin.ps1`
- `Install-CygpathShim.ps1`
- `cygpath` binary

**When to Install**: Only if you need Unix commands on Windows

---

## Installation Quick Start

### Minimum Required Setup (30 minutes)

**Step 1: Install Core Runtimes**
```powershell
# Python 3.8+
winget install Python.Python.3.12

# Node.js 18+
winget install OpenJS.NodeJS

# Git (if not installed)
winget install Git.Git

# Verify installations
python --version
node --version
git --version
```

**Step 2: Install Python Packages**
```bash
pip install pypdf python-docx openpyxl markdown
pip install sentence-transformers torch numpy scipy
pip install Pillow anthropic
pip install mcp-atlassian pydantic==2.11.0
```

**Step 3: Configure OneDrive**
- Sign in with Gain Servicing account
- Sync SharePoint folders

**Step 4: Install MCP Servers**
```powershell
cd System/scripts

# Serena
.\Setup-Serena-QuickStart.ps1

# Confluence
.\Setup-ConfluenceMCP.ps1
```

**Step 5: Initial Sync**
```powershell
# Full SharePoint sync
.\Sync-All.ps1
```

**Done!** All tools installed and configured.

---

## Verification Checklist

### Core Tools ✅
- [ ] Python 3.8+ installed and in PATH
- [ ] Node.js 18+ installed
- [ ] PowerShell 5.1+ available
- [ ] Git installed and configured

### AI Tools ✅
- [ ] Claude Code installed
- [ ] Serena MCP configured
- [ ] Confluence MCP configured
- [ ] MCP servers show in Claude Code

### Python Packages ✅
- [ ] Document processing packages installed
- [ ] AI/ML packages installed (sentence-transformers, torch)
- [ ] Image processing packages installed (Pillow, anthropic)
- [ ] MCP packages installed (mcp-atlassian, pydantic)

### Windows Integration ✅
- [ ] OneDrive syncing SharePoint
- [ ] SharePoint folders accessible
- [ ] Scripts can find OneDrive path

### Optional ⚪
- [ ] GitHub CLI installed (optional)
- [ ] Cygwin installed (optional)

**Verify All**:
```powershell
# Run this verification script:
python --version
node --version
git --version
pip list | Select-String "pypdf|python-docx|sentence-transformers|anthropic|mcp-atlassian"
Test-Path "C:\Users\<USER>\OneDrive - Gain Servicing"
```

---

## Tool Dependencies Map

### SharePoint Sync System
```
OneDrive (Windows)
  ↓
PowerShell Scripts (Sync-*.ps1)
  ↓
Python Scripts (extract_content.py, etc.)
  ↓
Python Packages (pypdf, python-docx, openpyxl)
```

### Semantic Search System
```
Python 3.8+
  ↓
sentence-transformers + torch + numpy
  ↓
generate_embeddings.py
  ↓
PowerShell Scripts (Generate-SemanticIndex.ps1)
```

### Image Analysis System
```
Anthropic API Key
  ↓
Python (anthropic + Pillow)
  ↓
analyze_image.py
  ↓
PowerShell Scripts (Analyze-Images-AI.ps1)
```

### AI Assistant System
```
Claude Code
  ↓
Node.js 18+ (Serena MCP)
Python 3.8+ (Confluence MCP)
  ↓
MCP Servers (Serena, Confluence)
  ↓
Business Context Integration
```

---

## Maintenance & Updates

### Weekly Checks
```powershell
# Update Python packages
pip list --outdated
pip install --upgrade [package-name]

# Update npm packages (Serena)
npm outdated -g
npx @serenaai/mcp-server # Always uses latest
```

### Monthly Updates
- Update Claude Code (check for app updates)
- Update Git (winget upgrade Git.Git)
- Update Python (winget upgrade Python.Python.3.12)
- Update Node.js (winget upgrade OpenJS.NodeJS)

### As Needed
- Reinstall Serena MCP if issues: `.\Setup-Serena-QuickStart.ps1`
- Update Confluence credentials if changed
- Regenerate semantic embeddings if model updates

---

## Troubleshooting

### "Python not found"
```powershell
# Check installation
python --version

# If not found, add to PATH:
# System Properties → Environment Variables → Path
# Add: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\
```

### "pip not found"
```bash
# Reinstall Python with pip checkbox enabled
# Or manually install pip:
python -m ensurepip --upgrade
```

### "Node.js not found"
```powershell
# Check installation
node --version

# If not found, reinstall Node.js
winget install OpenJS.NodeJS
```

### "Package installation fails"
```bash
# Update pip first
python -m pip install --upgrade pip

# Then try package again
pip install [package-name]
```

### "Serena MCP not appearing"
```powershell
# Restart Claude Code
# Re-run setup:
.\System\scripts\Setup-Serena-QuickStart.ps1

# Check Claude Code MCP settings
```

### "OneDrive not syncing"
- Check OneDrive system tray icon
- Verify signed in to Gain Servicing account
- Check available disk space
- Restart OneDrive

---

## Cost Analysis

### One-Time Costs
- **Python/Node.js/Git**: $0 (free, open source)
- **Claude Code**: Included with Claude subscription
- **OneDrive**: Included with Microsoft 365 (Gain Servicing)
- **Total One-Time**: $0

### Ongoing Costs
- **Semantic search model download**: $0 (one-time 500MB download)
- **Local processing**: $0 (runs on local machine)
- **Image analysis**: ~$0.001 per image (optional, Claude Vision API)
- **Total Monthly**: ~$0-5 depending on image analysis usage

### Storage Requirements
- **Python**: ~200MB
- **Node.js**: ~50MB
- **Python packages**: ~1.5GB (includes torch + sentence-transformers model)
- **Git**: ~50MB
- **Serena cache**: ~100MB
- **SharePoint content**: Variable (depends on documents)
- **Total Disk Space**: ~2-3GB minimum

---

## Version History

**Version 1.0** (2025-10-28)
- Initial comprehensive inventory
- Core runtimes, AI tools, packages documented
- Dependencies mapped
- Installation and verification procedures

---

## Related Documentation

- **SCRIPTS-AND-TOOLS-REGISTRY.md** - All helper scripts inventory
- **System/scripts/README.md** - Quick reference for scripts
- **SERENA-AI-ASSISTANT-GUIDE.md** - Serena setup details
- **CONFLUENCE-MCP-SETUP-GUIDE.md** - Confluence setup details
- **SEARCH-SYSTEMS-GUIDE.md** - Search capabilities overview

---

*This inventory is the single source of truth for all installed tools and dependencies in the ReidCEO system. Keep it updated when adding new tools or requirements.*

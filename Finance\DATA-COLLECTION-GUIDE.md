# Finance Data Collection Guide

**Purpose:** Guide for providing financial data to enable AI-powered KPI tracking and analysis  
**Date Created:** 2025-10-10  
**For:** CEO and CFO

---

## 🎯 Overview

This guide explains **what financial data to provide** and **how to organize it** so I can:
- Generate accurate KPIs and dashboards
- Identify underperforming accounts automatically
- Create trend analysis and forecasts
- Provide actionable insights and alerts
- Build your daily/weekly financial briefings

---

## 📋 Phase 1: Essential Data (Start Here)

### 1. **Current Financial Statements**

**What to provide:**
- Latest Profit & Loss Statement (P&L)
- Latest Balance Sheet
- Latest Cash Flow Statement
- Year-to-date financials

**How to provide:**
- Save as: `Finance/financial-statements/monthly-financials/2025-10-PL.xlsx` (or PDF)
- Save as: `Finance/financial-statements/monthly-financials/2025-10-BalanceSheet.xlsx`
- Save as: `Finance/financial-statements/monthly-financials/2025-10-CashFlow.xlsx`

**What I need to see:**
- Revenue by line item/category
- Cost of goods sold (COGS)
- Operating expenses by category
- Net income
- Cash position
- Accounts receivable
- Accounts payable
- Key balance sheet items

---

### 2. **Budget/Targets for 2025**

**What to provide:**
- Annual budget/plan for 2025
- Monthly revenue targets
- Monthly expense budgets
- Key financial goals

**How to provide:**
- Save as: `Finance/projections/2025-annual-budget.xlsx`
- Include monthly breakdown if available

**What I need to see:**
- Revenue targets by month
- Expense budgets by category
- Profitability targets
- Cash flow projections
- Any key assumptions

---

### 3. **Account List with Performance Data**

**What to provide:**
- List of all active client accounts
- Revenue by account (actual)
- Anticipated/projected revenue by account
- Account start dates

**How to provide:**
- Save as: `Finance/account-performance/account-master-list.xlsx`

**Required columns:**
| Column Name | Description | Example |
|-------------|-------------|---------|
| Account_ID | Unique identifier | A-1001 |
| Account_Name | Client name | ABC Law Firm |
| Start_Date | Onboarding date | 2024-03-15 |
| Industry | Industry category | Law Firm |
| Anticipated_Annual_Revenue | Projected annual revenue | $250,000 |
| Anticipated_Monthly_Revenue | Projected monthly revenue | $20,833 |
| Actual_YTD_Revenue | Actual revenue year-to-date | $180,000 |
| Actual_MTD_Revenue | Actual revenue month-to-date | $18,500 |
| Account_Manager | Who manages this account | John Smith |
| Status | Active/At Risk/Churned | Active |

**Optional but helpful columns:**
- Number of cases/transactions
- Average case value
- Collection rate
- Cost to service
- Contract end date
- Notes/comments

---

## 📋 Phase 2: Historical Data (For Trends)

### 4. **Historical Financial Statements**

**What to provide:**
- Monthly P&L statements for past 12 months
- Same for Balance Sheet and Cash Flow

**How to provide:**
- Save in: `Finance/financial-statements/monthly-financials/`
- Naming: `YYYY-MM-PL.xlsx`, `YYYY-MM-BalanceSheet.xlsx`, etc.
- Example: `2024-10-PL.xlsx`, `2024-11-PL.xlsx`, etc.

**Why this matters:**
- Enables trend analysis
- Identifies seasonality
- Calculates growth rates
- Benchmarks performance

---

### 5. **Historical Account Performance**

**What to provide:**
- Monthly revenue by account for past 12 months (if available)

**How to provide:**
- Save as: `Finance/account-performance/historical-account-revenue.xlsx`

**Format:**
| Account_ID | Account_Name | 2024-10 | 2024-11 | 2024-12 | 2025-01 | ... |
|------------|--------------|---------|---------|---------|---------|-----|
| A-1001 | ABC Law Firm | $18,000 | $19,500 | $21,000 | $20,500 | ... |

---

## 📋 Phase 3: Detailed Analysis Data (For Deep Insights)

### 6. **Revenue Breakdown**

**What to provide:**
- Revenue by service type/product line
- Revenue by industry/client segment
- Revenue by geography (if applicable)

**How to provide:**
- Save as: `Finance/revenue-analysis/revenue-breakdown.xlsx`

**Example structure:**
```
By Service Type:
- Debt Collection: $X
- Legal Services: $Y
- Consulting: $Z

By Industry:
- Law Firms: $X
- Healthcare: $Y
- Financial Services: $Z
```

---

### 7. **Cost Breakdown**

**What to provide:**
- Operating expenses by department
- Cost per account (if available)
- Variable vs. fixed costs

**How to provide:**
- Save as: `Finance/cost-analysis/cost-breakdown.xlsx`

**Example structure:**
```
By Department:
- Sales & Marketing: $X
- Operations: $Y
- Technology: $Z
- G&A: $W

By Type:
- Personnel: $X
- Technology: $Y
- Facilities: $Z
- Other: $W
```

---

### 8. **Key Metrics History**

**What to provide:**
- Historical KPIs if you track them

**How to provide:**
- Save as: `Finance/kpi-dashboard/historical-kpis.xlsx`

**Useful metrics:**
- Gross margin % by month
- Operating margin % by month
- Customer acquisition cost
- Customer lifetime value
- Churn rate
- Days sales outstanding (DSO)
- Any other metrics you currently track

---

## 📋 Phase 4: Forward-Looking Data

### 9. **Projections and Forecasts**

**What to provide:**
- Revenue forecast for next 12 months
- Expense forecast
- Cash flow projections
- Scenario analysis (best/base/worst case)

**How to provide:**
- Save as: `Finance/projections/2025-2026-forecast.xlsx`

**Include:**
- Monthly projections
- Key assumptions
- Growth rates assumed
- New account pipeline impact
- Known changes (new hires, contracts, etc.)

---

### 10. **Pipeline/New Business**

**What to provide:**
- New accounts expected to close
- Expected revenue from new accounts
- Timing of new revenue

**How to provide:**
- Save as: `Finance/projections/new-account-pipeline.xlsx`

**Format:**
| Prospect_Name | Expected_Close_Date | Probability | Expected_Annual_Revenue | Expected_Start_Date |
|---------------|---------------------|-------------|-------------------------|---------------------|
| XYZ Corp | 2025-11-15 | 75% | $300,000 | 2025-12-01 |

---

## 🎯 Data Quality Guidelines

### For Best Results:

✅ **Use consistent formats**
- Dates: YYYY-MM-DD
- Currency: Numbers only (no $ signs in cells)
- Percentages: As decimals (0.15 for 15%) or with % symbol

✅ **Be complete**
- Fill in all columns
- Use "N/A" or "0" rather than leaving blank
- Include notes for unusual items

✅ **Be consistent**
- Use same account names/IDs across all files
- Use same category names
- Use same time periods

✅ **Update regularly**
- Monthly financials: Upload by 5th business day of month
- Account performance: Update weekly
- Projections: Update quarterly or when material changes

---

## 📊 What I'll Generate For You

Once you provide this data, I can automatically create:

### Daily:
- Cash position and alerts
- Revenue vs. target (MTD, YTD)
- Critical account alerts
- Top priorities

### Weekly:
- KPI scorecard with all metrics
- Underperforming accounts list with analysis
- Trend analysis
- Variance explanations
- Recommendations

### Monthly:
- Comprehensive financial analysis
- Budget vs. actual with commentary
- Account performance deep dive
- Forecast updates
- Executive summary for board

### Ad-Hoc:
- Answer specific questions about performance
- Scenario analysis
- What-if modeling
- Custom reports

---

## 📁 Recommended File Organization

```
Finance/
├── financial-statements/
│   └── monthly-financials/
│       ├── 2024-10-PL.xlsx
│       ├── 2024-10-BalanceSheet.xlsx
│       ├── 2024-10-CashFlow.xlsx
│       ├── 2024-11-PL.xlsx
│       └── ... (one set per month)
│
├── account-performance/
│   ├── account-master-list.xlsx (UPDATED WEEKLY)
│   └── historical-account-revenue.xlsx
│
├── revenue-analysis/
│   └── revenue-breakdown.xlsx
│
├── cost-analysis/
│   └── cost-breakdown.xlsx
│
├── projections/
│   ├── 2025-annual-budget.xlsx
│   ├── 2025-2026-forecast.xlsx
│   └── new-account-pipeline.xlsx
│
└── kpi-dashboard/
    └── historical-kpis.xlsx (if available)
```

---

## 🚀 Getting Started - Action Plan

### Week 1: Essential Data
- [ ] Upload latest P&L, Balance Sheet, Cash Flow
- [ ] Upload 2025 budget/targets
- [ ] Create and upload account master list with performance data

### Week 2: Historical Context
- [ ] Upload past 12 months of financial statements
- [ ] Upload historical account revenue (if available)

### Week 3: Detailed Analysis
- [ ] Upload revenue breakdown by service/industry
- [ ] Upload cost breakdown by department
- [ ] Upload any historical KPIs you track

### Week 4: Forward Looking
- [ ] Upload revenue/expense forecasts
- [ ] Upload new account pipeline
- [ ] Upload scenario analysis

---

## 💡 Pro Tips

### 1. Start Simple
Don't wait to have everything perfect. Start with:
- Latest financials
- Budget
- Account list
Then add more over time.

### 2. Automate Where Possible
If you have accounting software:
- Export reports monthly
- Use consistent export formats
- Save to the same location

### 3. One Source of Truth
- Designate one person (CFO or controller) to maintain files
- Update on a schedule
- Version control (date in filename)

### 4. Protect Sensitive Data
- This is your private GitHub repo
- Still, be mindful of what you upload
- Consider using .gitignore for highly sensitive files

---

## 📞 Questions?

As you gather this data, if you have questions about:
- What format to use
- How to structure something
- What's most important
- How to handle edge cases

Just ask! I'll help you organize it optimally.

---

## ✅ Ready to Start?

**Next Steps:**
1. Gather the Phase 1 data (latest financials, budget, account list)
2. Save files to the Finance folder as described
3. Let me know when files are uploaded
4. I'll analyze and create your first KPI dashboard!

---

**Let's transform your financial visibility and decision-making!** 🚀


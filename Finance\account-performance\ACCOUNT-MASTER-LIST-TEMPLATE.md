# Account Master List Template

**Purpose:** Template for tracking all client accounts and their performance  
**Update Frequency:** Weekly (or more often)  
**Owner:** CFO

---

## 📋 How to Use This Template

1. **Create a spreadsheet** (Excel, Google Sheets, or CSV)
2. **Use the column structure below**
3. **Fill in one row per account**
4. **Save as:** `Finance/account-performance/account-master-list.xlsx`
5. **Update weekly** with latest revenue data

---

## 📊 Required Columns

### **Column Structure:**

| Column Name | Data Type | Description | Example |
|-------------|-----------|-------------|---------|
| Account_ID | Text | Unique identifier for account | A-1001 |
| Account_Name | Text | Client/company name | ABC Law Firm |
| Start_Date | Date | When account started | 2024-03-15 |
| Industry | Text | Industry category | Law Firm |
| Account_Manager | Text | Who manages this account | <PERSON> |
| Status | Text | Active, At Risk, Churned, Paused | Active |
| Anticipated_Annual_Revenue | Number | Projected annual revenue | 250000 |
| Anticipated_Monthly_Revenue | Number | Projected monthly revenue | 20833 |
| Actual_YTD_Revenue | Number | Actual revenue year-to-date | 180000 |
| Actual_MTD_Revenue | Number | Actual revenue month-to-date | 18500 |
| Last_Month_Revenue | Number | Previous month's revenue | 19200 |
| Performance_Status | Text | Auto-calculate or manual | Meeting Expectations |

---

## 📊 Optional But Helpful Columns

| Column Name | Data Type | Description | Example |
|-------------|-----------|-------------|---------|
| Number_of_Cases_MTD | Number | Cases/transactions this month | 45 |
| Number_of_Cases_YTD | Number | Cases/transactions year-to-date | 520 |
| Average_Case_Value | Number | Average revenue per case | 411 |
| Collection_Rate | Percentage | % of attempted collections successful | 0.68 |
| Cost_to_Service | Number | Monthly cost to service account | 5000 |
| Gross_Margin | Percentage | Revenue minus cost to service | 0.75 |
| Contract_End_Date | Date | When contract expires | 2026-03-15 |
| Contract_Value | Number | Total contract value | 750000 |
| Payment_Terms | Text | Payment terms | Net 30 |
| Last_Contact_Date | Date | Last CEO/exec contact | 2025-09-15 |
| Satisfaction_Score | Number | Client satisfaction (1-10) | 8 |
| Churn_Risk | Text | Low, Medium, High | Low |
| Expansion_Opportunity | Text | Yes, No, Maybe | Yes |
| Notes | Text | Any relevant notes | Considering expansion |

---

## 📊 Calculated Fields (I Can Generate)

Once you provide the data above, I can automatically calculate:

- **Variance %:** (Actual - Anticipated) / Anticipated
- **Performance Category:** Exceeding, Meeting, Below, Significantly Below
- **Trend:** Improving, Stable, Declining
- **Alert Status:** Red, Yellow, Green
- **Months Active:** Time since start date
- **Revenue Run Rate:** Annualized based on recent performance
- **Lifetime Value:** Total revenue to date
- **Monthly Average:** Average monthly revenue

---

## 📋 Example Data (First Few Rows)

```csv
Account_ID,Account_Name,Start_Date,Industry,Account_Manager,Status,Anticipated_Annual_Revenue,Anticipated_Monthly_Revenue,Actual_YTD_Revenue,Actual_MTD_Revenue,Last_Month_Revenue
A-1001,ABC Law Firm,2024-03-15,Law Firm,John Smith,Active,250000,20833,180000,18500,19200
A-1002,XYZ Healthcare,2023-06-01,Healthcare,Jane Doe,Active,500000,41667,420000,38000,42500
A-1003,123 Financial,2024-01-10,Financial Services,Bob Johnson,Active,180000,15000,135000,12000,13500
A-1004,DEF Corporation,2023-11-20,Corporate,Sarah Williams,At Risk,300000,25000,200000,15000,18000
A-1005,GHI Medical Group,2024-08-01,Healthcare,John Smith,Active,150000,12500,25000,11000,12000
```

---

## 🎯 Performance Status Guidelines

Use these guidelines to categorize accounts:

### **🟢 Exceeding Expectations**
- Actual revenue >110% of anticipated
- Consistently performing above projections
- Status: "Exceeding"

### **🟡 Meeting Expectations**
- Actual revenue 90-110% of anticipated
- Performing as expected
- Status: "Meeting"

### **🟠 Below Expectations**
- Actual revenue 75-90% of anticipated
- Underperforming but not critical
- Status: "Below"

### **🔴 Significantly Below Expectations**
- Actual revenue <75% of anticipated
- Requires immediate attention
- Status: "Significantly Below"

---

## 📊 Status Field Guidelines

### **Active**
- Account is currently generating revenue
- Services being provided
- Relationship is healthy

### **At Risk**
- Account showing signs of potential churn
- Performance issues
- Client dissatisfaction
- Payment issues

### **Churned**
- Account has left/terminated
- No longer generating revenue
- Keep in list for historical analysis

### **Paused**
- Temporarily not active
- Expected to resume
- Seasonal or project-based

### **Onboarding**
- New account in setup phase
- Not yet generating full revenue
- Still ramping up

---

## 🔄 Update Process

### **Weekly Updates (Minimum):**
1. Update Actual_MTD_Revenue for all accounts
2. Update Actual_YTD_Revenue for all accounts
3. Update Status if any changes
4. Add any new accounts
5. Update Notes for any significant changes

### **Monthly Updates:**
1. Move MTD to Last_Month_Revenue
2. Reset MTD to 0 (or current month's revenue)
3. Update YTD
4. Review and update Performance_Status
5. Update any optional fields

### **Quarterly Updates:**
1. Review Anticipated_Annual_Revenue (adjust if needed)
2. Update Contract_End_Date if renewed
3. Review Churn_Risk and Expansion_Opportunity
4. Update Satisfaction_Score if you have new data

---

## 💡 Tips for Success

### **1. Start Simple**
- Begin with just the required columns
- Add optional columns over time
- Don't wait for perfection

### **2. Be Consistent**
- Use same account names every time
- Use same date format (YYYY-MM-DD)
- Use same industry categories

### **3. Keep It Current**
- Update at least weekly
- More frequent is better
- Set a recurring calendar reminder

### **4. Use Formulas**
- In Excel/Sheets, use formulas for calculations
- Example: `=Actual_YTD_Revenue/Anticipated_Annual_Revenue` for % of target
- I can help with formulas if needed

### **5. Validate Data**
- Check for typos
- Verify calculations
- Ensure dates are correct
- Look for outliers

---

## 📁 File Formats Accepted

I can work with any of these formats:

✅ **Excel (.xlsx)** - Preferred  
✅ **CSV (.csv)** - Good for large datasets  
✅ **Google Sheets** - Export as Excel or CSV  
✅ **Tab-delimited text (.txt)**  

---

## 🚀 Getting Started

### **Step 1: Create Your Spreadsheet**
- Open Excel, Google Sheets, or similar
- Create column headers from "Required Columns" above
- Add optional columns if you have the data

### **Step 2: Populate Data**
- Add one row per account
- Fill in all required fields
- Add optional fields where available

### **Step 3: Save and Upload**
- Save as: `account-master-list.xlsx`
- Place in: `Finance/account-performance/`
- Commit to GitHub

### **Step 4: Let Me Know**
- Tell me you've uploaded the file
- I'll analyze and create your first dashboard
- We'll iterate and improve

---

## 📞 Questions?

**Common Questions:**

**Q: What if I don't have anticipated revenue for all accounts?**  
A: Use your best estimate based on contract value, historical performance, or industry benchmarks. We can refine over time.

**Q: What if an account just started and has no YTD revenue?**  
A: That's fine! Put 0 for now. We'll track as it ramps up.

**Q: What if I have hundreds of accounts?**  
A: Perfect! The more data, the better the analysis. Just make sure the format is consistent.

**Q: Can I add custom columns?**  
A: Absolutely! Add any columns that are relevant to your business. Just let me know what they mean.

**Q: How do I handle accounts that vary month-to-month?**  
A: Use the anticipated monthly average. We'll track variance and identify patterns.

---

## ✅ Checklist Before Uploading

- [ ] All required columns included
- [ ] At least one row per active account
- [ ] Dates in YYYY-MM-DD format
- [ ] Numbers are numbers (no $ or commas in cells)
- [ ] Account names are consistent
- [ ] File saved as .xlsx or .csv
- [ ] File named: `account-master-list.xlsx`
- [ ] File placed in: `Finance/account-performance/`

---

## 🎯 What Happens Next

Once you upload this file, I will:

1. ✅ Analyze all accounts
2. ✅ Calculate performance metrics
3. ✅ Identify underperforming accounts
4. ✅ Generate variance analysis
5. ✅ Create alerts for accounts needing attention
6. ✅ Build your first KPI dashboard
7. ✅ Provide insights and recommendations

---

**Ready to create your account master list? Let's do this!** 🚀

**Need help? Just ask - I can provide examples, formulas, or guidance!**


<#
.SYNOPSIS
    Lightning-fast search of SharePoint content using pre-built index

.DESCRIPTION
    Searches the full-text index created by Index-SharePointContent.ps1
    Returns results in < 1 second across all 284 files

.PARAMETER Query
    Search term or phrase (supports multi-word queries)

.PARAMETER Department
    Filter results by department

.PARAMETER Top
    Number of results to return (default: 10)

.PARAMETER ShowContext
    Show text context around matches

.EXAMPLE
    .\Search-SharePointContent.ps1 -Query "customer experience"
    Finds documents mentioning customer experience

.EXAMPLE
    .\Search-SharePointContent.ps1 -Query "Q4 goals" -Department Marketing -Top 5
    Top 5 Marketing documents about Q4 goals

.EXAMPLE
    .\Search-SharePointContent.ps1 -Query "budget" -ShowContext
    Finds budget mentions with surrounding text

.NOTES
    Requires: Index created by Index-SharePointContent.ps1
    Speed: Sub-second search across all files
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$Query,

    [string]$Department = "",

    [int]$Top = 10,

    [switch]$ShowContext
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$ContentRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$IndexFile = Join-Path $ContentRoot "search-index.json"

# Check if index exists
if (-not (Test-Path $IndexFile)) {
    Write-Host "ERROR: Search index not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Create index first:" -ForegroundColor Yellow
    Write-Host "  cd System\scripts" -ForegroundColor Gray
    Write-Host "  .\Index-SharePointContent.ps1" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

Write-Host "Loading search index..." -ForegroundColor Cyan
$startTime = Get-Date

# Load index
$index = Get-Content $IndexFile -Raw -Encoding UTF8 | ConvertFrom-Json

$loadTime = ((Get-Date) - $startTime).TotalMilliseconds
Write-Host "Index loaded in $([math]::Round($loadTime, 0))ms" -ForegroundColor Gray
Write-Host ""

# Parse query into words
$queryWords = $Query.ToLower() -split '\W+' | Where-Object { $_.Length -gt 2 }

if ($queryWords.Count -eq 0) {
    Write-Host "ERROR: Query too short or no valid words" -ForegroundColor Red
    exit 1
}

Write-Host "Searching for: " -NoNewline -ForegroundColor Cyan
Write-Host "$($queryWords -join ', ')" -ForegroundColor Yellow
Write-Host ""

# Search index for each word
$searchStartTime = Get-Date
$fileScores = @{}

foreach ($word in $queryWords) {
    if ($index.wordIndex.PSObject.Properties.Name -contains $word) {
        $filesWithWord = $index.wordIndex.$word

        foreach ($fileKey in $filesWithWord.PSObject.Properties.Name) {
            $frequency = $filesWithWord.$fileKey

            if ($fileScores.ContainsKey($fileKey)) {
                $fileScores[$fileKey] += $frequency
            } else {
                $fileScores[$fileKey] = $frequency
            }
        }
    }
}

$searchTime = ((Get-Date) - $searchStartTime).TotalMilliseconds

# Filter by department if specified
if ($Department) {
    $filtered = @{}
    foreach ($fileKey in $fileScores.Keys) {
        if ($index.metadata.PSObject.Properties.Name -contains $fileKey) {
            $meta = $index.metadata.$fileKey
            if ($meta.department -eq $Department) {
                $filtered[$fileKey] = $fileScores[$fileKey]
            }
        }
    }
    $fileScores = $filtered
}

# Sort by score (relevance)
$results = $fileScores.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First $Top

Write-Host "=== SEARCH RESULTS ===" -ForegroundColor Green
Write-Host "Found: $($fileScores.Count) documents" -ForegroundColor Gray
Write-Host "Showing: Top $($results.Count) results" -ForegroundColor Gray
Write-Host "Search time: $([math]::Round($searchTime, 0))ms" -ForegroundColor Gray
Write-Host ""

if ($results.Count -eq 0) {
    Write-Host "No documents found matching query" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Try:" -ForegroundColor Cyan
    Write-Host "  - Broader search terms" -ForegroundColor Gray
    Write-Host "  - Check spelling" -ForegroundColor Gray
    Write-Host "  - Remove department filter" -ForegroundColor Gray
    exit 0
}

$resultNum = 1
foreach ($result in $results) {
    $fileKey = $result.Key
    $score = $result.Value

    # Get metadata
    $meta = $null
    if ($index.metadata.PSObject.Properties.Name -contains $fileKey) {
        $meta = $index.metadata.$fileKey
    }

    # Get file info
    $fileInfo = $null
    if ($index.files.PSObject.Properties.Name -contains $fileKey) {
        $fileInfo = $index.files.$fileKey
    }

    Write-Host "[$resultNum] " -NoNewline -ForegroundColor Cyan

    # Check if this is an image file
    $isImage = $false
    if ($meta) {
        $isImage = $meta.extension -in @(".jpg", ".jpeg", ".png", ".gif", ".bmp")
    }

    if ($isImage) {
        Write-Host "🖼️  " -NoNewline
    }

    if ($meta) {
        Write-Host "$($meta.name)" -ForegroundColor White
        Write-Host "    Department: " -NoNewline -ForegroundColor Gray
        Write-Host "$($meta.department)" -NoNewline -ForegroundColor Yellow
        Write-Host " | Type: " -NoNewline -ForegroundColor Gray

        if ($isImage) {
            Write-Host "$($meta.extension) (Image)" -NoNewline -ForegroundColor Magenta
        } else {
            Write-Host "$($meta.extension)" -NoNewline -ForegroundColor Yellow
        }

        Write-Host " | Modified: " -NoNewline -ForegroundColor Gray
        Write-Host "$($meta.modified)" -ForegroundColor Yellow
    } else {
        Write-Host "$fileKey" -ForegroundColor White
    }

    Write-Host "    Relevance Score: " -NoNewline -ForegroundColor Gray
    Write-Host "$score" -NoNewline -ForegroundColor Green

    if ($fileInfo) {
        Write-Host " | Words: " -NoNewline -ForegroundColor Gray
        Write-Host "$($fileInfo.wordCount)" -ForegroundColor Green
    }

    Write-Host ""

    # Show context if requested
    if ($ShowContext -and $fileInfo) {
        $mdPath = $fileInfo.path
        if (Test-Path $mdPath) {
            $content = Get-Content $mdPath -Raw -Encoding UTF8

            # For images, show AI description snippet if available
            if ($isImage -and $content -match '## AI-Generated Description\s+(.{0,200})') {
                $descriptionSnippet = $matches[1].Trim() -replace '\s+', ' '
                if ($descriptionSnippet -notmatch '^\[') {
                    Write-Host "    AI Description: " -NoNewline -ForegroundColor Gray
                    Write-Host $descriptionSnippet.Substring(0, [Math]::Min(150, $descriptionSnippet.Length)) -ForegroundColor Cyan
                    if ($descriptionSnippet.Length -gt 150) {
                        Write-Host "..." -ForegroundColor Cyan
                    }
                }
            }

            # Find first occurrence of any query word
            $found = $false
            foreach ($word in $queryWords) {
                if ($content -match "(?i)(.{0,100})($word)(.{0,100})") {
                    Write-Host "    Context: " -NoNewline -ForegroundColor Gray
                    Write-Host "...$($matches[1])" -NoNewline -ForegroundColor DarkGray
                    Write-Host "$($matches[2])" -NoNewline -ForegroundColor Yellow
                    Write-Host "$($matches[3])..." -ForegroundColor DarkGray
                    $found = $true
                    break
                }
            }

            if (-not $found) {
                Write-Host "    [Context not available]" -ForegroundColor DarkGray
            }
        }
        Write-Host ""
    }

    $resultNum++
}

Write-Host ""
Write-Host "=== SEARCH COMPLETE ===" -ForegroundColor Green
Write-Host ""
Write-Host "To view a document:" -ForegroundColor Cyan
Write-Host "  - Click the .lnk shortcut in SharePointLinks/" -ForegroundColor Gray
Write-Host "  - Or read the extracted markdown in docs/sharepoint-content/" -ForegroundColor Gray
Write-Host ""
Write-Host "To see context:" -ForegroundColor Cyan
Write-Host "  .\Search-SharePointContent.ps1 -Query '$Query' -ShowContext" -ForegroundColor Gray
Write-Host ""

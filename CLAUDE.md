# CLAUDE.md - AI Assistant Development Guidelines

**Project**: Gain Servicing - CEO Management System
**Owner**: Single CEO User (with occasional assistance)
**Status**: ✅ Foundation Complete - Data Collection Phase

**⚠️ IMPORTANT**: This file applies to ALL AI assistants (Claude Code, Augment, etc.)

**Platform-Specific Notes**:
- **Augment users**: See `System/SystemGuides/AUGMENT-ASSISTANT-GUIDE.md` for quick reference on unavoidable differences (MCPs)
- **Claude Code users**: MCPs available (Serena, Confluence) - see `System/SystemGuides/SERENA-AI-ASSISTANT-GUIDE.md`

---

## Project Overview

This is a **personal CEO management system** for Gain Servicing that provides:
- Daily executive briefings and KPI tracking
- Department oversight (Legal, Finance, Sales, Marketing, Technology)
- Risk and opportunity identification
- Direct report performance management
- Strategic initiative tracking

**Important**: This is a single-user documentation project, not a multi-developer codebase. The CEO is the primary user with occasional assistance.

---

## ⚠️ MANDATORY DOCUMENTATION RULES

**CRITICAL: This is a documentation-based management system. Follow these rules strictly:**

### Rule 1: Documentation Placement

**✅ ALLOWED locations for documentation:**

1. **Root** - ONLY `CLAUDE.md` (this file):
   - Single source of truth for AI assistant guidelines
   - Everything else goes in folders

2. **`System/`** - All system files and documentation (committed):
   - `SystemGuides/` - Official documentation
     - `README.md` - System overview and navigation
     - `QUICK-START-GUIDE.md` - 30-minute onboarding
     - `DATA-COLLECTION-MASTER-PLAN.md` - Data collection strategy
     - `GETTING-STARTED-WITH-DATA.md` - Data setup guide
     - `IMPLEMENTATION-GUIDE.md` - Detailed implementation steps
     - `SYSTEM-SUMMARY.md` - Complete system summary
     - `SHAREPOINT-SYNC-GUIDE.md` - SharePoint automation guide
   - `scripts/` - Automation scripts (32 total)
     - `Sync-SharePoint.ps1` - SharePoint sync engine
     - `Setup-AutoSync.ps1` - Task scheduler setup
     - `Setup-SharePointSync.ps1` - Simple setup wizard for Reid
     - See `scripts/README.md` for quick reference
   - `SCRIPTS-AND-TOOLS-REGISTRY.md` - **Complete inventory of all 32 scripts**
   - `INSTALLED-TOOLS-INVENTORY.md` - **Complete list of all tools and dependencies**
   - `BusinessContext/` - Organized business knowledge (see CONTEXT-COLLECTION-GUIDE.md)

3. **`SharePointDocs/` (gitignored)** - Synced SharePoint files:
   - Automated sync from SharePoint via OneDrive
   - Local copies of Marketing, Finance, Legal, etc.
   - Large files not in Git (keeps repo small)

4. **Department folders** - Operational content (CEO's daily work):
   - `CEO-Dashboard/` - Daily briefings, KPIs, action items
   - `Finance/` - KPI tracking, account performance
   - `Legal/` - Litigation, compliance, urgent matters
   - `Sales/` - High-value targets, proposals
   - `Marketing/` - Materials, ROI calculators
   - `Technology/` - Roadmap, AI initiatives

5. **`TempDocs/` (gitignored)** - Working documents and scratch workspace:
   - Analysis reports and drafts
   - Meeting notes and experiments
   - AI-generated reports before review
   - Personal brainstorming and working notes
   - **Purpose**: Scratch pad that never goes to Git - your safe workspace

**❌ FORBIDDEN actions:**
- Creating ANY new root-level files (only CLAUDE.md allowed)
- Disrupting existing CEO operational files without explicit permission
- Moving department content without CEO approval

### Rule 2: What Goes Where

**Department Folders (CEO's workspace - HANDLE WITH CARE):**
- Daily briefings and KPI alerts
- Account tracking and performance data
- Risk registers and opportunity lists
- Templates and operational documents
- **These are the CEO's active working files - modify carefully**

**`TempDocs/` (gitignored - scratch workspace):**
- Analysis reports and working drafts
- Meeting notes and experiments
- AI-generated reports before review
- Personal brainstorming and ideas
- **Purpose**: Your local scratch pad - work freely without cluttering Git

### Rule 3: Single-User Considerations

**When assisting the CEO:**
- ✅ Always ask before modifying existing operational files
- ✅ Suggest changes rather than directly editing CEO's daily work
- ✅ Create templates or examples in `docs/` first
- ✅ Be explicit about what you're changing and why
- ✅ Preserve the CEO's personal notes and formatting preferences

**Examples:**

```bash
# ✅ CORRECT - Creating analysis in TempDocs
TempDocs/analysis/finance-kpi-analysis.md      # Working document (gitignored)
TempDocs/drafts/sales-pipeline-review.md       # Draft (gitignored)
TempDocs/notes/meeting-2025-10-11.md           # Notes (gitignored)

# ✅ CORRECT - Updating templates (with permission)
Finance/account-performance/ACCOUNT-MASTER-LIST-TEMPLATE.md

# ⚠️ CAREFUL - CEO's active files (ask first)
CEO-Dashboard/daily-briefing.md
Finance/kpi-dashboard/kpi-alerts.md

# ❌ INCORRECT - New root files
FINANCE_ANALYSIS.md                            # NEVER - use TempDocs/
NEW_FEATURE_GUIDE.md                           # NEVER - use TempDocs/
```

---

## Project Structure

```
ReidCEO/
├── CLAUDE.md                   # ⭐ ONLY FILE IN ROOT - AI assistant guidelines
├── SETUP-SHAREPOINT.md         # Quick start guide for Reid
├── .gitignore                  # Excludes TempDocs/ and SharePointDocs/
│
├── System/                     # All system files and documentation
│   ├── SystemGuides/           # Official documentation (committed)
│   │   ├── README.md
│   │   ├── QUICK-START-GUIDE.md
│   │   ├── DATA-COLLECTION-MASTER-PLAN.md
│   │   ├── GETTING-STARTED-WITH-DATA.md
│   │   ├── IMPLEMENTATION-GUIDE.md
│   │   ├── SYSTEM-SUMMARY.md
│   │   └── SHAREPOINT-SYNC-GUIDE.md
│   └── scripts/                # Automation scripts (committed)
│       ├── Sync-SharePoint.ps1
│       ├── Setup-AutoSync.ps1
│       ├── Setup-SharePointSync.ps1  # ⭐ Reid's setup wizard
│       ├── sync-log.txt        # Gitignored
│       └── sync-config.json    # Gitignored
│
├── SharePointDocs/             # ⚠️ GITIGNORED - Synced SharePoint files
│   ├── README.md
│   ├── Marketing/
│   ├── Finance/
│   ├── Legal/
│   ├── Sales/
│   └── Technology/
│
├── TempDocs/                   # ⚠️ GITIGNORED - Scratch workspace
│   ├── README.md
│   ├── analysis/
│   ├── drafts/
│   ├── notes/
│   └── experiments/
│
├── CEO-Dashboard/              # ⭐ CEO's DAILY WORKSPACE
│   ├── daily-briefing.md
│   ├── kpi-scorecard/
│   ├── risk-opportunity-register/
│   ├── action-items/
│   └── direct-reports/
│
├── Finance/                    # Financial tracking and KPIs
├── Legal/                      # Legal affairs management
├── Sales/                      # High-value target tracking
├── Marketing/                  # Marketing materials and ROI
└── Technology/                 # Innovation and roadmap
```

**Key Points:**
- **Root** = ONLY CLAUDE.md + SETUP-SHAREPOINT.md (minimal!)
- **`System/`** = All system files (docs + scripts)
- **`SharePointDocs/`** = Synced SharePoint files (gitignored)
- **`TempDocs/`** = Scratch workspace (gitignored)
- **Department folders** = CEO's operational workspace (modify with care)

---

## Architecture

### System Design
This is a **file-based knowledge management system** using markdown for:
- Daily executive dashboards
- KPI tracking and alerts
- Risk and opportunity management
- Department-specific content organization
- Template-based reporting

**Not a software project**: No code, APIs, or databases. Pure documentation and knowledge management.

### Data Flow Pattern
```
CEO Daily Routine:
1. Morning: Read CEO-Dashboard/daily-briefing.md
2. Check Department Alerts:
   - Finance/kpi-dashboard/kpi-alerts.md
   - Legal/ceo-briefings/urgent-matters.md
   - Sales/ceo-briefings/hot-opportunities.md
3. Review Action Items
4. Update as needed throughout the day

Weekly Review:
1. Consolidated KPI scorecard
2. Risk and opportunity registers
3. Direct report performance
4. Strategic initiatives progress
```

### Key Components

1. **CEO Dashboard** - Command center for daily operations
2. **Department Folders** - Specialized content by business function
3. **Templates** - Reusable formats for common documents
4. **Alert System** - Exception-based reporting (kpi-alerts, urgent-matters)

---

## Development Workflow

### When Assisting the CEO

**Before Making Changes:**
1. Ask which files to modify (CEO may have personal notes)
2. Preview changes or create examples first
3. Respect existing formatting and structure
4. Confirm before moving or reorganizing files

**Safe Operations (No Permission Needed):**
- Creating new files in `TempDocs/` (gitignored)
- Analyzing existing content
- Suggesting improvements
- Creating templates or examples in TempDocs/

**Requires Permission:**
- Modifying CEO-Dashboard files
- Changing KPI structures
- Moving department files
- Reorganizing folder structure

### Adding New Features

1. **Discuss with CEO** - Understand the need
2. **Create prototype in `TempDocs/`** - Safe scratch workspace
3. **Get feedback** - Refine approach
4. **Implement in operational folders** - With CEO approval
5. **Update templates** - For future use

### SharePoint Sync Setup (For Reid)

When Reid asks to set up SharePoint syncing:

**Step 1: Guide OneDrive Sync**
- Tell Reid to open the SharePoint URL in browser
- Click "Sync" button in SharePoint
- Wait for OneDrive to complete initial sync
- Verify folder exists: `C:\Users\<USER>\OneDrive - Gain Servicing\`

**Step 2: Run Setup Wizard (with PowerShell as Admin)**
```powershell
cd "C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts"
.\Setup-SharePointSync.ps1
```

**Step 3: Guide Through Prompts**
- Confirm OneDrive sync complete
- Choose schedule (Hourly recommended for daily use)
- Test sync (recommended)

**Step 4: Verify Success**
- Check `SharePointDocs/` for synced files
- View log: `System/scripts/sync-log.txt`
- Task Scheduler: ReidCEO → ReidCEO-SharePointSync

**Common Issues:**
- "No admin rights" → Reid must run PowerShell as Administrator
- "No SharePoint folders" → Complete OneDrive sync first
- "Files not syncing" → Check OneDrive sync status in system tray

**Manual Sync Anytime:**
```powershell
cd System/scripts
.\Sync-SharePoint.ps1
```

---

## 📋 Business Context Management

**Guide**: See `System/SystemGuides/CONTEXT-COLLECTION-GUIDE.md` for complete details

### Three-Tier Context System

This project uses a permanent strategy for capturing and organizing business context about Gain Servicing:

#### Tier 1: Raw Capture (Fast & Frictionless)
**Location**: `TempDocs/context-capture/` (gitignored)
**Purpose**: Immediately capture business knowledge without worrying about organization

**When to capture**:
- After meetings with executives or direct reports
- During sales calls or client discussions
- When learning about products, processes, or strategy
- Any "lightbulb moment" about how the business works

**How to capture**:
```bash
# Meeting notes
TempDocs/context-capture/meetings/2025-10-28-cfo-quarterly-review.md

# Conversations
TempDocs/context-capture/conversations/2025-10-28-cto-ai-roadmap.md

# Quick research
TempDocs/context-capture/research/2025-10-28-competitor-analysis.md

# Today I learned (TIL)
TempDocs/context-capture/quick-notes/2025-10-28-terminology.md
```

**Rules for Tier 1**:
- ✅ Capture immediately while it's fresh
- ✅ Include date stamps (YYYY-MM-DD)
- ✅ Any format is fine (bullets, paragraphs, stream-of-consciousness)
- ✅ Mark unclear items with [?] or [VERIFY]
- ❌ Don't worry about perfect organization yet
- ❌ Don't edit or polish before saving

#### Tier 2: Organized Context (Structured & Curated)
**Location**: `System/BusinessContext/` (committed to Git)
**Purpose**: Organized, searchable business knowledge following templates

**Structure**:
```
System/BusinessContext/
├── _templates/              # Templates for organizing context
├── Company/                 # Company overview, business model, industry
├── Products/                # Product/service details (PA, Express, PCA, etc.)
├── Operations/              # Processes (underwriting, collections, etc.)
├── Departments/             # Finance, Legal, Sales, Marketing, Technology
├── Strategy/                # Strategic objectives, growth plans
├── Glossary/                # Business terms, acronyms, KPI definitions
└── _archive/                # Outdated context (YYYY-MM/)
```

**Rules for Tier 2**:
- ✅ Use templates from `_templates/`
- ✅ Follow naming convention: `kebab-case.md`
- ✅ Include header: Last Updated, Owner, Status, Related Context
- ✅ Create cross-references to related files
- ✅ Mark status: Draft | Active | Review Needed | Outdated
- ❌ Don't organize until weekly/monthly review cycle

#### Tier 3: AI Integration (Searchable & Accessible)
**Location**: Serena AI memories (`.serena/memories/`)
**Purpose**: Distilled knowledge for AI assistant consumption

**When to update**:
- After organizing major context domains (Company, Products, Operations)
- When business model or products change significantly
- Quarterly refresh to keep AI assistance current

**Memory topics**:
- `project_overview.md` - High-level business summary
- `business_model.md` - How Gain Servicing makes money
- `products_services.md` - Product details
- `tech_stack.md` - Technology architecture
- `development_guidelines.md` - Coding standards (for scripts)

### Review & Maintenance Schedule

**Weekly Review (15-30 min)**:
- Review `TempDocs/context-capture/` for the week
- Identify urgent context needing immediate organization
- Sort into domains (don't fully organize yet)

**Monthly Organization (1-2 hours)**:
- Fully organize raw captures into `System/BusinessContext/`
- Apply templates and create cross-references
- Archive outdated context to `_archive/YYYY-MM/`
- Commit organized context to Git

**Quarterly Refresh (2-3 hours)**:
- Review all organized context for accuracy
- Update status tags (Draft → Active, etc.)
- Refresh Serena memory files
- Update CEO-Dashboard shortcuts
- Identify gaps in context coverage

### AI Assistant Guidelines for Context

**When CEO shares business context with you:**

1. **Offer to capture it** - "Would you like me to save this to TempDocs/context-capture/?"
2. **Suggest organization** - "This looks like product context - should go in System/BusinessContext/Products/"
3. **Apply templates** - Point to appropriate template in `_templates/`
4. **Create cross-references** - Link related context files automatically
5. **Update memories** - Refresh Serena memories when context stabilizes

**Proactive behaviors**:
- Alert when TempDocs captures are >1 month old (suggest organizing)
- **Identify and track context gaps** - Document what we DON'T know
- Offer to create Serena memory files from organized context
- Suggest CEO-Dashboard shortcuts to frequently-accessed context
- **Detect context conflicts** - Compare new captures against existing organized context
- **Track open items systematically** - Maintain gap tracker and domain-specific open items files

### Context Conflict Detection (CRITICAL)

**Mandatory before updating any organized context:**

**When organizing new context (Tier 1 → Tier 2), you MUST:**

1. **Search existing context** for related information
   ```
   - Search System/BusinessContext/ for similar topics
   - Check Serena memories for relevant context
   - Review recent captures in TempDocs
   ```

2. **Compare for conflicts**
   - Numbers/metrics (e.g., "$50K" vs "$75K")
   - Process descriptions (changed workflows)
   - Strategic statements (shifted priorities)
   - Definitions (terminology ambiguity)
   - Dates/timelines (project milestones)

3. **If conflict detected → PAUSE and investigate:**
   - Which source is more recent?
   - Which source is more authoritative? (CEO > Department Head > Team)
   - Can I verify against SharePoint documents?
   - Is this business evolution (intentional change) or error?

4. **Resolution paths:**

   **Path A: High Confidence - Resolve Automatically**
   - New source is clearly more authoritative AND recent
   - Change is expected (updated metrics, team sizes)
   - Conflict is minor and factual

   **Action**: Update context + Inform CEO with resolution summary

   **Path B: Uncertain - Escalate to CEO**
   - Sources equally authoritative but contradictory
   - Strategic direction conflict
   - Significant business impact
   - Cannot verify correct version

   **Action**: Present conflict + investigation + ask CEO to decide

   **Path C: Business Evolution - Document Change**
   - Conflict indicates intentional change
   - Strategic priorities shifted
   - Process has evolved

   **Action**: Archive old context + Create new + Notify CEO

**Conflict Notification Templates:**

**Resolved Automatically:**
```markdown
✅ CONTEXT CONFLICT RESOLVED

**Topic**: [Brief description]
**Conflict**: [Old info] vs. [New info]
**Resolution**: Updated to [new] based on [reasoning]
**Action**: Updated [file path]
**Verification**: [If CEO should double-check]
```

**Escalation to CEO:**
```markdown
⚠️ CONTEXT CONFLICT - CEO INPUT NEEDED

**Topic**: [What conflict is about]
**Priority**: Critical | High | Normal

**Conflicting Information**:
- Source A: [Info + source]
- Source B: [Info + source]

**Investigation**: [What I found]

**Question for CEO**: [Specific question]

**Impact**: [Why this matters]
```

**Business Evolution:**
```markdown
📊 BUSINESS CONTEXT EVOLVED

**Topic**: [What changed]
**Previous**: [Old context]
**Current**: [New context]
**Action**: Archived old to _archive/, updated current
**Files Updated**: [List]
```

**Mandatory Check Points:**
- ✅ Before updating ANY existing context file
- ✅ When new capture contradicts Serena memory
- ✅ When organizing captures with specific claims (numbers, dates, names)
- ✅ During monthly context review

**Full Details**: See `System/SystemGuides/CONTEXT-COLLECTION-GUIDE.md` - "Context Conflict Detection & Resolution" section

---

### Context Gap Tracking (SYSTEMATIC)

**Principle**: Track what we DON'T know, not just what we do know.

**When organizing context, you MUST:**

1. **Identify gaps** while organizing:
   - Template sections unfilled?
   - Questions unanswered?
   - Information marked [VERIFY] or [ESTIMATE]?
   - References to non-existent context?
   - Conflicts unresolvable due to missing info?

2. **Document gaps systematically**:
   - Add to domain-specific open items file
   - Update central tracker (`context-gaps-tracker.md`)
   - Categorize priority: 🔴 CRITICAL | 🟡 HIGH | 🟢 NORMAL
   - Categorize type: Missing | Incomplete | Ambiguous | Unverified | Outdated | Conflicting

3. **Prioritize and resolve**:
   - 🔴 **CRITICAL**: Flag to CEO immediately (blocks decisions)
   - 🟡 **HIGH**: Include in weekly summary, batch resolution
   - 🟢 **NORMAL**: Track passively, fill opportunistically

**Gap Tracking Files**:
- Central: `System/BusinessContext/context-gaps-tracker.md`
- Domains: `Company/company-open-items.md`, `Products/products-open-items.md`, etc.

**Gap Categories**:
- **[MISSING]** - No answer yet
- **[INCOMPLETE]** - Partial answer, needs detail
- **[AMBIGUOUS]** - Multiple interpretations
- **[VERIFY]** / **[ESTIMATE]** - Unconfirmed information
- **[REVIEW NEEDED]** - May be outdated
- **[CONFLICT]** - Sources contradict

**Weekly CEO Summary Should Include**:
```markdown
## Context Gaps Status
- 🔴 CRITICAL: 2 items (action this week)
- 🟡 HIGH: 8 items (batch resolution recommended)
- 🟢 NORMAL: 55 items (fill opportunistically)

**Critical Gaps**:
1. [Gap requiring immediate attention]
2. [Gap requiring immediate attention]
```

**Full Details**: See `System/SystemGuides/CONTEXT-COLLECTION-GUIDE.md` - "Context Gap Tracking & Open Items" section

---

### Where Context Goes - Quick Reference

```bash
# ✅ TIER 1: Raw Capture (Immediate, unorganized)
TempDocs/context-capture/meetings/2025-10-28-topic.md
TempDocs/context-capture/conversations/2025-10-28-person.md
TempDocs/context-capture/research/2025-10-28-topic.md
TempDocs/context-capture/quick-notes/2025-10-28-til.md

# ✅ TIER 2: Organized Context (Weekly/monthly organization)
System/BusinessContext/Company/business-model.md
System/BusinessContext/Products/express-funding.md
System/BusinessContext/Operations/underwriting-process.md
System/BusinessContext/Glossary/business-glossary.md

# ✅ TIER 3: AI Integration (Quarterly refresh)
.serena/memories/business_model.md
.serena/memories/products_services.md

# ❌ NEVER put business context here
CEO-Dashboard/              # CEO's operational workspace, not for context docs
Finance/                    # Department operational files, not for context docs
Root directory              # No files except CLAUDE.md + SETUP-SHAREPOINT.md
```

### Success Metrics

**You'll know the system is working when**:
- ✅ Capture is effortless (no friction to dump knowledge)
- ✅ Context is findable (locate information in <2 minutes)
- ✅ AI is helpful (Claude/Serena give context-aware assistance)
- ✅ Decisions are informed (business context readily available)
- ✅ Nothing is lost (important insights preserved and accessible)

---

## Common Tasks

### For AI Assistants Working with ReidCEO

**Creating Analysis Reports:**
```bash
# ✅ Create in TempDocs/analysis/
TempDocs/analysis/q4-2025-finance-review.md
TempDocs/analysis/sales-pipeline-health.md
```

**Working on Drafts:**
```bash
# ✅ Safe workspace for drafts
TempDocs/drafts/new-kpi-proposal.md
TempDocs/notes/meeting-with-cfo-2025-10-11.md
```

**Updating Templates:**
```bash
# ⚠️ Ask CEO first, then update
Finance/account-performance/ACCOUNT-MASTER-LIST-TEMPLATE.md
CEO-Dashboard/action-items/ceo-action-items.md
```

**Adding New Accounts or Tracking:**
```bash
# ✅ Safe - creating new entries from templates
Finance/account-performance/accounts/ABC-Company.md
Sales/high-value-targets/target-profiles/XYZ-Corp.md
```

---

## Key Files & Patterns

### Critical Files (CEO's Daily Workflow)
- `CEO-Dashboard/daily-briefing.md` - Start here every morning
- `Finance/kpi-dashboard/kpi-alerts.md` - Exception reporting
- `Legal/ceo-briefings/urgent-matters.md` - Time-sensitive legal items
- `Sales/ceo-briefings/hot-opportunities.md` - Active sales targets

### Template Patterns
- **Master Lists**: `*-MASTER-LIST-TEMPLATE.md` (e.g., ACCOUNT-MASTER-LIST-TEMPLATE.md)
- **Briefings**: `ceo-briefings/*.md` in each department folder
- **Tracking**: `*-tracker.md` files for ongoing monitoring
- **Alerts**: `*-alerts.md` for exception-based reporting

### Naming Conventions
- **Dates**: YYYY-MM-DD format
- **Status**: [DRAFT], [REVIEW], [FINAL], [URGENT] prefixes
- **Versions**: v1, v2, v3 suffixes when needed
- **Priority**: 🔴 CRITICAL, 🟡 HIGH, 🟢 NORMAL, ⚪ INFO

---

## 🚨 CRITICAL REMINDERS FOR AI AGENTS

### Single-User Project Considerations

**This is NOT a multi-developer codebase:**
- ❌ No branch protection needed
- ❌ No PR review process
- ❌ No CODEOWNERS file
- ✅ Direct updates to main branch (with CEO permission)
- ✅ Focus on user experience, not developer workflow

**The CEO is the primary user:**
- Ask before modifying operational files
- Respect personal notes and preferences
- Suggest rather than directly change
- Create examples in `TempDocs/` first
- Be explicit about changes

**Documentation Rules (Quick Reference):**
1. **Root** = ONLY CLAUDE.md + SETUP-SHAREPOINT.md (nothing else!)
2. **`System/SystemGuides/`** = Official documentation (committed)
3. **`System/scripts/`** = Automation scripts (committed)
4. **`SharePointDocs/`** = Synced SharePoint files (gitignored)
5. **`TempDocs/`** = Scratch workspace (gitignored, modify freely)
6. **Department folders** = CEO's workspace (ask before modifying)

**Before Creating ANY File:**
- Analysis/draft/notes? → `TempDocs/` (gitignored scratch workspace)
- System documentation? → `System/SystemGuides/` (committed, ask CEO first)
- Automation script? → `System/scripts/` (committed, ask CEO first)
- Operational content? → Department folder (ask CEO first)
- Root file? → NEVER (only CLAUDE.md + SETUP-SHAREPOINT.md allowed)

---

## 🔍 SharePoint Search & Indexing

**Critical**: SharePoint content is **already indexed and searchable by AI assistants**!

### How It Works

1. **SharePoint files synced** → `SharePointDocs/` (via OneDrive)
2. **Content extracted** → `docs/sharepoint-content/*.md` (text from PDFs, DOCX, PPTX, XLSX)
3. **Indexes built** → JSON files AI can read directly:
   - `docs/sharepoint-content/search-index.json` - Full-text search index
   - `docs/sharepoint-content/semantic-embeddings.json` - Semantic search
   - `SharePointLinks/index.json` - File catalog

### AI Assistants Can Search SharePoint Content Directly

**Method 1: Read the index files** (fastest)
```
# View the search index structure
view docs/sharepoint-content/search-index.json

# Search for specific terms in extracted content
view docs/sharepoint-content/ (directory listing)
view docs/sharepoint-content/Marketing_*.md (specific files)
```

**Method 2: Use PowerShell search scripts** (when user needs formatted results)
```powershell
# User runs these (AI can guide):
.\Search-SharePointContent.ps1 -Query "customer experience"
.\Search-SemanticContent.ps1 -Query "lead generation strategies"
```

**When to use each:**
- **AI reads index directly**: When answering questions about SharePoint content
- **User runs scripts**: When user wants formatted search results or to explore content

### Platform-Specific Tools

**Claude Code only** (optional enhancements):
- Serena MCP - Code intelligence for Git-tracked files
- Confluence MCP - Confluence integration
- See: `System/SystemGuides/SERENA-AI-ASSISTANT-GUIDE.md`

**All other tools work universally** across all AI assistants.

---

## Version History

**Version 1.8** (2025-10-29)
- Created minimal `AUGMENT-ASSISTANT-GUIDE.md` focusing only on unavoidable differences (MCPs)
- Clarified SharePoint indexing system - AI can read indexes directly via `view` tool
- Confirmed: Augment cannot use MCPs (Model Context Protocol) - Claude Code only
- CLAUDE.md remains universal source of truth for all AI assistants
- Platform differences: Only MCPs (Serena, Confluence) are Claude Code-specific

**Version 1.6** (2025-10-28)
- Added systematic context gap tracking system
- Central tracker (`context-gaps-tracker.md`) for all open items across domains
- Domain-specific open items files for detailed gap tracking
- Three-tier priority system: 🔴 CRITICAL | 🟡 HIGH | 🟢 NORMAL
- Six gap types: Missing, Incomplete, Ambiguous, Unverified, Outdated, Conflicting
- AI mandatory gap identification during context organization
- Batch resolution workflow for efficient CEO time usage
- Weekly/monthly gap status reporting
- Domain coverage assessment (% complete)
- Target: All domains >50% coverage within 6 months

**Version 1.5** (2025-10-28)
- Added context conflict detection and resolution system
- AI assistants now proactively detect conflicts when organizing context
- Three resolution paths: Auto-resolve, Escalate to CEO, Document evolution
- Conflict notification templates for CEO communication
- Quarterly context audit process
- Maintenance tags: [VERIFY], [ESTIMATE], [AS OF DATE], [DEPRECATED]
- Created `TempDocs/CONTEXT-CONFLICT-TEMPLATE.md` for documenting conflicts

**Version 1.4** (2025-10-28)
- Added comprehensive business context management system (three-tier strategy)
- Created `CONTEXT-COLLECTION-GUIDE.md` with permanent context capture strategy
- Defined Tier 1 (Raw Capture), Tier 2 (Organized), Tier 3 (AI Integration)
- Established `TempDocs/context-capture/` for frictionless knowledge capture
- Documented `System/BusinessContext/` structure for organized context
- Added AI assistant guidelines for proactive context management

**Version 1.3** (2025-10-11)
- Created `System/` root folder containing SystemGuides/ and scripts/
- Added automated SharePoint syncing with `SharePointDocs/`
- Created simple setup wizard for Reid: `System/scripts/Setup-SharePointSync.ps1`
- Added `SETUP-SHAREPOINT.md` quick start guide
- Updated all paths to reflect System/ structure

**Version 1.2** (2025-10-11)
- Moved all system guides to `SystemGuides/` folder
- Root now contains ONLY CLAUDE.md (maximum simplicity)
- Created clear folder hierarchy

**Version 1.1** (2025-10-11)
- Changed from `docs/` to `TempDocs/` for clarity
- TempDocs/ serves as gitignored scratch workspace
- Simplified for single-user workflow

**Version 1.0** (2025-10-11)
- Initial CLAUDE.md creation
- Established documentation rules
- Defined single-user workflow patterns

---

## Questions or Improvements?

Document suggestions in: `TempDocs/notes/claude-md-improvements.md`

The CEO can modify this file at any time to reflect evolving needs.

<#
.SYNOP<PERSON>S
    Creates and maintains a portable SharePoint file index with local shortcuts

.DESCRIPTION
    This script:
    1. Scans OneDrive SharePoint folders
    2. Creates a portable index.json (relative paths - committed to Git)
    3. Creates local config.json with user's OneDrive root (gitignored)
    4. Generates Windows shortcuts using index + local config
    5. Works across different machines with different OneDrive paths

.PARAMETER UpdateIndex
    Update the shared index.json from OneDrive files

.PARAMETER CreateShortcuts
    Create shortcuts from index.json using local config

.PARAMETER Both
    Update index AND create shortcuts (default)

.EXAMPLE
    .\Sync-SharePointIndex.ps1
    Updates index and creates shortcuts

.EXAMPLE
    .\Sync-SharePointIndex.ps1 -UpdateIndex
    Only updates the shared index

.EXAMPLE
    .\Sync-SharePointIndex.ps1 -CreateShortcuts
    Only creates shortcuts from existing index

.NOTES
    Author: ReidCEO Project
    Date: 2025-10-11
    Version: 2.0 - Portable approach
#>

[CmdletBinding()]
param(
    [switch]$UpdateIndex,
    [switch]$CreateShortcuts,
    [switch]$Both,
    [switch]$NoAutoSync  # Prevents automatic Sync-All trigger (used when called FROM Sync-All)
)

# Default to Both if no switches specified
if (-not $UpdateIndex -and -not $CreateShortcuts -and -not $Both) {
    $Both = $true
}

if ($Both) {
    $UpdateIndex = $true
    $CreateShortcuts = $true
}

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$SharePointLinksRoot = Join-Path $ProjectRoot "SharePointLinks"
$IndexFile = Join-Path $SharePointLinksRoot "index.json"
$ConfigFile = Join-Path $SharePointLinksRoot "config.json"
$LogFile = Join-Path $PSScriptRoot "sharepoint-index-log.txt"

# Initialize log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

Write-Log "=== SharePoint Index Sync Started ==="

# Function to find OneDrive SharePoint root
function Find-OneDriveRoot {
    Write-Log "Detecting OneDrive SharePoint root path..."

    $possiblePaths = @(
        "$env:USERPROFILE\OneDrive - Gain Servicing",
        "$env:USERPROFILE\OneDrive - GainServicing",
        "$env:USERPROFILE\Gain Servicing",
        "$env:USERPROFILE\Gainservicing",
        "$env:OneDriveCommercial"
    )

    foreach ($path in $possiblePaths) {
        if ($path -and (Test-Path $path)) {
            Write-Log "Found OneDrive root: $path" "SUCCESS"
            return $path
        }
    }

    # Check registry
    $registryPaths = @(
        "HKCU:\Software\Microsoft\OneDrive\Accounts\Business1",
        "HKCU:\Software\Microsoft\OneDrive\Accounts\Business2"
    )

    foreach ($regPath in $registryPaths) {
        try {
            $userFolder = Get-ItemProperty -Path $regPath -Name "UserFolder" -ErrorAction SilentlyContinue
            if ($userFolder -and (Test-Path $userFolder.UserFolder)) {
                Write-Log "Found OneDrive root via registry: $($userFolder.UserFolder)" "SUCCESS"
                return $userFolder.UserFolder
            }
        } catch {
            # Continue
        }
    }

    return $null
}

# Function to get or create local config
function Get-LocalConfig {
    if (Test-Path $ConfigFile) {
        Write-Log "Loading existing config: $ConfigFile"
        $config = Get-Content $ConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json

        # Verify path still exists
        if (Test-Path $config.oneDriveRoot) {
            return $config
        } else {
            Write-Log "Configured OneDrive path no longer exists: $($config.oneDriveRoot)" "WARN"
        }
    }

    # Create new config
    Write-Log "Creating new local config..."
    $oneDriveRoot = Find-OneDriveRoot

    if (-not $oneDriveRoot) {
        Write-Log "Could not find OneDrive SharePoint folder!" "ERROR"
        Write-Log "Please sync SharePoint to OneDrive first." "ERROR"
        throw "OneDrive SharePoint folder not found"
    }

    $config = @{
        oneDriveRoot = $oneDriveRoot
        userName = $env:USERNAME
        computerName = $env:COMPUTERNAME
        lastUpdated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    # Create SharePointLinks folder if needed
    if (-not (Test-Path $SharePointLinksRoot)) {
        New-Item -ItemType Directory -Path $SharePointLinksRoot -Force | Out-Null
    }

    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $ConfigFile -Encoding UTF8
    Write-Log "Created config: $ConfigFile" "SUCCESS"
    Write-Log "OneDrive root: $oneDriveRoot"

    return $config
}

# Function to scan OneDrive and create index
function Update-SharePointIndex {
    param([PSCustomObject]$Config)

    Write-Log "Scanning OneDrive folder: $($Config.oneDriveRoot)"

    $index = @{
        generatedBy = $Config.userName
        generatedOn = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        oneDriveRoot = "OneDrive - Gain Servicing"  # Relative marker
        files = @()
    }

    # Find department folders
    $departmentFolders = @("Marketing", "Finance", "Legal", "Sales", "Technology", "Operations")
    $foundFolders = @()

    foreach ($dept in $departmentFolders) {
        $deptPath = Join-Path $Config.oneDriveRoot $dept
        if (Test-Path $deptPath) {
            $foundFolders += @{name = $dept; path = $deptPath}
            Write-Log "Found department folder: $dept"
        }
    }

    if ($foundFolders.Count -eq 0) {
        Write-Log "No department folders found in OneDrive root" "WARN"
        Write-Log "Looking for FDrive or Shared Documents..." "INFO"

        # Check for common SharePoint structures
        $commonPaths = @("FDrive", "Shared Documents", "Documents")
        foreach ($commonPath in $commonPaths) {
            $fullPath = Join-Path $Config.oneDriveRoot $commonPath
            if (Test-Path $fullPath) {
                Write-Log "Found SharePoint root: $commonPath"
                # Check for departments inside
                foreach ($dept in $departmentFolders) {
                    $deptPath = Join-Path $fullPath $dept
                    if (Test-Path $deptPath) {
                        $foundFolders += @{name = "$commonPath\$dept"; path = $deptPath}
                        Write-Log "Found department folder: $commonPath\$dept"
                    }
                }
            }
        }

        # Check for "F Drive - {Department}" pattern
        if ($foundFolders.Count -eq 0) {
            Write-Log "Looking for 'F Drive - {Department}' pattern..." "INFO"
            foreach ($dept in $departmentFolders) {
                $fdriveName = "F Drive - $dept"
                $fdrivePath = Join-Path $Config.oneDriveRoot $fdriveName
                if (Test-Path $fdrivePath) {
                    $foundFolders += @{name = $dept; path = $fdrivePath; fdriveName = $fdriveName}
                    Write-Log "Found department folder: $fdriveName"
                }
            }
        }
    }

    if ($foundFolders.Count -eq 0) {
        Write-Log "No recognizable folders found!" "ERROR"
        throw "Could not find Marketing, Finance, Legal, etc. folders in OneDrive"
    }

    # Scan each folder
    $fileCount = 0
    foreach ($folder in $foundFolders) {
        Write-Log "Scanning $($folder.name)..."

        $files = Get-ChildItem -Path $folder.path -Recurse -File -ErrorAction SilentlyContinue |
                 Where-Object { $_.Extension -in @('.pdf', '.xlsx', '.docx', '.pptx', '.csv', '.txt', '.png', '.jpg') }

        foreach ($file in $files) {
            # Create relative path from OneDrive root
            $relativePath = $file.FullName.Substring($Config.oneDriveRoot.Length).TrimStart('\', '/')

            $fileInfo = @{
                path = $relativePath
                name = $file.Name
                extension = $file.Extension
                sizeBytes = $file.Length
                sizeMB = [math]::Round($file.Length / 1MB, 2)
                modified = $file.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
                department = $folder.name
            }

            $index.files += $fileInfo
            $fileCount++
        }
    }

    Write-Log "Found $fileCount files across $($foundFolders.Count) departments"

    # Save index with UTF-8 encoding to preserve special characters
    $index | ConvertTo-Json -Depth 10 | Set-Content -Path $IndexFile -Encoding UTF8
    Write-Log "Saved index: $IndexFile" "SUCCESS"

    return $index
}

# Function to create shortcut
function Create-Shortcut {
    param(
        [string]$TargetPath,
        [string]$ShortcutPath
    )

    try {
        $WScriptShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WScriptShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = $TargetPath
        $Shortcut.Save()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($WScriptShell) | Out-Null
        return $true
    } catch {
        Write-Log "Failed to create shortcut: $ShortcutPath - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to create shortcuts from index
function Create-ShortcutsFromIndex {
    param([PSCustomObject]$Config)

    if (-not (Test-Path $IndexFile)) {
        Write-Log "Index file not found: $IndexFile" "ERROR"
        Write-Log "Run with -UpdateIndex first to create the index" "ERROR"
        throw "Index file not found"
    }

    Write-Log "Loading index: $IndexFile"
    $index = Get-Content $IndexFile -Raw -Encoding UTF8 | ConvertFrom-Json

    Write-Log "Creating shortcuts for $($index.files.Count) files..."

    $shortcutCount = 0
    $skippedCount = 0

    foreach ($fileInfo in $index.files) {
        # Build full target path using local config
        $targetPath = Join-Path $Config.oneDriveRoot $fileInfo.path

        # Build shortcut path
        $relativePath = $fileInfo.path
        $shortcutPath = Join-Path $SharePointLinksRoot $relativePath
        $shortcutPath = $shortcutPath + ".lnk"
        $shortcutFolder = Split-Path -Parent $shortcutPath

        # Create folder structure if needed
        if (-not (Test-Path $shortcutFolder)) {
            New-Item -ItemType Directory -Path $shortcutFolder -Force | Out-Null
        }

        # Check if target file exists (use Get-Item for better UTF-8 support)
        try {
            $targetFile = Get-Item $targetPath -ErrorAction Stop
        } catch {
            Write-Log "Target file not found, skipping: $($fileInfo.path)" "WARN"
            $skippedCount++
            continue
        }

        # Check if shortcut already exists and is up-to-date
        if (Test-Path $shortcutPath) {
            $shortcutFile = Get-Item $shortcutPath

            # Skip if shortcut is newer than target
            if ($shortcutFile.LastWriteTime -ge $targetFile.LastWriteTime) {
                $skippedCount++
                continue
            }

            # Remove old shortcut
            Remove-Item $shortcutPath -Force
        }

        # Create shortcut
        $success = Create-Shortcut -TargetPath $targetPath -ShortcutPath $shortcutPath
        if ($success) {
            $shortcutCount++
            if ($shortcutCount % 50 -eq 0) {
                Write-Log "  Created $shortcutCount shortcuts so far..."
            }
        }
    }

    Write-Log "Created $shortcutCount shortcuts, skipped $skippedCount existing" "SUCCESS"

    # Create README
    $readmePath = Join-Path $SharePointLinksRoot "README.md"
    $readmeContent = @"
# SharePoint Links

This folder contains Windows shortcuts to files synced from SharePoint via OneDrive.

## How It Works

**Portable Design:**
- ``index.json`` - Shared catalog of files (committed to Git)
- ``config.json`` - Your local OneDrive path (gitignored)
- ``*.lnk`` files - Shortcuts generated from index + config

**Works across different machines!** Each user has their own OneDrive path in config.json.

## Usage

**Click any .lnk file** to open the file from OneDrive.

## Updating

``````powershell
cd System\scripts

# Update index from OneDrive + create shortcuts
.\Sync-SharePointIndex.ps1

# Only update index
.\Sync-SharePointIndex.ps1 -UpdateIndex

# Only create shortcuts
.\Sync-SharePointIndex.ps1 -CreateShortcuts
``````

## Last Updated

Index generated: $($index.generatedOn)
By: $($index.generatedBy)
Total files: $($index.files.Count)
Shortcuts created: $shortcutCount
"@

    $readmeContent | Set-Content -Path $readmePath
    Write-Log "Created README.md"
}

# Main execution
try {
    # Get or create local config
    $config = Get-LocalConfig

    # Update index if requested
    if ($UpdateIndex) {
        Write-Log "=== Updating SharePoint Index ===" "INFO"
        $index = Update-SharePointIndex -Config $config
    }

    # Create shortcuts if requested
    if ($CreateShortcuts) {
        Write-Log "=== Creating Shortcuts ===" "INFO"
        Create-ShortcutsFromIndex -Config $config
    }

    Write-Log "=== SharePoint Index Sync Complete ===" "SUCCESS"
    Write-Log "Index: $IndexFile"
    Write-Log "Config: $ConfigFile"
    Write-Log "Shortcuts: $SharePointLinksRoot"

    # If we updated the index, automatically trigger full sync workflow (unless NoAutoSync is set)
    if ($UpdateIndex -and -not $NoAutoSync) {
        Write-Log "" "INFO"
        Write-Log "=== Triggering Automatic Content Sync ===" "INFO"
        Write-Log "Running incremental sync (extracts new files + rebuilds indexes)..." "INFO"
        Write-Log "" "INFO"

        try {
            # Run Sync-All in incremental mode (only processes new/changed files)
            & "$PSScriptRoot\Sync-All.ps1" -IncrementalOnly
            Write-Log "Automatic sync completed successfully" "SUCCESS"
        } catch {
            Write-Log "Automatic sync failed (you can run manually: .\Sync-All.ps1)" "WARN"
            Write-Log "Error: $($_.Exception.Message)" "WARN"
        }
    }

} catch {
    Write-Log "ERROR: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}

Write-Log "=== Finished ==="

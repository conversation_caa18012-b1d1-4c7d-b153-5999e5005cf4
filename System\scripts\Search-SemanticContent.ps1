<#
.SYNOPSIS
    Semantic search of SharePoint content using AI embeddings

.DESCRIPTION
    Finds conceptually similar documents, not just keyword matches:
    - Understands "customer satisfaction" = "client happiness"
    - Finds related concepts across departments
    - Ranks by semantic similarity (0-100%)

.PARAMETER Query
    Search query (natural language)

.PARAMETER Top
    Number of results to return (default: 10)

.PARAMETER Department
    Filter results by department

.PARAMETER Threshold
    Minimum similarity threshold 0-1 (default: 0.1)

.PARAMETER ShowContext
    Show text snippets around matches

.EXAMPLE
    .\Search-SemanticContent.ps1 -Query "customer satisfaction strategies"
    Finds documents about customer satisfaction

.EXAMPLE
    .\Search-SemanticContent.ps1 -Query "how to acquire new clients" -Top 5
    Finds lead generation and acquisition strategies

.EXAMPLE
    .\Search-SemanticContent.ps1 -Query "risk management" -Department Finance -ShowContext
    Finds Finance docs about risk, shows context

.NOTES
    Requires: Embeddings generated by Generate-SemanticIndex.ps1
    Model: all-MiniLM-L6-v2 (384 dimensions)
    Search time: < 100ms
    Cost: $0 (runs locally)
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$Query,

    [int]$Top = 10,

    [string]$Department = "",

    [double]$Threshold = 0.1,

    [switch]$ShowContext
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$ContentRoot = Join-Path $ProjectRoot "docs\sharepoint-content"
$EmbeddingsFile = Join-Path $ContentRoot "semantic-embeddings.json"
$MetadataFile = Join-Path $ProjectRoot "SharePointLinks\index.json"
$PythonScript = Join-Path $PSScriptRoot "semantic_search.py"

# Check if embeddings exist
if (-not (Test-Path $EmbeddingsFile)) {
    Write-Host "ERROR: Semantic embeddings not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Generate embeddings first:" -ForegroundColor Yellow
    Write-Host "  cd System\scripts" -ForegroundColor Gray
    Write-Host "  .\Generate-SemanticIndex.ps1" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

Write-Host "Semantic Search: " -NoNewline -ForegroundColor Cyan
Write-Host $Query -ForegroundColor Yellow
Write-Host ""

# Create search script if needed
if (-not (Test-Path $PythonScript)) {
    $pythonCode = @'
"""
Semantic search using pre-generated embeddings
"""

import json
import sys
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

def main():
    query = sys.argv[1]
    embeddings_file = sys.argv[2]
    top_n = int(sys.argv[3])
    threshold = float(sys.argv[4])

    # Load embeddings
    with open(embeddings_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Load model
    model = SentenceTransformer(data['model'])

    # Generate query embedding
    query_embedding = model.encode(query)

    # Convert stored embeddings to numpy array
    file_keys = list(data['embeddings'].keys())
    doc_embeddings = np.array([data['embeddings'][key] for key in file_keys])

    # Calculate similarities
    similarities = cosine_similarity([query_embedding], doc_embeddings)[0]

    # Get results above threshold
    results = []
    for i, (file_key, similarity) in enumerate(zip(file_keys, similarities)):
        if similarity >= threshold:
            results.append({
                "file_key": file_key,
                "similarity": float(similarity),
                "rank": 0
            })

    # Sort by similarity
    results.sort(key=lambda x: x['similarity'], reverse=True)

    # Add ranks
    for rank, result in enumerate(results[:top_n], 1):
        result['rank'] = rank

    # Output as JSON
    print(json.dumps(results[:top_n], indent=2))

if __name__ == "__main__":
    main()
'@

    $pythonCode | Set-Content -Path $PythonScript -Encoding UTF8
}

# Run semantic search
Write-Host "Searching..." -ForegroundColor Cyan
$startTime = Get-Date

try {
    $results = python $PythonScript $Query $EmbeddingsFile $Top $Threshold | ConvertFrom-Json

    $searchTime = ((Get-Date) - $startTime).TotalMilliseconds

    # Load metadata
    $metadata = @{}
    if (Test-Path $MetadataFile) {
        $indexData = Get-Content $MetadataFile -Raw -Encoding UTF8 | ConvertFrom-Json

        foreach ($file in $indexData.files) {
            $fileKey = "$($file.department)_$($file.path -replace '\\', '_' -replace ' ', '_')"
            $metadata[$fileKey] = @{
                name = $file.name
                path = $file.path
                department = $file.department
                extension = $file.extension
                sizeMB = $file.sizeMB
                modified = $file.modified
            }
        }
    }

    # Filter by department if specified
    if ($Department) {
        $results = $results | Where-Object {
            $fileKey = $_.file_key
            if ($metadata.ContainsKey($fileKey)) {
                $metadata[$fileKey].department -eq $Department
            } else {
                $false
            }
        }
    }

    Write-Host "=== SEMANTIC SEARCH RESULTS ===" -ForegroundColor Green
    Write-Host "Query: " -NoNewline -ForegroundColor Gray
    Write-Host $Query -ForegroundColor White
    Write-Host "Found: " -NoNewline -ForegroundColor Gray
    Write-Host "$($results.Count) documents" -ForegroundColor White
    Write-Host "Search time: " -NoNewline -ForegroundColor Gray
    Write-Host "$([math]::Round($searchTime, 0))ms" -ForegroundColor White
    Write-Host ""

    if ($results.Count -eq 0) {
        Write-Host "No documents found matching query" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Try:" -ForegroundColor Cyan
        Write-Host "  - Broader search terms" -ForegroundColor Gray
        Write-Host "  - Lower threshold: -Threshold 0.05" -ForegroundColor Gray
        Write-Host "  - Remove department filter" -ForegroundColor Gray
        exit 0
    }

    foreach ($result in $results) {
        $fileKey = $result.file_key
        $similarity = $result.similarity
        $rank = $result.rank

        Write-Host "[$rank] " -NoNewline -ForegroundColor Cyan

        if ($metadata.ContainsKey($fileKey)) {
            $meta = $metadata[$fileKey]

            Write-Host "$($meta.name)" -ForegroundColor White
            Write-Host "    Department: " -NoNewline -ForegroundColor Gray
            Write-Host "$($meta.department)" -NoNewline -ForegroundColor Yellow
            Write-Host " | Type: " -NoNewline -ForegroundColor Gray
            Write-Host "$($meta.extension)" -NoNewline -ForegroundColor Yellow
            Write-Host " | Modified: " -NoNewline -ForegroundColor Gray
            Write-Host "$($meta.modified)" -ForegroundColor Yellow
        } else {
            Write-Host "$fileKey" -ForegroundColor White
        }

        Write-Host "    Similarity: " -NoNewline -ForegroundColor Gray
        $similarityPct = [math]::Round($similarity * 100, 1)
        Write-Host "$similarityPct%" -NoNewline -ForegroundColor Green

        # Show quality indicator
        if ($similarity -gt 0.5) {
            Write-Host " (Excellent match)" -ForegroundColor Green
        } elseif ($similarity -gt 0.3) {
            Write-Host " (Good match)" -ForegroundColor Yellow
        } elseif ($similarity -gt 0.15) {
            Write-Host " (Fair match)" -ForegroundColor DarkYellow
        } else {
            Write-Host " (Weak match)" -ForegroundColor DarkGray
        }

        # Show context if requested
        if ($ShowContext -and $metadata.ContainsKey($fileKey)) {
            $mdPath = Join-Path $ContentRoot "$fileKey.md"
            if (Test-Path $mdPath) {
                $content = Get-Content $mdPath -Raw -Encoding UTF8

                # Get first 200 chars of main content
                if ($content -match '## Content\s+([\s\S]{1,200})') {
                    $snippet = $matches[1] -replace '\s+', ' '
                    Write-Host "    Preview: " -NoNewline -ForegroundColor Gray
                    Write-Host "$snippet..." -ForegroundColor DarkGray
                }
            }
        }

        Write-Host ""
    }

    Write-Host "=== SEARCH COMPLETE ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "To view a document:" -ForegroundColor Cyan
    Write-Host "  - Click the .lnk shortcut in SharePointLinks/" -ForegroundColor Gray
    Write-Host "  - Or read the extracted markdown in docs/sharepoint-content/" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Tips:" -ForegroundColor Cyan
    Write-Host "  - Use natural language queries" -ForegroundColor Gray
    Write-Host "  - Similarity > 30% = good match" -ForegroundColor Gray
    Write-Host "  - Similarity > 50% = excellent match" -ForegroundColor Gray
    Write-Host ""

} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Email Templates

**Purpose**: Reusable email templates for common CEO communications
**Location**: `System/EmailTemplates/`
**Used By**: Email automation scripts in `System/scripts/`

---

## Template Structure

Each template is a simple text file with placeholders:

```
SUBJECT: {{subject}}
TO: {{to}}
CC: {{cc}}
---
{{body}}
```

**Placeholders** are replaced by scripts before sending.

---

## Available Templates

### Daily/Weekly Reports
- `daily-briefing.txt` - Morning executive summary
- `weekly-summary.txt` - Weekly team update
- `kpi-alert.txt` - KPI threshold breach notification

### Action Items & Tasks
- `action-item-assigned.txt` - Task assignment notification
- `action-item-reminder.txt` - Task deadline reminder
- `action-item-completed.txt` - Task completion notification

### Team Communications
- `meeting-request.txt` - Standard meeting invitation
- `quick-question.txt` - Quick question template
- `follow-up.txt` - Follow-up after meeting

### Department-Specific
- `finance-question.txt` - Financial inquiry
- `legal-update-request.txt` - Legal matter follow-up
- `sales-opportunity.txt` - Hot sales opportunity notification

---

## Using Templates

### Method 1: Direct Script Call
```powershell
# Load template and send
.\Send-TemplatedEmail.ps1 -Template "action-item-assigned" `
    -To "<EMAIL>" `
    -Variables @{
        task = "Review Q4 budget projections"
        deadline = "Friday, Nov 1"
        priority = "High"
    }
```

### Method 2: Integration with Other Scripts
```powershell
# Automated daily briefing
.\Email-DailyBriefing.ps1  # Uses daily-briefing.txt template

# Action item notifications
.\Email-ActionItems.ps1 -ItemId 123  # Uses action-item-assigned.txt
```

---

## Template Variables

### Common Variables (Available in all templates)
- `{{date}}` - Current date
- `{{sender_name}}` - Your name
- `{{company}}` - Gain Servicing

### Template-Specific Variables
See individual template files for specific placeholder variables.

---

## Creating New Templates

**Steps**:
1. Copy an existing template
2. Modify subject, recipients, and body
3. Use `{{variable_name}}` for dynamic content
4. Save with descriptive filename
5. Document in this README

**File naming**: `kebab-case-description.txt`

---

## Template Categories

### 📊 Reports & Briefings
Automated summaries and reports

### 📋 Action Items & Tasks
Task management communications

### 👥 Team Communications
General team coordination

### 🏢 Department-Specific
Targeted department communications

---

## Best Practices

**When to use templates**:
✅ Repetitive communications (daily/weekly)
✅ Standardized notifications (action items)
✅ Structured requests (meeting invites)

**When NOT to use templates**:
❌ Personal, sensitive communications
❌ Complex, unique situations
❌ When template feels too rigid

**Template guidelines**:
- Keep subject lines clear and actionable
- Use friendly but professional tone
- Include all necessary context
- Make variables obvious with `{{brackets}}`

---

## Related Scripts

- `Send-Email.ps1` - Core email sender
- `Send-TemplatedEmail.ps1` - Template processor
- `Email-DailyBriefing.ps1` - Automated daily briefing
- `Email-ActionItems.ps1` - Action item notifications
- `Email-WeeklySummary.ps1` - Weekly team update

---

*Templates make repetitive communications fast and consistent.*

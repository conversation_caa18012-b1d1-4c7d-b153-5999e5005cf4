# SharePoint Links Setup - Portable Approach

**Time:** 5 minutes | **Works across different machines!**

---

## 🎯 How This Works (Portable Design)

### The Problem:
Your OneDrive path: `C:\Users\<USER>\OneDrive - Gain Servicing\`
<PERSON>'s OneDrive path: `C:\Users\<USER>\OneDrive - Gain Servicing\`

Different paths would break regular shortcuts!

### The Solution:
**Metadata-based portable shortcuts!**

```
SharePointLinks/
├── index.json              ← Shared catalog (committed to Git)
│                             Lists all files with relative paths
├── config.json             ← Local OneDrive root (gitignored)
│                             Your machine: C:\Users\<USER>\...
│                             <PERSON>'s machine: C:\Users\<USER>\...
└── Marketing/
    └── Report.pdf.lnk      ← Generated from index + config
                              Works on any machine!
```

**How it works:**
1. **Scan OneDrive** → Creates `index.json` with relative paths
2. **Commit index** → Everyone shares the same catalog
3. **Each machine** has its own `config.json` (gitignored)
4. **Script combines** index + config → creates shortcuts

**Result:** Shortcuts work on any machine!

---

## 🚀 Setup Process

### Step 1: Sync SharePoint to OneDrive (One-Time)

1. **Open SharePoint** in browser:
   ```
   https://appriver3651007941.sharepoint.com/sites/FDrive
   ```

2. **Navigate** to folder (Marketing, Finance, Legal, etc.)

3. **Click "Sync"** button (cloud icon)

4. **OneDrive opens** and starts syncing

5. **Wait** for sync to complete (watch system tray icon)

6. **Verify** files synced to:
   ```
   C:\Users\<USER>\OneDrive - Gain Servicing\
   ```

### Step 2: Run the Index Script

**Open PowerShell** (no admin needed):

```powershell
cd "C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts"

# First run: Creates index + config + shortcuts
.\Sync-SharePointIndex.ps1
```

**What this does:**
1. Detects your OneDrive root path
2. Creates `config.json` with your path (gitignored)
3. Scans OneDrive folders
4. Creates `index.json` with file catalog (committed to Git)
5. Generates shortcuts using index + config

### Step 3: Commit and Push (If You're the First)

**If you created the index:**

```powershell
cd ../..
git add SharePointLinks/index.json
git add SharePointLinks/README.md
git commit -m "Add SharePoint index"
git push
```

**If someone else created it:**
Just pull and run the script - it will use the existing index!

---

## 🔄 For the Second User (Reid)

### Reid's Process:

1. **Pull latest** from GitHub:
   ```bash
   git pull
   ```

2. **Sync SharePoint to OneDrive** (same as Step 1 above)

3. **Run the script:**
   ```powershell
   cd System\scripts
   .\Sync-SharePointIndex.ps1 -CreateShortcuts
   ```

**That's it!** Reid's shortcuts will:
- Use the shared `index.json` (from Git)
- Create his own `config.json` with his OneDrive path
- Generate shortcuts that work on his machine

**No conflicts!** Each user has their own config, shared index.

---

## 📁 What Gets Committed vs. Gitignored

### ✅ Committed to Git:
```
SharePointLinks/
├── index.json          ← Shared catalog of files
└── README.md           ← Documentation
```

### ❌ Gitignored (Local Only):
```
SharePointLinks/
├── config.json         ← Your OneDrive root path
└── **/*.lnk            ← All shortcut files
```

**Why?**
- `index.json` is portable (relative paths work everywhere)
- `config.json` is machine-specific (your OneDrive path)
- `.lnk` files are generated (can recreate from index + config)

---

## 🔄 Updating the Index

### ⚡ NEW: Automated Full Sync (Recommended)

**When new files are added to SharePoint, use the master sync script:**

```powershell
# Full sync: Updates everything (index, content, shortcuts, search)
.\Sync-All.ps1
```

**What this does automatically:**
1. ✅ Updates SharePoint index (scans OneDrive for new files)
2. ✅ Extracts content (PDF/DOCX/XLSX/PPTX → searchable markdown)
3. ✅ Creates/updates shortcuts
4. ✅ Rebuilds keyword index (fast word search)
5. ✅ Rebuilds semantic embeddings (AI semantic search)

**Performance:**
- Small update (few files): ~30 seconds
- Full sync (284 files): ~5 minutes

**Optional parameters:**
```powershell
# Sync only Marketing department
.\Sync-All.ps1 -Department Marketing

# Skip content extraction (faster, only update indexes)
.\Sync-All.ps1 -SkipExtraction

# Skip semantic embeddings (faster, only keyword search)
.\Sync-All.ps1 -SkipSemanticIndex
```

---

### Manual Options (Advanced)

**Option 1: Update index and shortcuts**
```powershell
.\Sync-SharePointIndex.ps1
```

**Option 2: Just update index** (commit this)
```powershell
.\Sync-SharePointIndex.ps1 -UpdateIndex
git add SharePointLinks/index.json
git commit -m "Update SharePoint index"
git push
```

**Option 3: Just recreate shortcuts** (from existing index)
```powershell
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

### Other users pull and recreate:
```bash
git pull
cd System/scripts
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

---

## 📊 Example: How It Works

### Your Machine (jrazz):

**config.json** (gitignored):
```json
{
  "oneDriveRoot": "C:\\Users\\<USER>\\OneDrive - Gain Servicing",
  "userName": "jrazz",
  "computerName": "DESKTOP-ABC"
}
```

### Reid's Machine:

**config.json** (gitignored):
```json
{
  "oneDriveRoot": "C:\\Users\\<USER>\\OneDrive - Gain Servicing",
  "userName": "Reid",
  "computerName": "REID-LAPTOP"
}
```

### Shared (Committed to Git):

**index.json**:
```json
{
  "generatedBy": "jrazz",
  "generatedOn": "2025-10-11 18:00:00",
  "files": [
    {
      "path": "Marketing\\Reports\\Leadership Financials 2507.pdf",
      "name": "Leadership Financials 2507.pdf",
      "sizeMB": 2.1,
      "modified": "2025-10-07",
      "department": "Marketing"
    }
  ]
}
```

### Script Combines:

**Your machine:**
```
C:\Users\<USER>\OneDrive - Gain Servicing\Marketing\Reports\Leadership Financials 2507.pdf
```

**Reid's machine:**
```
C:\Users\<USER>\OneDrive - Gain Servicing\Marketing\Reports\Leadership Financials 2507.pdf
```

**Same relative path, different absolute paths - both work!**

---

## 💡 Benefits of This Approach

### vs. Regular Shortcuts:
- ✅ Works across different machines
- ✅ Works with different OneDrive paths
- ✅ Portable index (committed to Git)
- ✅ Each user's paths stay local

### vs. Copying Files:
- ✅ Tiny Git repo (just index.json)
- ✅ No file duplication
- ✅ Always up-to-date (OneDrive syncs)
- ✅ Click shortcuts to open instantly

---

## 🐛 Troubleshooting

### "OneDrive SharePoint folder not found"
**Cause:** OneDrive not syncing yet

**Fix:**
1. Open SharePoint in browser
2. Click "Sync" button
3. Wait for OneDrive to sync
4. Run script again

### Shortcuts point to wrong path
**Cause:** config.json has old OneDrive path

**Fix:**
```powershell
# Delete config and regenerate
Remove-Item SharePointLinks\config.json
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

### Want to update index
```powershell
# Rescan OneDrive and update index
.\Sync-SharePointIndex.ps1 -UpdateIndex

# Commit if you want others to get updates
git add SharePointLinks/index.json
git commit -m "Update SharePoint index"
git push
```

### After pulling new index, shortcuts missing
```powershell
# Recreate shortcuts from new index
.\Sync-SharePointIndex.ps1 -CreateShortcuts
```

---

## 📚 Files Created

### SharePointLinks/index.json (Committed)
- Catalog of all files
- Relative paths from OneDrive root
- File metadata (size, date, etc.)
- Portable across machines

### SharePointLinks/config.json (Gitignored)
- Your local OneDrive root path
- Machine-specific
- Auto-created on first run
- Each user has their own

### SharePointLinks/**/*.lnk (Gitignored)
- Windows shortcuts
- Generated from index + config
- Recreated when needed
- Machine-specific

---

## 🎯 Summary

**Setup once:**
1. Sync SharePoint to OneDrive
2. Run `.\Sync-SharePointIndex.ps1`
3. Commit `index.json` (if first user)

**Daily use:**
- Click shortcuts in `SharePointLinks/`
- Files open from OneDrive
- No manual management needed

**When new files added:**
- Run `.\Sync-SharePointIndex.ps1 -UpdateIndex`
- Commit new `index.json`
- Others pull and run `-CreateShortcuts`

**Works everywhere!** Your paths, Reid's paths - doesn't matter. The index is portable! 🎉

---

**Questions? Run the script - it's self-documenting with lots of helpful logs!**

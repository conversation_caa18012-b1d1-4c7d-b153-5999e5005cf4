# [Process/Workflow Name]

**Last Updated**: YYYY-MM-DD
**Owner**: [Department Head or Process Owner]
**Status**: Draft | Active | Review Needed | Outdated
**Related Context**:
- [Link to related process](./related-process.md)
- [Link to product context](../Products/related-product.md)

---

## Quick Summary
[2-3 sentence summary: What is this process, why does it exist, what's the outcome?]

---

## Process Overview

### Purpose
[Why does this process exist? What business need does it serve?]

### Scope
**Includes**:
- [What's covered by this process]
- [What's covered by this process]

**Excludes**:
- [What's NOT covered - handled elsewhere]

### Key Stakeholders
- **Owner**: [Who is accountable]
- **Participants**: [Who is involved]
- **Customers**: [Who benefits/receives output]

---

## Process Flow

### High-Level Steps

```
[Input] → Step 1 → Step 2 → Step 3 → [Output]
```

### Detailed Workflow

#### Step 1: [Step Name]
**Who**: [Role responsible]
**What**: [Description of what happens]
**Time**: [How long this typically takes]
**Tools**: [Systems or tools used]

**Inputs**:
- [What's needed to start]

**Outputs**:
- [What's produced]

**Decision Points**:
- If [condition], then [action]
- If [condition], then [action]

---

#### Step 2: [Step Name]
**Who**: [Role responsible]
**What**: [Description of what happens]
**Time**: [How long this typically takes]
**Tools**: [Systems or tools used]

**Inputs**:
- [What's needed from previous step]

**Outputs**:
- [What's produced]

**Decision Points**:
- If [condition], then [action]

---

#### Step 3: [Step Name]
[Continue pattern for all major steps]

---

### Process Diagram
[If a visual exists, reference it here or describe the flow]

```
[Consider creating a simple text-based flowchart or Mermaid diagram]
```

---

## Roles & Responsibilities

### [Role Name]
**Responsibilities**:
- [Specific task or decision]
- [Specific task or decision]

**Required Skills/Authority**:
- [What they need to do this job]

### [Role Name]
[Repeat for all key roles]

---

## Performance Metrics

### Process KPIs
| Metric | Current | Target | Status |
|--------|---------|--------|--------|
| Cycle Time | [Days/Hours] | [Target] | 🟢/🟡/🔴 |
| Error Rate | [%] | [Target] | 🟢/🟡/🔴 |
| Throughput | [#/Day or Month] | [Target] | 🟢/🟡/🔴 |
| [Custom KPI] | [Value] | [Target] | 🟢/🟡/🔴 |

### Success Criteria
- [What defines successful process execution]
- [Quality threshold]

---

## Current State Assessment

### What's Working Well
- [Strength]
- [Strength]

### Pain Points
- **[Pain Point]**: [Description]
  - **Impact**: [How this affects outcomes]
  - **Frequency**: [How often this occurs]
- **[Pain Point]**: [Description]
  - **Impact**: [How this affects outcomes]

### Bottlenecks
- **[Bottleneck]**: [Where the process slows down]
  - **Cause**: [Why this happens]
  - **Impact**: [Effect on throughput/quality]

---

## Dependencies

### Upstream Dependencies
[What must happen before this process can start?]
- [Dependency]
- [Dependency]

### Downstream Dependencies
[What depends on this process completing?]
- [Dependent process]
- [Dependent process]

### System Dependencies
- **[System/Tool]**: [How it's used, what if it fails]
- **[System/Tool]**: [How it's used, what if it fails]

---

## Risk Management

### Process Risks
- **[Risk]**: [Description]
  - **Likelihood**: High | Medium | Low
  - **Impact**: High | Medium | Low
  - **Mitigation**: [How we prevent or address]

### Failure Modes
- **[Failure Mode]**: [What can go wrong]
  - **Detection**: [How we know it happened]
  - **Recovery**: [How we fix it]

---

## Improvement Opportunities

### Quick Wins
- [Improvement that could be implemented quickly]
- [Improvement that could be implemented quickly]

### Long-Term Improvements
- **[Opportunity]**: [Description]
  - **Benefit**: [What would improve]
  - **Investment Required**: [Time, money, resources]
  - **Priority**: High | Medium | Low

### Automation Potential
[Where could this process be automated? What's the ROI?]

---

## Standard Operating Procedures

### Normal Operation
[Standard happy-path instructions]

### Exception Handling
- **[Exception]**: [How to handle]
- **[Exception]**: [How to handle]

### Escalation Criteria
[When should this be escalated to management?]
- [Trigger for escalation]
- [Who to escalate to]

---

## Related Context

### Products
- [Link to product that uses this process](../Products/related-product.md)

### Other Processes
- [Link to upstream process](./upstream-process.md)
- [Link to downstream process](./downstream-process.md)

### Technology
- [Link to technology context](../Departments/technology-context.md)

---

## Historical Context

### Process History
- **Established**: [Date]
- **Last Major Change**: [Date - what changed]

### Evolution
[How has this process changed over time?]

### Lessons Learned
- [Key insight from running this process]
- [Key insight from running this process]

---

## Documentation & Training

### Related Documentation
- SOP: [Link to detailed SOP if exists]
- Training Materials: [Link if exists]
- Systems Guides: [Link to tool documentation]

### Training Requirements
[What training do people need to perform this process?]

---

## Sources

- Meeting: [Date, with whom]
- Process Observation: [Date]
- Documentation: [Link to existing docs]
- Interview: [Person, date]

---

## Questions / To Investigate

- [ ] [Open question about the process]
- [ ] [Area needing clarification or measurement]

---

**Next Review**: [Date when this should be reviewed for accuracy]
**Process Owner Contact**: [Name, email, phone]

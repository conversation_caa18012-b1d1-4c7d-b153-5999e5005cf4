# Email System Guide - ReidCEO

**Last Updated**: 2025-10-28
**Status**: Active - Complete email automation system
**Purpose**: Send emails directly from your Outlook account with automation

---

## Overview

The ReidCEO email system provides **comprehensive email integration** for:
- ✅ **Automated reports** (daily briefings, weekly summaries)
- ✅ **Action item notifications** (task assignments, reminders)
- ✅ **Quick templated emails** (common communications)
- ✅ **Manual emails** (ad-hoc communications)

**Key Feature**: Uses your existing Outlook account - no separate credentials needed!

---

## Quick Start (2 Minutes)

### Send Your First Email

```powershell
cd System/scripts

# Open draft email for review
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Quick Question" `
    -Body "Do we have the Q4 budget numbers ready?"
```

**That's it!** Outlook will open with a draft ready to review and send.

---

## System Components

### 1. Core Email Sender
**Script**: `Send-Email.ps1`
**Purpose**: Send any email from Outlook
**Features**:
- Draft mode (review before sending) - **Default and recommended**
- Immediate send mode (automated emails)
- Attachments support
- HTML or plain text
- CC/BCC recipients
- Priority levels
- Read/delivery receipts

### 2. Template System
**Location**: `System/EmailTemplates/`
**Purpose**: Reusable email templates with variables
**Included Templates**:
- `daily-briefing.txt` - Morning executive summary
- `weekly-summary.txt` - Weekly team update
- `action-item-assigned.txt` - Task assignment
- `kpi-alert.txt` - KPI threshold breach

### 3. Template Processor
**Script**: `Send-TemplatedEmail.ps1`
**Purpose**: Load template, replace variables, send email
**Features**:
- Variable substitution (`{{variable}}` format)
- Automatic variables (date, company, sender_name)
- Template validation
- Preview mode

### 4. Automation Scripts
**Scripts**:
- `Email-DailyBriefing.ps1` - Automated daily briefing
- `Email-ActionItems.ps1` - Action item notifications
- More can be added easily

---

## Usage Examples

### Example 1: Simple Email

```powershell
# Draft email (opens for review - SAFE)
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Team Meeting Tomorrow" `
    -Body "Don't forget our team meeting at 10am tomorrow."
```

### Example 2: Email with Attachment

```powershell
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Q4 Report" `
    -Body "Attached is the Q4 financial report." `
    -Attachments "C:\Reports\Q4-Report.pdf"
```

### Example 3: High Priority Email

```powershell
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Urgent: Contract Review Needed" `
    -Body "Please review attached contract ASAP." `
    -Importance High `
    -Attachments "contract.pdf"
```

### Example 4: Send Immediately (Automated)

```powershell
# USE WITH CAUTION - Sends without review
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Automated Alert" `
    -Body "System notification." `
    -Send
```

### Example 5: HTML Email

```powershell
$htmlBody = @"
<html>
<body>
<h1>Quarterly Report</h1>
<p>Here are the key metrics:</p>
<ul>
<li><strong>Revenue:</strong> $2.5M</li>
<li><strong>Growth:</strong> 15%</li>
</ul>
</body>
</html>
"@

.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Q4 Results" `
    -Body $htmlBody `
    -BodyFormat HTML
```

### Example 6: Multiple Recipients & CC

```powershell
.\Send-Email.ps1 `
    -To "<EMAIL>, <EMAIL>" `
    -Cc "<EMAIL>" `
    -Subject "Budget Discussion" `
    -Body "Let's discuss the 2026 budget next week."
```

---

## Template System

### Using Templates

```powershell
# Send action item notification
.\Send-TemplatedEmail.ps1 -Template "action-item-assigned" -Variables @{
    assignee_name = "John Smith"
    assignee_email = "<EMAIL>"
    task_title = "Review Q4 Budget Projections"
    priority = "High"
    deadline = "Friday, November 1"
    task_description = "Please review the attached budget projections and provide feedback."
    task_context = "We need to finalize the budget before the board meeting."
    next_steps = "1. Review projections`n2. Provide feedback by deadline`n3. Schedule meeting if needed"
    task_link = "CEO-Dashboard/action-items/budget-review.md"
}
```

### Creating New Templates

**Step 1**: Create template file in `System/EmailTemplates/`

**File**: `System/EmailTemplates/my-template.txt`
```
SUBJECT: {{subject_line}}
TO: {{recipient_email}}
CC: {{cc_emails}}
---
Hi {{recipient_name}},

{{message_body}}

{{closing}}

Best regards,
{{sender_name}}
```

**Step 2**: Use template
```powershell
.\Send-TemplatedEmail.ps1 -Template "my-template" -Variables @{
    subject_line = "Hello"
    recipient_email = "<EMAIL>"
    recipient_name = "John"
    message_body = "This is the main message."
    closing = "Looking forward to hearing from you."
}
```

### Available Variables

**Automatic (always available)**:
- `{{date}}` - Current date (e.g., "October 28, 2025")
- `{{datetime}}` - Current date and time
- `{{company}}` - "Gain Servicing"
- `{{sender_name}}` - Your Windows username (can be overridden)

**Template-specific**:
- Define your own with `{{variable_name}}`
- Pass values via `-Variables @{variable_name = "value"}`

---

## Automation Scripts

### Daily Briefing Email

```powershell
# Send your daily briefing as email
.\Email-DailyBriefing.ps1

# Opens draft with content from CEO-Dashboard/daily-briefing.md
```

**What it does**:
1. Reads `CEO-Dashboard/daily-briefing.md`
2. Extracts sections (KPIs, urgent items, priorities, alerts)
3. Formats into email using template
4. Opens draft for review

**Automate it** (optional):
```powershell
# Run at 7am every weekday via Task Scheduler
# Task: Email-DailyBriefing
# Schedule: Weekdays at 7:00 AM
# Action: PowerShell.exe -File "C:\...\Email-DailyBriefing.ps1" -Send
```

### Action Item Notifications

```powershell
# Notify team member of new action item
.\Email-ActionItems.ps1 `
    -ActionItemFile "CEO-Dashboard/action-items/review-contract.md" `
    -AssigneeName "Legal Team" `
    -AssigneeEmail "<EMAIL>" `
    -Priority "High" `
    -Deadline "Tomorrow"
```

**What it does**:
1. Reads action item file
2. Extracts task details
3. Formats into email using template
4. Opens draft for review

---

## Common Workflows

### Workflow 1: Morning Routine

```powershell
# Generate and review your daily briefing email
cd System/scripts
.\Email-DailyBriefing.ps1

# Review in Outlook, make edits if needed, click Send
```

### Workflow 2: Assign Task to Team Member

```powershell
# Create action item notification
.\Email-ActionItems.ps1 `
    -ActionItemFile "path/to/task.md" `
    -AssigneeName "Jane Doe" `
    -AssigneeEmail "<EMAIL>" `
    -Deadline "Next Friday"

# Review in Outlook, send
```

### Workflow 3: Send Weekly Summary

```powershell
# (Script coming soon: Email-WeeklySummary.ps1)
# For now, use template manually:

.\Send-TemplatedEmail.ps1 -Template "weekly-summary" -Variables @{
    to = "<EMAIL>"
    week_start = "October 21"
    accomplishments = "- Closed 3 major deals`n- Launched new marketing campaign"
    metrics = "- Revenue: +12%`n- Customer satisfaction: 95%"
    challenges = "- Hiring delays`n- System upgrade needed"
    next_week_priorities = "1. Complete Q4 planning`n2. Hire 2 engineers"
    team_updates = "John promoted to Senior Manager"
}
```

### Workflow 4: Quick Ad-Hoc Email

```powershell
# Fast one-liner for quick questions
.\Send-Email.ps1 -To "<EMAIL>" -Subject "Budget?" -Body "Do we have Q4 numbers?"
```

---

## Advanced Features

### Multiple Attachments

```powershell
$files = @(
    "C:\Reports\Q4-Report.pdf",
    "C:\Reports\Budget-2026.xlsx",
    "C:\Reports\Summary.docx"
)

.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Q4 Materials" `
    -Body "Attached are the Q4 materials for review." `
    -Attachments $files
```

### Piping Content

```powershell
# Generate report and email it
Get-Content report.txt | Out-String | ForEach-Object {
    .\Send-Email.ps1 -To "<EMAIL>" -Subject "Report" -Body $_
}
```

### Save to Drafts (Don't Display)

```powershell
# Create draft silently (don't pop up Outlook window)
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Draft Email" `
    -Body "This is saved to drafts." `
    -SaveToDrafts
```

### Batch Emails

```powershell
# Send to multiple people (separate emails)
$recipients = @(
    @{Name="John"; Email="<EMAIL>"}
    @{Name="Jane"; Email="<EMAIL>"}
)

foreach ($recipient in $recipients) {
    .\Send-TemplatedEmail.ps1 -Template "my-template" -Variables @{
        recipient_name = $recipient.Name
        recipient_email = $recipient.Email
        # ... other variables
    } -Send  # Automate if confident in template
}
```

---

## Safety Features

### Draft-First Philosophy

**By default, emails open as drafts** for review before sending. This prevents accidental sends.

```powershell
# SAFE - Opens draft
.\Send-Email.ps1 -To "<EMAIL>" -Subject "Test" -Body "Test"

# SENDS IMMEDIATELY - Use with caution
.\Send-Email.ps1 -To "<EMAIL>" -Subject "Test" -Body "Test" -Send
```

### Template Preview

```powershell
# Shows preview before opening Outlook
.\Send-TemplatedEmail.ps1 -Template "my-template" -Variables @{...}

# Output shows:
# ═══════════════════════════════════════
# Email Preview (from template: my-template)
# To: <EMAIL>
# Subject: Hello
# Body: (preview shown)
# ═══════════════════════════════════════
```

### Unresolved Variable Warnings

```powershell
# If you forget a variable:
.\Send-TemplatedEmail.ps1 -Template "action-item-assigned" -Variables @{
    assignee_email = "<EMAIL>"
    # Oops - forgot other variables!
}

# Warning displayed:
# WARNING: Unresolved variables found:
#   - {{task_title}}
#   - {{priority}}
#   - {{deadline}}
```

---

## Troubleshooting

### "Could not connect to Outlook"

**Problem**: Outlook desktop app not running or not installed

**Solutions**:
1. Install Outlook desktop app (part of Microsoft 365)
2. Ensure Outlook is configured with your email account
3. Open Outlook at least once to complete setup

### "Attachment not found"

**Problem**: File path incorrect or file doesn't exist

**Solutions**:
1. Use full path: `C:\Full\Path\To\File.pdf`
2. Check file exists: `Test-Path "C:\path\to\file.pdf"`
3. Use quotes for paths with spaces

### "Template not found"

**Problem**: Template doesn't exist or wrong name

**Solutions**:
1. Check available templates:
   ```powershell
   Get-ChildItem System\EmailTemplates\*.txt
   ```
2. Use template name without `.txt` extension
3. Create template if needed

### Email Doesn't Send

**Problem**: Draft created but not sent (even with `-Send`)

**Solutions**:
1. Check Outlook "Outbox" folder
2. Ensure Outlook is online (not in offline mode)
3. Check email account settings in Outlook

---

## Best Practices

### ✅ DO:
- **Use draft mode** for important emails (default behavior)
- **Test templates** before automating with `-Send`
- **Use templates** for repetitive communications
- **Add attachments** with full paths
- **Review emails** in Outlook before sending

### ❌ DON'T:
- Don't use `-Send` flag until you've tested thoroughly
- Don't hardcode email addresses (use variables or parameters)
- Don't send sensitive info without encryption
- Don't automate emails to external recipients without review

### 💡 TIPS:
- **Create templates** for emails you send often
- **Schedule automations** via Windows Task Scheduler
- **Use variables** to personalize bulk emails
- **Test with yourself** first (send to your own email)

---

## Integration with ReidCEO System

### Daily Briefing Integration

```powershell
# Morning routine (automated or manual)
.\Email-DailyBriefing.ps1 -To "<EMAIL>"
```

**Reads**: `CEO-Dashboard/daily-briefing.md`
**Sends**: Formatted email with sections

### Action Items Integration

```powershell
# When assigning task
.\Email-ActionItems.ps1 `
    -ActionItemFile "CEO-Dashboard/action-items/new-task.md" `
    -AssigneeName "Team Member" `
    -AssigneeEmail "<EMAIL>"
```

**Reads**: Action item markdown file
**Sends**: Task assignment notification

### KPI Alerts Integration

```powershell
# (Future: Automated KPI monitoring)
# For now, manual KPI alerts:

.\Send-TemplatedEmail.ps1 -Template "kpi-alert" -Variables @{
    to = "<EMAIL>"
    kpi_name = "Monthly Revenue"
    current_value = "$1.8M"
    threshold = "$2.0M"
    variance = "-10%"
    alert_type = "Below Threshold"
    context = "Revenue trending below target for October."
    recommended_actions = "1. Review sales pipeline`n2. Accelerate deal closures"
}
```

---

## Task Scheduler Setup (Optional Automation)

### Automate Daily Briefing

**Step 1**: Open Task Scheduler
**Step 2**: Create Basic Task
- Name: "ReidCEO Daily Briefing Email"
- Trigger: Daily at 7:00 AM (weekdays only)
- Action: Start a program
  - Program: `PowerShell.exe`
  - Arguments: `-ExecutionPolicy Bypass -File "C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts\Email-DailyBriefing.ps1" -Send -To "<EMAIL>"`
  - Start in: `C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts`

**Step 3**: Configure settings
- Run whether user is logged on or not: No (requires Outlook open)
- Run with highest privileges: No

**Result**: Every weekday at 7am, briefing email sent automatically

---

## Extending the System

### Add New Template

1. Create `System/EmailTemplates/my-new-template.txt`
2. Add placeholders: `{{variable_name}}`
3. Use with `Send-TemplatedEmail.ps1`

### Add New Automation Script

**Example**: `Email-WeeklySummary.ps1`

```powershell
# Email-WeeklySummary.ps1
param(
    [string]$To = "",
    [switch]$Send
)

# 1. Gather data from various sources
# 2. Populate variables hashtable
# 3. Call Send-TemplatedEmail.ps1 with "weekly-summary" template
```

### Integration with Other Scripts

```powershell
# Any script can send emails
# Example: After generating report, email it

# Generate-Report.ps1
$reportContent = ... # Generate report
$reportFile = ... # Save to file

# Email it
.\Send-Email.ps1 `
    -To "<EMAIL>" `
    -Subject "Weekly Report - $(Get-Date -Format 'MMM d')" `
    -Body $reportContent `
    -Attachments $reportFile `
    -Send
```

---

## Email System Architecture

```
┌─────────────────────────────────────────────────────────┐
│              ReidCEO Email System                        │
└─────────────────────────────────────────────────────────┘

User/Script
    │
    ├─── Direct Email ──────────→ Send-Email.ps1
    │                                    │
    │                                    ↓
    ├─── Templated Email ───→ Send-TemplatedEmail.ps1
    │                              │         │
    │                              ↓         ↓
    │                         Templates   Send-Email.ps1
    │                              │         │
    ├─── Daily Briefing ────→ Email-DailyBriefing.ps1
    │                              │
    │                              ↓
    ├─── Action Items ──────→ Email-ActionItems.ps1
    │                              │
    │                              ↓
    └───────────────────────→ Send-TemplatedEmail.ps1
                                   │
                                   ↓
                              Send-Email.ps1
                                   │
                                   ↓
                            Outlook COM API
                                   │
                                   ↓
                         Your Outlook Account
                                   │
                                   ↓
                             Recipients
```

---

## Quick Reference

### Core Commands

```powershell
# Simple email
.\Send-Email.ps1 -To "email" -Subject "subject" -Body "body"

# Template email
.\Send-TemplatedEmail.ps1 -Template "name" -Variables @{key="value"}

# Daily briefing
.\Email-DailyBriefing.ps1

# Action item
.\Email-ActionItems.ps1 -ActionItemFile "path"
```

### Common Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `-To` | Recipient email | `"<EMAIL>"` |
| `-Cc` | CC recipients | `"<EMAIL>"` |
| `-Subject` | Email subject | `"Hello"` |
| `-Body` | Email body | `"Message text"` |
| `-Attachments` | File(s) to attach | `"file.pdf"` or `@("a.pdf", "b.doc")` |
| `-Send` | Send immediately | Switch (no value) |
| `-Importance` | Priority level | `High`, `Normal`, `Low` |
| `-BodyFormat` | Text or HTML | `Text`, `HTML` |

---

## Related Documentation

- **SCRIPTS-AND-TOOLS-REGISTRY.md** - All scripts inventory
- **System/scripts/README.md** - Quick script reference
- **System/EmailTemplates/README.md** - Template system details
- **CLAUDE.md** - AI assistant guidelines

---

## Version History

**Version 1.0** (2025-10-28)
- Initial email system
- Core sender (Send-Email.ps1)
- Template system (4 templates)
- Template processor
- Daily briefing automation
- Action item notifications
- Complete documentation

---

*The ReidCEO email system makes CEO communications fast, consistent, and professional.*

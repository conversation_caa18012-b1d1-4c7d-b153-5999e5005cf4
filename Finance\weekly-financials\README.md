# Weekly Financials

**Purpose:** Track weekly financial performance for real-time CEO visibility  
**Update Frequency:** Weekly (every Friday or Monday)  
**Owner:** CFO

---

## 📋 Purpose

This folder provides **weekly financial snapshots** to enable:
- Real-time performance tracking (don't wait for month-end)
- Early identification of trends and issues
- Faster decision-making
- More frequent CEO-CFO alignment
- Better cash flow management

---

## 📁 Folder Structure

```
weekly-financials/
├── README.md (this file)
├── 2025-W41-financial-snapshot.xlsx (Week ending 2025-10-11)
├── 2025-W42-financial-snapshot.xlsx (Week ending 2025-10-18)
├── 2025-W43-financial-snapshot.xlsx (Week ending 2025-10-25)
└── ... (one file per week)
```

---

## 📊 What to Include in Weekly Snapshot

### **File Naming Convention:**
`YYYY-WXX-financial-snapshot.xlsx`

**Examples:**
- `2025-W41-financial-snapshot.xlsx` (Week 41 of 2025)
- `2025-W42-financial-snapshot.xlsx` (Week 42 of 2025)

**Or use date-based naming:**
`YYYY-MM-DD-weekly-snapshot.xlsx` (using Friday's date)

**Examples:**
- `2025-10-11-weekly-snapshot.xlsx`
- `2025-10-18-weekly-snapshot.xlsx`

---

## 📊 Weekly Snapshot Template

### **Tab 1: Summary Dashboard**

| Metric | This Week | Last Week | WoW Change | MTD | Target | Variance |
|--------|-----------|-----------|------------|-----|--------|----------|
| **Revenue** |
| Total Revenue | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| New Account Revenue | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| Existing Account Revenue | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| **Cash** |
| Cash Balance (EOW) | $[Amount] | $[Amount] | $[Change] | - | $[Min] | - |
| Cash Collected | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| **Accounts Receivable** |
| AR Balance | $[Amount] | $[Amount] | [%] | - | - | - |
| Days Sales Outstanding | [Days] | [Days] | [Days] | - | [Target] | [Days] |
| **Activity** |
| New Cases/Transactions | [#] | [#] | [#] | [#] | [Target] | [#] |
| Active Accounts | [#] | [#] | [#] | - | - | - |
| **Expenses** |
| Operating Expenses (WTD) | $[Amount] | $[Amount] | [%] | $[Amount] | $[Budget] | [%] |

**WoW = Week over Week**  
**MTD = Month to Date**  
**EOW = End of Week**  
**WTD = Week to Date**

---

### **Tab 2: Revenue Detail**

**Revenue by Account (Top 20)**

| Account Name | This Week | Last Week | Change | MTD | Anticipated MTD | Variance |
|--------------|-----------|-----------|--------|-----|-----------------|----------|
| ABC Law Firm | $[Amount] | $[Amount] | [%] | $[Amount] | $[Amount] | [%] |
| XYZ Healthcare | $[Amount] | $[Amount] | [%] | $[Amount] | $[Amount] | [%] |
| ... | ... | ... | ... | ... | ... | ... |

**Revenue by Service Type**

| Service Type | This Week | Last Week | Change | MTD | Target | Variance |
|--------------|-----------|-----------|--------|-----|--------|----------|
| Debt Collection | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| Legal Services | $[Amount] | $[Amount] | [%] | $[Amount] | $[Target] | [%] |
| ... | ... | ... | ... | ... | ... | ... |

---

### **Tab 3: Cash Flow**

**Weekly Cash Flow**

| Category | Amount | Notes |
|----------|--------|-------|
| **Beginning Cash Balance** | $[Amount] | As of [Date] |
| **Cash In** |
| Collections from Clients | $[Amount] | |
| New Account Deposits | $[Amount] | |
| Other Income | $[Amount] | |
| **Total Cash In** | $[Amount] | |
| **Cash Out** |
| Payroll | $[Amount] | |
| Operating Expenses | $[Amount] | |
| Technology/Software | $[Amount] | |
| Other Expenses | $[Amount] | |
| **Total Cash Out** | $[Amount] | |
| **Net Cash Flow** | $[Amount] | |
| **Ending Cash Balance** | $[Amount] | As of [Date] |

---

### **Tab 4: Key Metrics**

**Operational Metrics**

| Metric | This Week | Last Week | 4-Week Avg | Target |
|--------|-----------|-----------|------------|--------|
| New Cases Received | [#] | [#] | [#] | [#] |
| Cases Closed | [#] | [#] | [#] | [#] |
| Average Case Value | $[Amount] | $[Amount] | $[Amount] | $[Amount] |
| Collection Rate | [%] | [%] | [%] | [%] |
| Cost per Case | $[Amount] | $[Amount] | $[Amount] | $[Amount] |

**Account Health**

| Status | # Accounts | % of Total | Revenue This Week |
|--------|------------|------------|-------------------|
| 🟢 Exceeding Expectations | [#] | [%] | $[Amount] |
| 🟡 Meeting Expectations | [#] | [%] | $[Amount] |
| 🟠 Below Expectations | [#] | [%] | $[Amount] |
| 🔴 Significantly Below | [#] | [%] | $[Amount] |

---

### **Tab 5: Alerts & Notes**

**🔴 Critical Alerts**
- [Alert description and action needed]

**🟡 Items to Watch**
- [Item description and status]

**📝 Notes from CFO**
- [Key observations, explanations, context]

**🎯 Next Week Focus**
- [Priorities for coming week]

---

## 🎯 What I'll Generate From This

Once you provide weekly snapshots, I can automatically:

### **Weekly Analysis:**
- Week-over-week trend analysis
- Revenue velocity tracking
- Cash flow projections
- Account performance alerts
- Variance explanations

### **Monthly Rollup:**
- 4-week trends
- Month-to-date performance
- Forecast accuracy
- Pattern identification

### **Quarterly Insights:**
- 13-week trends
- Seasonality analysis
- Growth rate calculations
- Strategic recommendations

---

## 📋 Quick Start Options

### **Option 1: Full Weekly Snapshot (Recommended)**
Use the template above with all tabs
- **Time:** 30-45 minutes per week
- **Value:** Complete weekly visibility

### **Option 2: Simplified Weekly Snapshot**
Just the Summary Dashboard tab
- **Time:** 15-20 minutes per week
- **Value:** Key metrics tracking

### **Option 3: Revenue-Focused Snapshot**
Summary Dashboard + Revenue Detail tabs
- **Time:** 20-30 minutes per week
- **Value:** Revenue and account focus

---

## 📊 Simplified Template (Option 2)

If you want to start simple, just provide this each week:

### **Weekly Financial Snapshot - [Date]**

**Revenue:**
- Total Revenue This Week: $______
- Month-to-Date Revenue: $______
- Target MTD: $______

**Cash:**
- Cash Balance (End of Week): $______
- Cash Collected This Week: $______

**Top 5 Accounts This Week:**
1. [Account Name]: $______
2. [Account Name]: $______
3. [Account Name]: $______
4. [Account Name]: $______
5. [Account Name]: $______

**Alerts:**
- [Any issues or concerns]

**Notes:**
- [Key observations]

---

## 🔄 Update Process

### **Weekly Routine:**

**Every Friday (or Monday):**
1. Gather week's financial data
2. Fill in weekly snapshot template
3. Save as: `YYYY-WXX-financial-snapshot.xlsx`
4. Upload to `Finance/weekly-financials/`
5. Commit to GitHub
6. Notify CEO (or I'll auto-detect and analyze)

**Time Required:** 15-45 minutes (depending on detail level)

---

## 💡 Pro Tips

### **1. Consistency is Key**
- Update same day each week (Friday recommended)
- Use same format each time
- Don't skip weeks

### **2. Start Simple, Add Detail**
- Week 1: Just summary metrics
- Week 2-4: Add revenue detail
- Week 5+: Add full template

### **3. Automate Where Possible**
- Export from accounting system
- Use Excel formulas
- Copy/paste from reports

### **4. Focus on Trends**
- Week-over-week changes matter most
- Look for patterns
- Flag anomalies

---

## 📈 What Makes Weekly Data Valuable

### **Advantages Over Monthly-Only:**

✅ **Earlier Problem Detection**
- Spot issues in Week 1, not Week 4
- Take corrective action faster
- Prevent small problems from becoming big ones

✅ **Better Cash Flow Management**
- Weekly cash visibility
- Proactive cash management
- Avoid surprises

✅ **More Accurate Forecasting**
- 4 data points per month vs. 1
- Better trend identification
- More reliable projections

✅ **Faster Decision Making**
- Don't wait for month-end
- Real-time performance awareness
- Agile response to changes

---

## 📞 Getting Started

### **This Week:**

1. **Choose Your Approach:**
   - [ ] Full weekly snapshot (all tabs)
   - [ ] Simplified snapshot (summary only)
   - [ ] Revenue-focused snapshot

2. **Gather This Week's Data:**
   - [ ] Revenue (total and by account)
   - [ ] Cash balance
   - [ ] Key metrics

3. **Create First Snapshot:**
   - [ ] Use template above
   - [ ] Fill in current week's data
   - [ ] Save as `2025-W[XX]-financial-snapshot.xlsx`

4. **Upload to GitHub:**
   - [ ] Save to `Finance/weekly-financials/`
   - [ ] Commit and push
   - [ ] Let me know it's ready

---

## 📊 Sample File Names

```
Finance/weekly-financials/
├── 2025-W41-financial-snapshot.xlsx (Week ending Oct 11)
├── 2025-W42-financial-snapshot.xlsx (Week ending Oct 18)
├── 2025-W43-financial-snapshot.xlsx (Week ending Oct 25)
├── 2025-W44-financial-snapshot.xlsx (Week ending Nov 1)
└── 2025-W45-financial-snapshot.xlsx (Week ending Nov 8)
```

---

## ✅ Checklist for First Upload

- [ ] Decided on template approach (full/simplified/revenue-focused)
- [ ] Gathered this week's financial data
- [ ] Created weekly snapshot file
- [ ] Named file correctly (YYYY-WXX-financial-snapshot.xlsx)
- [ ] Saved to Finance/weekly-financials/
- [ ] Committed to GitHub
- [ ] Ready for analysis

---

## 🎯 What Happens Next

Once you upload your first weekly snapshot:

1. ✅ I'll analyze the data
2. ✅ Provide week-over-week insights (once you have 2+ weeks)
3. ✅ Identify trends and patterns
4. ✅ Flag any alerts or concerns
5. ✅ Include in your daily/weekly briefings
6. ✅ Track progress toward monthly targets

---

**Ready to start weekly financial tracking? Create your first snapshot and upload it!** 📊

**Questions about the template? Need help getting started? Just ask!** 🚀


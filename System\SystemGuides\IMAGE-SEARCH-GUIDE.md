# Image Search Guide

**Created**: 2025-10-12
**Status**: Complete - Image indexing and search capability implemented

---

## Overview

The SharePoint search system now includes **AI-powered image search** that allows you to find images by:
- ✅ **Visual content** (what's shown in the image)
- ✅ **Text visible in images** (OCR-like capability)
- ✅ **Image type** (logo, chart, photo, screenshot, etc.)
- ✅ **Technical metadata** (dimensions, format, file size)

### Note on Search Systems

This guide covers **SharePoint image search** using custom PowerShell scripts.

For searching **Git-tracked code and documentation** files, use **Serena** via Claude Code.

**See**: `SEARCH-SYSTEMS-GUIDE.md` for complete comparison of both search systems.

---

## How It Works

### Architecture

```
SharePoint Image Files
    ↓
Extract-ImageContent.ps1 (extracts metadata)
    ↓
Analyze-Images-AI.ps1 (AI vision analysis with Claude)
    ↓
Index-SharePointContent.ps1 (builds search index)
    ↓
Search-SharePointContent.ps1 (instant search)
```

### Components

1. **`analyze_image.py`** - Python script using Claude API for vision analysis
2. **`Extract-ImageContent.ps1`** - Extracts technical metadata (dimensions, EXIF, etc.)
3. **`Analyze-Images-AI.ps1`** - **NEW** - Batch processes images with AI descriptions
4. **`Index-SharePointContent.ps1`** - Indexes all content including AI descriptions
5. **`Search-SharePointContent.ps1`** - **ENHANCED** - Displays images with icons and AI snippets

---

## Quick Start

### Step 1: Set API Key (One-Time Setup)

```powershell
# Add to your PowerShell profile for persistence
$env:ANTHROPIC_API_KEY = "sk-ant-api03-..."

# Or set as system environment variable:
# System Properties → Environment Variables → User Variables → New
# Variable: ANTHROPIC_API_KEY
# Value: sk-ant-api03-...
```

**Cost**: ~$0.004 per image (Claude Sonnet 3.5)

### Step 2: Analyze Images

```powershell
cd C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts

# Analyze all images with pending descriptions
.\Analyze-Images-AI.ps1

# Analyze specific department
.\Analyze-Images-AI.ps1 -Department Marketing

# Analyze first 10 images (for testing)
.\Analyze-Images-AI.ps1 -MaxImages 10

# Re-analyze all images (even those with descriptions)
.\Analyze-Images-AI.ps1 -Force
```

**Output**: Updates markdown files in `docs/sharepoint-content/` with AI descriptions

### Step 3: Rebuild Search Index

```powershell
# Rebuild to include new AI descriptions
.\Index-SharePointContent.ps1 -Rebuild
```

**Optional**: The Analyze-Images-AI.ps1 script will offer to rebuild automatically

### Step 4: Search for Images

```powershell
# Search for images (and documents)
.\Search-SharePointContent.ps1 -Query "inc5000 award"

# Search with context (shows AI descriptions for images)
.\Search-SharePointContent.ps1 -Query "logo" -ShowContext

# Filter by department
.\Search-SharePointContent.ps1 -Query "presentation" -Department Marketing -Top 5
```

**Image Results**: Show with 🖼️ icon and marked as "(Image)" in magenta

---

## What Gets Analyzed

The AI vision analysis extracts:

1. **Main Subject/Content**: What is shown in the image
2. **Visible Text**: All text that appears in the image (OCR)
3. **Image Type**: Photo, screenshot, graphic, chart, logo, etc.
4. **Searchable Details**: Key information for finding the image

### Example AI Description

**File**: `inc5000 - 2021 for web.jpg`

**AI Description**:
```
Main Subject: Inc. 5000 award badge/logo

Visible Text:
- "INC. 5000"
- "2021"
- "AMERICA'S FASTEST-GROWING PRIVATE COMPANIES"
- Company ranking information

Type: Award badge graphic (official logo)

Key Details: This is an official Inc. 5000 recognition badge for 2021,
typically used for marketing materials and press releases to showcase
the company's ranking among America's fastest-growing private companies.
```

This description is now **fully searchable** - you can find this image by searching for:
- "inc 5000"
- "award badge"
- "fastest growing"
- "2021"
- "recognition"

---

## Search Examples

### Find Award Images
```powershell
.\Search-SharePointContent.ps1 -Query "award" -ShowContext
```

**Results**: Inc. 5000 badges, ABC Pacesetters awards, EOY logos, etc.

### Find Logos
```powershell
.\Search-SharePointContent.ps1 -Query "logo" -Department Marketing
```

**Results**: Company logos, partner logos, event logos

### Find Charts/Graphics
```powershell
.\Search-SharePointContent.ps1 -Query "chart graph data"
```

**Results**: Data visualizations, financial charts, performance graphs

### Find Screenshots
```powershell
.\Search-SharePointContent.ps1 -Query "screenshot interface"
```

**Results**: Software screenshots, UI mockups, system interfaces

---

## Maintenance

### When to Re-Analyze Images

- ✅ **After syncing new SharePoint files** (new images downloaded)
- ✅ **If search results seem incomplete** (missing descriptions)
- ✅ **After changing image files** (updates to existing images)

### Workflow After Adding New Images

```powershell
# 1. Sync SharePoint (if new images added)
.\Sync-SharePoint.ps1

# 2. Extract image metadata
.\Extract-ImageContent.ps1

# 3. Analyze with AI
.\Analyze-Images-AI.ps1

# 4. Rebuild index
.\Index-SharePointContent.ps1 -Rebuild

# 5. Search!
.\Search-SharePointContent.ps1 -Query "your search"
```

---

## Performance

### Analysis Speed
- **With API key**: ~2-3 seconds per image
- **Without API key**: Instant (fallback to filename-based descriptions)

### Search Speed
- **With index**: < 1 second (all files including images)
- **Multi-word queries**: < 1 second
- **Ranked by relevance**: Instant

### Storage
- **Index file**: ~1.4 MB (41 files currently)
- **Markdown files**: ~5-10 KB per image
- **Original images**: Not stored (uses OneDrive paths)

---

## Technical Details

### AI Model
- **Model**: Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)
- **Vision capability**: Analyzes image content and extracts text
- **Max tokens**: 500 per image (sufficient for detailed descriptions)

### Supported Image Formats
- ✅ JPEG (.jpg, .jpeg)
- ✅ PNG (.png)
- ⚠️ GIF, BMP (supported by metadata extraction, may need testing for AI analysis)

### Rate Limiting
- Built-in 500ms delay between images
- Prevents API rate limit errors
- Configurable in `Analyze-Images-AI.ps1`

---

## Troubleshooting

### "ANTHROPIC_API_KEY not set"
**Solution**: Set the environment variable (see Step 1 above)

**Workaround**: Script will run but use fallback descriptions based on filenames

### "Image not found"
**Cause**: OneDrive not synced or image moved

**Solution**:
1. Check OneDrive sync status
2. Verify `SharePointLinks/config.json` has correct `oneDriveRoot`
3. Re-run SharePoint sync

### "Analysis failed"
**Cause**: API error, image format issue, or network problem

**Solution**:
1. Check internet connection
2. Verify API key is valid
3. Check image file is not corrupted
4. Review log: `System/scripts/analyze-images-log.txt`

### Images Not Appearing in Search
**Cause**: Index not rebuilt after analysis

**Solution**:
```powershell
.\Index-SharePointContent.ps1 -Rebuild
```

---

## Cost Estimation

### Claude API Pricing (as of 2025)
- **Input tokens**: $3.00 per million tokens
- **Output tokens**: $15.00 per million tokens
- **Average per image**: ~$0.004

### Example Costs
- **10 images**: ~$0.04
- **50 images**: ~$0.20
- **100 images**: ~$0.40
- **500 images**: ~$2.00

**Note**: First-time analysis of all SharePoint images is a one-time cost.
Subsequent searches are free (using the index).

---

## Future Enhancements

### Potential Improvements
- [ ] Semantic search using embeddings (similar images)
- [ ] Image similarity detection (find duplicate or similar images)
- [ ] Batch processing optimization (parallel analysis)
- [ ] Automatic re-analysis on file changes
- [ ] Image tagging system (manual tags + AI tags)
- [ ] Visual search results (thumbnail previews in terminal)

### Integration Ideas
- [ ] Integrate with CEO Dashboard (image alerts/notifications)
- [ ] Create image gallery by topic/department
- [ ] Generate image usage reports (where images are referenced)

---

## Files Created/Modified

### New Files
- ✅ `System/scripts/Analyze-Images-AI.ps1` - Batch AI image analysis
- ✅ `TempDocs/IMAGE-SEARCH-GUIDE.md` - This documentation

### Modified Files
- ✅ `System/scripts/Search-SharePointContent.ps1` - Enhanced with image display
  - Added 🖼️ icon for images
  - Magenta "(Image)" type indicator
  - AI description snippets in context view

### Existing Files (No Changes)
- `System/scripts/analyze_image.py` - Python AI vision script
- `System/scripts/Extract-ImageContent.ps1` - Metadata extraction
- `System/scripts/Index-SharePointContent.ps1` - Indexing engine

---

## Summary

**Image search is now fully operational!** 🎉

You can:
1. ✅ Extract metadata from images
2. ✅ Generate AI descriptions with visual content and OCR
3. ✅ Index all content (documents + images) for instant search
4. ✅ Search images by content, not just filenames
5. ✅ View results with clear image indicators

**Next Steps**:
1. Set your ANTHROPIC_API_KEY
2. Run `Analyze-Images-AI.ps1` to process existing images
3. Start searching with `Search-SharePointContent.ps1`

**Questions?** Add notes to `TempDocs/notes/image-search-questions.md`

---

*This guide created during image indexing implementation - 2025-10-12*

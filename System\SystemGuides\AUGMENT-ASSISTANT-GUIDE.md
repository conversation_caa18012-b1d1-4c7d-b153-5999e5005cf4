# Augment AI Assistant Guide

**Purpose**: Quick reference for Augment users working with the ReidCEO system.

**TL;DR**: Read `CLAUDE.md` first - it contains all the main instructions. This guide just highlights the few unavoidable differences between Augment and Claude Code.

---

## Start Here

1. **Read `CLAUDE.md`** - This is your primary guide (applies to ALL AI assistants)
2. **Skim this guide** - Understand what's different for Augment (5 min read)
3. **Start working** - You're ready!

---

## Key Difference: MCPs (Model Context Protocol)

**What are MCPs?**
- MCPs are external tool servers that integrate with Claude Code
- They provide additional capabilities like semantic code search (Serena) and Confluence access
- Think of them like plugins or extensions

**Can Augment use MCPs?**
- ❌ **No** - MCPs are specific to Claude Code's architecture
- Augment has its own native tools instead (which are excellent!)

**What this means for you:**
- Skip any documentation mentioning "MCP", "Serena MCP", or "Confluence MCP"
- Use Augment's native `codebase-retrieval` tool instead (it's world-class!)

---

## What Works in Augment

### ✅ Everything Else Works Perfectly

**All PowerShell scripts** (32+ scripts):
- Email automation (4 scripts)
- SharePoint sync (9 scripts)
- Search systems (5 scripts)
- Setup scripts (4 scripts)
- Image processing (3 scripts)
- All others

**All documentation and templates**:
- Business context system
- Email templates
- Department folders
- CEO Dashboard

**SharePoint indexing** (critical!):
- AI can read `docs/sharepoint-content/search-index.json` directly
- AI can read `docs/sharepoint-content/*.md` (extracted content)
- AI can read `SharePointLinks/index.json` (file catalog)
- No need to run PowerShell scripts - just use `view` tool!

**File organization**:
- All CLAUDE.md rules apply
- Same folder structure
- Same file placement rules

---

## Augment's Strengths (Use These!)

### 1. `codebase-retrieval` Tool
**What it does**: World-class semantic search across the entire codebase

**When to use**:
- "Where is the email template system?"
- "How does SharePoint syncing work?"
- "What scripts handle KPI tracking?"
- "Show me all business context about products"

**Replaces**: Serena MCP's `find_symbol` and `search_for_pattern` tools

### 2. `view` Tool with Regex Search
**What it does**: Read files and search within them using regex patterns

**When to use**:
- Reading specific files
- Searching for patterns in files
- Finding all references to a symbol in a file

**Example**:
```
view path="System/scripts/New-Email.ps1" search_query_regex="param.*Template"
```

### 3. Parallel Tool Calls
**What it does**: Augment can call multiple tools simultaneously

**When to use**: Always! Read multiple files at once, search multiple locations in parallel

**Example**: Reading 3 files? Call `view` 3 times in parallel, not sequentially.

---

## What Doesn't Apply to Augment

### ❌ Skip These Guides
- `System/SystemGuides/SERENA-AI-ASSISTANT-GUIDE.md` - Claude Code only
- `System/SystemGuides/CONFLUENCE-MCP-SETUP-GUIDE.md` - Claude Code only
- Any mention of `.serena/project.yml` - Claude Code only

### ❌ Skip These Scripts
- `Setup-Serena-QuickStart.ps1` - Claude Code only
- `setup-serena.ps1` - Claude Code only
- `Setup-ConfluenceMCP.ps1` - Claude Code only

### ❌ Ignore These References
- "Serena MCP" - Claude Code enhancement
- "Confluence MCP" - Claude Code enhancement
- "MCP server" - Claude Code architecture
- "uvx" commands for Serena - Claude Code setup

---

## Quick Reference: Augment Equivalents

| Claude Code (MCP) | Augment (Native) | Purpose |
|-------------------|------------------|---------|
| Serena `find_symbol` | `codebase-retrieval` | Find code/docs by semantic search |
| Serena `search_for_pattern` | `view` with regex | Search within files |
| Serena `get_symbols_overview` | `view` + `codebase-retrieval` | Understand file structure |
| Confluence MCP | N/A | Access Confluence (not needed for ReidCEO) |

---

## Common Tasks - Augment Edition

### Task 1: Find Information About a Topic
```
Use codebase-retrieval:
"Where is the email template system documented?"
"How does SharePoint syncing work?"
"What business context exists about products?"
```

### Task 2: Search SharePoint Content
```
Method 1 (AI reads index directly):
view path="docs/sharepoint-content/search-index.json"
view path="docs/sharepoint-content/" type="directory"
view path="docs/sharepoint-content/Marketing_*.md"

Method 2 (User runs search script):
Guide user to run: .\Search-SharePointContent.ps1 -Query "customer experience"
```

### Task 3: Generate Email
```
Use email scripts (works perfectly in Augment):
launch-process with: .\Send-TemplatedEmail.ps1 -Template 'template-name' -Variables @{...}
```

### Task 4: Find All References to Something
```
Use codebase-retrieval first (broad search):
"Find all files mentioning KPI tracking"

Then use view with regex (specific file search):
view path="Finance/kpi-dashboard/kpi-alerts.md" search_query_regex="KPI.*alert"
```

---

## Critical Reminders (Same as CLAUDE.md)

### Before Creating ANY File:
1. Check `CLAUDE.md` for file placement rules
2. Use `codebase-retrieval` to find existing similar files
3. Check `System/SCRIPTS-AND-TOOLS-REGISTRY.md` for existing scripts
4. Ask: Does this belong in `TempDocs/` (scratch) or operational folders?

### Email Workflow (CRITICAL):
- ✅ **Always use email scripts**: `Send-TemplatedEmail.ps1` or `New-Email.ps1`
- ❌ **Never create standalone email drafts** in `TempDocs/`
- Scripts create drafts in Outlook (safe for user review)

### File Placement Quick Reference:
```
✅ TempDocs/                    # Scratch workspace (gitignored)
✅ System/SystemGuides/         # Official documentation (committed)
✅ System/scripts/              # Automation scripts (committed)
✅ Department folders           # CEO's workspace (ask first!)
❌ Root directory               # ONLY CLAUDE.md + SETUP-SHAREPOINT.md allowed
```

---

## When You Need Help

**If you're unsure about something:**
1. Check `CLAUDE.md` first (comprehensive guide)
2. Use `codebase-retrieval` to find relevant documentation
3. Check `System/SCRIPTS-AND-TOOLS-REGISTRY.md` for script inventory
4. Ask the user for clarification

**If you notice yourself:**
- Going in circles
- Calling the same tool repeatedly
- Unable to find information
→ **Ask the user for help!**

---

## Summary

**Bottom line for Augment users:**
- ✅ Read `CLAUDE.md` - it's your main guide
- ✅ Use `codebase-retrieval` extensively (it's excellent!)
- ✅ All PowerShell scripts work perfectly
- ✅ SharePoint indexing is AI-readable (use `view` tool)
- ❌ Skip MCP-related documentation (Claude Code only)
- ❌ Can't use Serena or Confluence MCPs (use native tools instead)

**You have everything you need!** Augment's native tools are powerful and work great with this project. The only difference is MCPs, which are optional enhancements for Claude Code users.

---

## Version History

**Version 1.0** (2025-10-29)
- Initial minimal guide for Augment users
- Focused on unavoidable differences (MCPs)
- Points to CLAUDE.md as primary source of truth
- Highlights Augment's native strengths


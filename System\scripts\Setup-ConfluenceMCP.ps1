<#
.SYNOPSIS
    Setup Confluence MCP server for Claude Code integration

.DESCRIPTION
    Interactive setup wizard that:
    1. Collects Confluence credentials (or uses team token)
    2. Creates credentials file (gitignored)
    3. Updates MCP server configuration
    4. Tests the connection
    5. Verifies everything works

.PARAMETER UseTeamToken
    Use existing team credentials from confluence-credentials.json

.EXAMPLE
    .\Setup-ConfluenceMCP.ps1
    Interactive setup with prompts

.EXAMPLE
    .\Setup-ConfluenceMCP.ps1 -UseTeamToken
    Use existing team credentials

.NOTES
    Author: ReidCEO Project Team
    Date: 2025-10-20
    Version: 1.0
#>

[CmdletBinding()]
param(
    [switch]$UseTeamToken
)

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "  CONFLUENCE MCP SETUP - CLAUDE CODE INTEGRATION" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Paths
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$CredentialsFile = Join-Path $PSScriptRoot "confluence-credentials.json"
$McpConfigFile = Join-Path $ProjectRoot "serena-mcp-config.json"

# Step 1: Check if credentials already exist
if ($UseTeamToken -or (Test-Path $CredentialsFile)) {
    if (Test-Path $CredentialsFile) {
        Write-Host "[1/5] Existing credentials found" -ForegroundColor Green
        Write-Host ""
        $existingCreds = Get-Content $CredentialsFile -Raw -Encoding UTF8 | ConvertFrom-Json

        Write-Host "Current configuration:" -ForegroundColor Yellow
        Write-Host "  URL:   $($existingCreds.confluence_url)" -ForegroundColor White
        Write-Host "  Email: $($existingCreds.confluence_email)" -ForegroundColor White
        Write-Host "  Token: $($existingCreds.confluence_api_token.Substring(0, 20))..." -ForegroundColor White
        Write-Host ""

        $response = Read-Host "Use these credentials? (Y/n)"
        if ($response -ne "n" -and $response -ne "N") {
            $confluenceUrl = $existingCreds.confluence_url
            $confluenceEmail = $existingCreds.confluence_email
            $confluenceToken = $existingCreds.confluence_api_token
            Write-Host "Using existing credentials" -ForegroundColor Green
            Write-Host ""
        } else {
            $useExisting = $false
        }
    }
}

# Step 1 (continued): Get new credentials if needed
if (-not $confluenceUrl) {
    Write-Host "[1/5] Collecting Confluence Credentials" -ForegroundColor Yellow
    Write-Host ""

    Write-Host "Where to get an API token:" -ForegroundColor Cyan
    Write-Host "  1. Ask IT or Jonathan for the team token (recommended)" -ForegroundColor Gray
    Write-Host "  2. Generate your own: https://id.atlassian.com/manage-profile/security/api-tokens" -ForegroundColor Gray
    Write-Host ""

    $confluenceUrl = Read-Host "Confluence URL (press Enter for default: https://gainservicing.atlassian.net)"
    if ([string]::IsNullOrWhiteSpace($confluenceUrl)) {
        $confluenceUrl = "https://gainservicing.atlassian.net"
    }

    $confluenceEmail = Read-Host "Your Confluence email"
    if ([string]::IsNullOrWhiteSpace($confluenceEmail)) {
        Write-Host "Error: Email is required" -ForegroundColor Red
        exit 1
    }

    $confluenceToken = Read-Host "Confluence API Token" -AsSecureString
    $confluenceTokenPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto(
        [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($confluenceToken)
    )
    if ([string]::IsNullOrWhiteSpace($confluenceTokenPlain)) {
        Write-Host "Error: API Token is required" -ForegroundColor Red
        exit 1
    }
    $confluenceToken = $confluenceTokenPlain

    Write-Host ""
    Write-Host "Credentials collected successfully" -ForegroundColor Green
    Write-Host ""
}

# Step 2: Create credentials file
Write-Host "[2/5] Creating credentials file..." -ForegroundColor Yellow
Write-Host ""

$credentials = @{
    confluence_url = $confluenceUrl
    confluence_email = $confluenceEmail
    confluence_api_token = $confluenceToken
}

try {
    $credentials | ConvertTo-Json -Depth 10 | Set-Content -Path $CredentialsFile -Encoding UTF8
    Write-Host "Created: $CredentialsFile" -ForegroundColor Green
    Write-Host "(This file is gitignored - safe from commits)" -ForegroundColor Gray
    Write-Host ""
} catch {
    Write-Host "Error creating credentials file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Update MCP configuration
Write-Host "[3/5] Updating MCP server configuration..." -ForegroundColor Yellow
Write-Host ""

try {
    if (Test-Path $McpConfigFile) {
        $mcpConfig = Get-Content $McpConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json
    } else {
        $mcpConfig = @{
            mcpServers = @{}
        }
    }

    # Add or update Confluence MCP server
    $mcpConfig.mcpServers | Add-Member -NotePropertyName "confluence" -NotePropertyValue @{
        command = "python"
        args = @(
            "-m",
            "mcp_atlassian.confluence",
            "--url",
            $confluenceUrl,
            "--email",
            $confluenceEmail,
            "--token",
            $confluenceToken
        )
    } -Force

    $mcpConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $McpConfigFile -Encoding UTF8
    Write-Host "Updated: $McpConfigFile" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "Error updating MCP config: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Test connection
Write-Host "[4/5] Testing Confluence connection..." -ForegroundColor Yellow
Write-Host ""

try {
    $testScript = @"
from atlassian import Confluence
import json

creds = json.load(open('$($CredentialsFile.Replace('\', '/'))'))
c = Confluence(
    url=creds['confluence_url'],
    username=creds['confluence_email'],
    password=creds['confluence_api_token']
)

spaces = c.get_all_spaces(limit=10)
print(f"Connected! Found {len(spaces['results'])} spaces:")
for s in spaces['results']:
    print(f"  - {s['name']}")
"@

    $testResult = python -c $testScript 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host $testResult -ForegroundColor Green
        Write-Host ""
    } else {
        throw "Connection test failed: $testResult"
    }
} catch {
    Write-Host "Error testing connection: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "  1. Verify your API token is correct" -ForegroundColor Gray
    Write-Host "  2. Check Confluence status: https://status.atlassian.com/" -ForegroundColor Gray
    Write-Host "  3. Ensure you have access to $confluenceUrl" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

# Step 5: Verify installation
Write-Host "[5/5] Verifying MCP module installation..." -ForegroundColor Yellow
Write-Host ""

try {
    $verifyScript = "import mcp_atlassian.confluence; print('SUCCESS: MCP module is ready!')"
    $verifyResult = python -c $verifyScript 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host $verifyResult -ForegroundColor Green
        Write-Host ""
    } else {
        throw "Module verification failed: $verifyResult"
    }
} catch {
    Write-Host "Error verifying installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "To fix, run:" -ForegroundColor Yellow
    Write-Host "  pip install mcp-atlassian" -ForegroundColor Gray
    Write-Host "  pip install pydantic==2.11.0" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

# Success summary
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "  SETUP COMPLETE!" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Credentials: $CredentialsFile" -ForegroundColor White
Write-Host "  MCP Config:  $McpConfigFile" -ForegroundColor White
Write-Host ""

Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "  1. Restart Claude Code (if running)" -ForegroundColor White
Write-Host "  2. Ask Claude to search Confluence:" -ForegroundColor White
Write-Host "     'Search Confluence for onboarding procedures'" -ForegroundColor Gray
Write-Host "     'List all Confluence spaces'" -ForegroundColor Gray
Write-Host "     'Find pages about database backups'" -ForegroundColor Gray
Write-Host ""

Write-Host "Documentation:" -ForegroundColor Yellow
Write-Host "  Setup Guide: System\SystemGuides\CONFLUENCE-MCP-SETUP-GUIDE.md" -ForegroundColor White
Write-Host ""

Write-Host "Team Token Sharing:" -ForegroundColor Yellow
Write-Host "  This API token can be shared with other team members." -ForegroundColor White
Write-Host "  They can copy your confluence-credentials.json file or" -ForegroundColor White
Write-Host "  run this setup script with the same credentials." -ForegroundColor White
Write-Host ""

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Serena AI Assistant Setup Guide

**Serena** is an AI-powered coding assistant that provides advanced code navigation, semantic search, and intelligent code understanding for the ReidCEO system.

---

## What is <PERSON>?

<PERSON> adds powerful code intelligence to Claude Code, enabling:
- **Semantic code search** - Find functions, classes, and symbols by name or pattern
- **Reference finding** - Discover where code is used throughout the project
- **Symbol navigation** - Jump to definitions and understand relationships
- **Code overview** - Get high-level summaries of files and modules
- **Smart editing** - Make precise code changes using symbol-based operations

Perfect for a documentation-heavy system like ReidCEO where you need to find, navigate, and update markdown files and Python scripts efficiently.

---

## Prerequisites

Before setting up Serena, ensure you have:

✅ **Python 3.11+** installed
✅ **Claude Code** installed and running
✅ **Git for Windows** installed (includes git-bash)

---

## One-Step Installation (Easiest)

### Option 1: Using Claude Code CLI (Recommended)

1. **Open PowerShell** in the ReidCEO directory:
   ```powershell
   cd "C:\Users\<USER>\Development\Gain\ReidCEO"
   ```

2. **Set git-bash path** (one-time setup):
   ```powershell
   $env:CLAUDE_CODE_GIT_BASH_PATH = "C:\Dev\Git\usr\bin\bash.exe"
   [Environment]::SetEnvironmentVariable('CLAUDE_CODE_GIT_BASH_PATH', 'C:\Dev\Git\usr\bin\bash.exe', 'User')
   ```

   *(Adjust the path if Git is installed elsewhere)*

3. **Add Serena to Claude Code**:
   ```powershell
   claude mcp add serena-ReidCEO -- uvx --from git+https://github.com/oraios/serena serena start-mcp-server --context ide-assistant --project "$(pwd)"
   ```

4. **Restart Claude Code** to activate Serena

5. **Verify installation** - Open Claude Code and check for Serena tools:
   - `find_symbol`
   - `search_for_pattern`
   - `get_symbols_overview`

---

### Option 2: Using Setup Script (Alternative)

If the CLI method doesn't work, use the automated setup script:

1. **Open PowerShell as Administrator**

2. **Navigate to the scripts directory**:
   ```powershell
   cd "C:\Users\<USER>\Development\Gain\ReidCEO\System\scripts"
   ```

3. **Run the setup script**:
   ```powershell
   .\setup-serena.ps1
   ```

4. **Follow the prompts** - The script will:
   - Check prerequisites (Python 3.11+, uv, Serena)
   - Generate `.serena/project.yml` configuration
   - Index the project for semantic search
   - Test the MCP server
   - Create configuration files

5. **Restart Claude Code**

---

## Verification

Once installed and Claude Code is restarted:

1. **Check for Serena tools** in Claude Code:
   - Ask Claude: "List your available tools"
   - Look for tools starting with `mcp__serena-ReidCEO__`

2. **Test basic functionality**:
   - Ask Claude: "Use Serena to find all markdown files in the CEO-Dashboard"
   - Try: "Show me the symbols overview of CLAUDE.md"

3. **Check Serena dashboard** (optional):
   - Open: `http://localhost:24282/dashboard/index.html`
   - Only accessible when Claude Code is running with Serena active

---

## How Serena Helps with ReidCEO

### Finding Documentation
```
"Find all files mentioning 'KPI dashboard'"
"Show me where daily-briefing.md is referenced"
"List all template files in the Finance folder"
```

### Understanding Structure
```
"Give me a symbols overview of the CEO-Dashboard folder"
"Show me all sections in CLAUDE.md"
"Find all files importing the SharePoint sync script"
```

### Smart Editing
```
"Update the version number in CLAUDE.md using Serena"
"Add a new section to the Finance KPI tracker"
"Rename the symbol 'old_name' to 'new_name' across all files"
```

---

## Troubleshooting

### "Serena tools not showing up"
- **Cause**: Claude Code not restarted
- **Fix**: Fully close and restart Claude Code

### "Python 3.11+ required"
- **Cause**: Older Python version installed
- **Fix**: Install Python 3.11+ from python.org

### "uv not found"
- **Cause**: `uv` package manager not installed
- **Fix**: Run `curl.exe -LsSf https://astral.sh/uv/install.sh | bash`

### "MCP server failed to start"
- **Cause**: Project path incorrect or permissions issue
- **Fix**: Verify project path in `.serena/project.yml`

### "Git-bash not found"
- **Cause**: Git not installed or CLAUDE_CODE_GIT_BASH_PATH not set
- **Fix**: Install Git for Windows, then set environment variable

---

## Technical Details

### Files Created
- `.serena/project.yml` - Serena project configuration (gitignored cache/logs/memories)
- `.serena/.gitignore` - Prevents committing cache and indices

### Configuration

**MCP Integration**:
Serena uses the MCP (Model Context Protocol) to integrate with Claude Code:
- **Server name**: `serena-ReidCEO`
- **Command**: `uvx --from git+https://github.com/oraios/serena serena start-mcp-server`
- **Context**: `ide-assistant` (optimized for code editing)
- **Project**: Points to ReidCEO directory

**Important Config Setting** (`.serena/project.yml`):
```yaml
# Allow Serena to index docs/sharepoint-content/ even though it's gitignored
ignore_all_files_in_gitignore: false

# Manually specify what to ignore (mirrors .gitignore except SharePoint content)
ignored_paths:
  - "TempDocs/**"
  - "SharePointDocs/**"
  - "*PRIVATE*.md"
  # ... etc (see project.yml for full list)
```

**Why**: SharePoint content is gitignored (user-specific, changes frequently) but we still want Serena to index it for structure navigation.

### What Gets Indexed
Serena indexes:
- Python files (`.py`)
- Markdown files (`.md`)
- Configuration files (`.json`, `.yml`)
- Git-tracked files (respects `.gitignore`)

**Not indexed** (explicitly ignored in Serena config):
- `TempDocs/` - Scratch workspace
- `SharePointDocs/` - Old SharePoint sync approach (deprecated)
- `.serena/cache/`, `.serena/logs/`, `.serena/memories/` - Serena internals
- `*PRIVATE*.md`, `*PERSONAL*.md`, `*SCRATCH*.md` - Personal notes

**Indexed (even though gitignored)**:
- `docs/sharepoint-content/` - Extracted SharePoint documents (54 files)
  - Gitignored because it's user-specific and changes frequently
  - Serena configured to index it anyway (see config below)

### Serena vs SharePoint Search

**Important**: Serena searches **Git-tracked ReidCEO files only** (CEO-Dashboard, System, templates, etc.)

For **SharePoint documents** (Marketing presentations, Finance files, Legal docs), use the **SharePoint Search scripts**:
```powershell
cd System/scripts
.\Search-SharePointContent.ps1 -Query "your query"
.\Search-SemanticContent.ps1 -Query "conceptual query"
```

**Why two systems?**
- **Serena**: Searches Git repository (~50 files) - Code navigation and editing
- **SharePoint Search**: Searches extracted SharePoint documents (~284 files) - Document discovery

**See**: `SEARCH-SYSTEMS-GUIDE.md` for complete comparison and when to use each.

---

## Advanced Usage

### Reindexing the Project
If you add many new files or make significant changes:
```powershell
cd System/scripts
uvx --from git+https://github.com/oraios/serena serena project index "C:\Users\<USER>\Development\Gain\ReidCEO"
```

### Checking Serena Status
```powershell
uvx --from git+https://github.com/oraios/serena serena --version
```

### Viewing Dashboard
When Claude Code is running with Serena:
```
http://localhost:24282/dashboard/index.html
```

---

## Uninstalling Serena

If you need to remove Serena:

1. **Remove from Claude Code**:
   ```powershell
   claude mcp remove serena-ReidCEO
   ```

2. **Delete Serena configuration** (optional):
   ```powershell
   rm -r .serena
   ```

3. **Restart Claude Code**

---

## Resources

- **Serena GitHub**: https://github.com/oraios/serena
- **MCP Documentation**: https://modelcontextprotocol.io/
- **Claude Code Docs**: https://docs.claude.com/claude-code

---

## Summary

**Why Serena?** - Makes navigating and editing the ReidCEO documentation system faster and more intelligent.

**Installation**: One command with Claude CLI, or run the setup script.

**Usage**: Ask Claude to use Serena tools for finding, understanding, and editing code/docs.

**Maintenance**: Mostly automatic - reindex occasionally if needed.

---

*Last updated: 2025-10-18*
*Version: 1.0*

# Send-TemplatedEmail.ps1
# Process email templates and send using New-Email.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$Template,  # Template name (without .txt extension)

    [Parameter(Mandatory=$true)]
    [hashtable]$Variables,  # Hashtable of variable replacements

    [string]$TemplatePath = "$PSScriptRoot\..\EmailTemplates",
    [switch]$Send,  # Pass through to New-Email.ps1
    [switch]$SaveToDrafts,
    [switch]$Silent
)

<#
.SYNOPSIS
Send email using template with variable substitution

.DESCRIPTION
Loads email template, replaces variables, and sends using New-Email.ps1

.EXAMPLE
.\Send-TemplatedEmail.ps1 -Template "action-item-assigned" -Variables @{
    assignee_name = "<PERSON>"
    assignee_email = "<EMAIL>"
    task_title = "Review Q4 Budget"
    priority = "High"
    deadline = "Friday"
}

.NOTES
Requires: New-Email.ps1 in same directory
Templates: System/EmailTemplates/
#>

function Write-Log {
    param($Message, $Level = "INFO")
    if (-not $Silent) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

try {
    # Resolve template path
    $templateFile = Join-Path $TemplatePath "$Template.txt"

    if (-not (Test-Path $templateFile)) {
        Write-Log "ERROR: Template not found: $templateFile" "ERROR"
        Write-Log "Available templates in $TemplatePath`:" "ERROR"
        Get-ChildItem $TemplatePath -Filter "*.txt" | ForEach-Object {
            Write-Log "  - $($_.BaseName)" "ERROR"
        }
        exit 1
    }

    Write-Log "Loading template: $Template"

    # Read template
    $templateContent = Get-Content $templateFile -Raw

    # Add automatic variables
    $Variables["date"] = Get-Date -Format "MMMM d, yyyy"
    $Variables["datetime"] = Get-Date -Format "MMMM d, yyyy h:mm tt"
    $Variables["company"] = "Gain Servicing"
    $Variables["sender_name"] = $env:USERNAME  # Can be overridden

    # Replace variables in template
    $processedContent = $templateContent
    foreach ($key in $Variables.Keys) {
        $placeholder = "{{$key}}"
        $value = $Variables[$key]
        $processedContent = $processedContent -replace [regex]::Escape($placeholder), $value
    }

    # Check for unresolved variables
    $unresolvedVars = [regex]::Matches($processedContent, '{{([^}]+)}}') | ForEach-Object { $_.Groups[1].Value }
    if ($unresolvedVars.Count -gt 0) {
        Write-Log "WARNING: Unresolved variables found:" "WARN"
        foreach ($var in $unresolvedVars) {
            Write-Log "  - {{$var}}" "WARN"
        }
        Write-Log "These will appear as-is in the email" "WARN"
    }

    # Parse template sections
    $sections = $processedContent -split "---", 2
    if ($sections.Count -ne 2) {
        Write-Log "ERROR: Template format invalid. Expected HEADERS---BODY format" "ERROR"
        exit 1
    }

    $headers = $sections[0].Trim()
    $body = $sections[1].Trim()

    # Parse headers
    $headerLines = $headers -split "`n"
    $to = ""
    $cc = ""
    $subject = ""
    $format = "Text"  # Default to plain text

    foreach ($line in $headerLines) {
        if ($line -match "^SUBJECT:\s*(.+)$") {
            $subject = $matches[1].Trim()
        }
        elseif ($line -match "^TO:\s*(.+)$") {
            $to = $matches[1].Trim()
        }
        elseif ($line -match "^CC:\s*(.+)$") {
            $cc = $matches[1].Trim()
        }
        elseif ($line -match "^FORMAT:\s*(.+)$") {
            $format = $matches[1].Trim()
        }
    }

    # Validate required fields
    if (-not $to) {
        Write-Log "ERROR: TO field not found in template or is empty" "ERROR"
        exit 1
    }

    if (-not $subject) {
        Write-Log "ERROR: SUBJECT field not found in template or is empty" "ERROR"
        exit 1
    }

    # Show preview
    if (-not $Silent) {
        Write-Host ""
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host "Email Preview (from template: $Template)" -ForegroundColor Cyan
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host "To: $to" -ForegroundColor White
        if ($cc) { Write-Host "CC: $cc" -ForegroundColor White }
        Write-Host "Subject: $subject" -ForegroundColor White
        Write-Host ""
        Write-Host "Body:" -ForegroundColor Yellow
        Write-Host $body.Substring(0, [Math]::Min(500, $body.Length)) -ForegroundColor Gray
        if ($body.Length -gt 500) {
            Write-Host "... (truncated for preview)" -ForegroundColor Gray
        }
        Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan
        Write-Host ""
    }

    # Build New-Email.ps1 parameters
    $emailParams = @{
        To = $to
        Subject = $subject
        Body = $body
    }

    if ($cc) { $emailParams["Cc"] = $cc }

    # Set body format if HTML
    if ($format -eq "HTML") {
        $emailParams["BodyFormat"] = "HTML"
    }

    # New-Email.ps1 creates drafts by default, only send if -Send specified
    if ($Send) { $emailParams["Send"] = $true }

    # Call New-Email.ps1
    $sendEmailScript = Join-Path $PSScriptRoot "New-Email.ps1"
    if (-not (Test-Path $sendEmailScript)) {
        Write-Log "ERROR: New-Email.ps1 not found at: $sendEmailScript" "ERROR"
        exit 1
    }

    Write-Log "Calling New-Email.ps1..."
    & $sendEmailScript @emailParams

    Write-Log "Templated email processed successfully" "SUCCESS"
}
catch {
    Write-Log "ERROR: Failed to process template" "ERROR"
    Write-Log "Error details: $($_.Exception.Message)" "ERROR"
    exit 1
}
